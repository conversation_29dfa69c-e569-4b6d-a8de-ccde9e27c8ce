ARG PLATFORM=${BUILDPLATFORM}

# Build
FROM node:22.12.0-alpine3.21 AS build

ARG VARIANT=production
ENV NPM_CONFIG_CACHE=/build/.npm

COPY package.json package-lock.json /build/

WORKDIR /build

RUN --mount=type=cache,target=/build/.npm npm ci

COPY src /build/src/
COPY angular.json tsconfig.* /build/

RUN --mount=type=cache,target=/build/.npm,ro npm run build-${VARIANT}

# Package
FROM --platform=${PLATFORM} nginx:1.27.3-alpine3.20

COPY deployment/nginx/nginx.conf /etc/nginx/

COPY --from=build /build/dist/front-end/browser/ /var/www/

EXPOSE 4200

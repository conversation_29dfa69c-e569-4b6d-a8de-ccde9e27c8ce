import {AfterViewInit, Component, inject, <PERSON><PERSON><PERSON>roy, ViewChild} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';
import {Cancellation} from '../data/cancellation';
import {FormatCustomerPipe} from '../pipes/format-customer.pipe';
import {DEBOUNCE_TIME, rangesToString, today} from '../utility/utility';
import {formatGGC} from '../utility/reservation-utility';
import {CustomerGroup} from '../data/customers/customer-group';
import {
   BalanceDialogComponent,
   BalanceDialogData,
   FinancialContext
} from '../dialogs/balance-dialog/balance-dialog.component';
import {MatDialog} from '@angular/material/dialog';
import {fullScreenDialogOptions} from '../utility/dialog-utility';
import {MatPaginator} from '@angular/material/paginator';
import {CustomerGroupService} from '../services/customer-group.service';
import {CancellationService} from '../services/cancellation.service';
import {debounceTime, filter, Subject, switchMap} from 'rxjs';
import {FormControl} from '@angular/forms';
import {fullName, matchCustomerProperty} from '../data/customers/customer';
import {FinancialAccountService} from '../services/financial-account.service';

const startSort = (a: Cancellation, b: Cancellation) => a.start.valueOf() -
   b.start.valueOf();

@Component({
   selector: 'app-cancellations',
   templateUrl: './cancellations.component.html',
   styleUrl: './cancellations.component.scss',
   standalone: false
})
export class CancellationsComponent implements AfterViewInit, OnDestroy {
   @ViewChild(MatPaginator) paginator!: MatPaginator;

   dataSource = new MatTableDataSource<Cancellation>();
   columns = ['name', 'source', 'bundle', 'dates', 'guests', 'actions'];
   startDate = today().minus({days: 15});
   endDate = today().plus({days: 15});
   filterInput = new FormControl();

   getName = (cancellation: Cancellation) => FormatCustomerPipe.toString(
      cancellation.titular);
   getSource = (cancellation: Cancellation) => cancellation.source.name;
   getBundle = (cancellation: Cancellation) => cancellation.bundle.name;
   getDates = (cancellation: Cancellation) => rangesToString([cancellation]);
   getGuests = (cancellation: Cancellation) => formatGGC(cancellation.guestGroupCount,
      this.groups);

   private groups: CustomerGroup[] = [];
   private dialog = inject(MatDialog);
   private sCancellation = inject(CancellationService);
   private sFinancialAccount = inject(FinancialAccountService);
   private dateChangeSubject = new Subject<void>();
   private updateDatesSubscription = this.dateChangeSubject.pipe(
      debounceTime(DEBOUNCE_TIME),
      filter(() => !!this.startDate && !!this.endDate),
   ).subscribe(() => this.renewData());

   constructor() {
      inject(CustomerGroupService).getAll().subscribe(gs => this.groups = gs);
   }

   ngAfterViewInit(): void {
      this.dataSource.paginator = this.paginator;
      this.dataSource.filterPredicate =
         (c: Cancellation, fltr) => matchCustomerProperty(c.titular, fltr);
      this.filterInput.valueChanges.subscribe(
         fltr => this.dataSource.filter = fltr.toLowerCase());
      this.renewData();
   }

   ngOnDestroy() {
      this.updateDatesSubscription.unsubscribe();
   }

   onDatesInputChange() {
      this.dateChangeSubject.next();
   }

   openAccount(cancellation: Cancellation): void {
      this.sFinancialAccount.getChain(cancellation.id).pipe(
         switchMap(accountChain => {
            const data: BalanceDialogData = {
               accountChain,
               movementAllowed: true,
               context: FinancialContext.cancellation,
               subtitle: `Анулирана резервация на ${fullName(cancellation.titular)}`
            };

            return this.dialog.open(BalanceDialogComponent, {
               data,
               ...fullScreenDialogOptions,
            }).afterClosed();
         })
      ).subscribe(() => this.renewData());
   }

   private renewData(): void {
      this.sCancellation.getInRange(this.startDate, this.endDate).subscribe(
         cancellations => this.dataSource.data = cancellations.sort(startSort));
   }
}

<div class="main-view-header full-width">
   <mat-form-field class="search filter">
      <mat-label>Титуляр</mat-label>
      <input [formControl]="filterInput" autocomplete="off" matInput
             placeholder="Име, телефон, имейл или ЕГН/БУЛСТАТ">
      <mat-icon matSuffix>search</mat-icon>
   </mat-form-field>

   <mat-form-field class="filter">
      <mat-label>Период</mat-label>
      <mat-date-range-input [rangePicker]="rangePicker">
         <input (ngModelChange)="onDatesInputChange()" [(ngModel)]="startDate"
                matStartDate placeholder="Начало">
         <input (ngModelChange)="onDatesInputChange()" [(ngModel)]="endDate" matEndDate
                placeholder="Край">
      </mat-date-range-input>
      <mat-datepicker-toggle [for]="rangePicker" matSuffix/>
      <mat-date-range-picker #rangePicker/>
   </mat-form-field>
</div>
<div class="main-view-table mat-elevation-z5" style="width: 90vw;">
   <table [dataSource]="dataSource" mat-table>
      <mat-text-column [dataAccessor]="getName" headerText="Име" name="name"/>
      <mat-text-column [dataAccessor]="getSource" headerText="Канал" name="source"/>
      <mat-text-column [dataAccessor]="getBundle" headerText="Пакет" name="bundle"/>
      <mat-text-column [dataAccessor]="getDates" headerText="Период" name="dates"/>
      <mat-text-column [dataAccessor]="getGuests" headerText="Гости" name="guests"/>

      <ng-container matColumnDef="actions">
         <th *matHeaderCellDef mat-header-cell></th>
         <td *matCellDef="let cancellation" mat-cell>
            <button (click)="openAccount(cancellation)" mat-icon-button>
               <mat-icon>attach_money</mat-icon>
            </button>
         </td>
      </ng-container>

      <tr *matHeaderRowDef="columns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: columns;" mat-row></tr>

      <tr *matNoDataRow>
         <td [colSpan]="columns.length" class="text-center">Няма анулации за периода</td>
      </tr>
   </table>

   <mat-paginator [pageSizeOptions]="[20, 50, 100]" showFirstLastButtons/>
</div>

import {Component, inject, OnInit} from '@angular/core';

import {AuthService} from './auth/auth.service';
import {CachingService} from './services/caching.service';
import {VersioningService} from './services/versioning.service';

@Component({
   selector: 'app-root',
   template: `
      @if (sAuth.loggedIn()) {
         <app-navigation/>
      }
      <router-outlet></router-outlet>
   `,
   styles: [`
      ::ng-deep .mat-mdc-dialog-content {
         max-height: 80vh !important;
      }
   `],
   standalone: false
})
export class AppComponent implements OnInit {
   sAuth = inject(AuthService);

   private sCaching = inject(CachingService);
   private sVersioning = inject(VersioningService);

   ngOnInit(): void {
      if (!this.sAuth.loggedIn()) {
         this.sAuth.refreshToken().subscribe(() => this.sCaching.init());
      } else {
         this.sCaching.init();
      }

      this.sVersioning.init();
   }
}

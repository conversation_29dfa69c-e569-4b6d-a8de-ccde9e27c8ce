import {Currency, Money} from '../data/common';

export const subMoney = (m1: Money, m2: Money): Money =>
   ({amount: m1.amount - m2.amount, currency: m1.currency});

export const addMoney = (m1: Money, m2: Money): Money =>
   ({amount: m1.amount + m2.amount, currency: m1.currency});

export const sumMoney = (moneyArray: (Money | null | undefined)[]): Money => {
   const result: Money = {amount: 0, currency: Currency.bgn};

   for (const money of moneyArray) {
      if (money && money.amount) {
         result.amount += money.amount;
      }
   }

   return result;
};

export const validMoney = (m?: Money): boolean =>
   !!m && (!!m.amount || m.amount === 0) && !!m.currency;

import {MatDialogRef} from '@angular/material/dialog';
import {Observer} from 'rxjs';
import {NotificationService} from '../services/notification.service';

export const fullScreenDialogOptions = {
   closeOnNavigation: true,
   maxWidth: '100vw',
   maxHeight: '100vh',
   width: '100vw',
   height: '100vh',
};

export const maxHeightDialogOptions = {
   closeOnNavigation: true,
   maxHeight: '100vh',
   height: '100vh',
};

export const dialogCloseObserver = (dialog: MatDialogRef<any>,
                                    sNotification: NotificationService): Observer<any> => ({
   next: () => dialog.close(true),
   error: (err) => {
      sNotification.displayError(err);
      dialog.close(false);
   },
   complete: () => dialog.close(true),
});

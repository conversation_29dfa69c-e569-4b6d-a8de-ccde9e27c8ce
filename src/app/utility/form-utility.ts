import {Currency} from '../data/common';
import {FormControl, FormGroup, UntypedFormBuilder, Validators} from '@angular/forms';

export const moneyFormGroup = (fb: UntypedFormBuilder) => fb.group({
   amount: '',
   currency: Currency.bgn,
});

export const moneyFormGroupRequired = (fb: UntypedFormBuilder,
                                       value?: number) => fb.group({
   amount: [value ?? '', Validators.required],
   currency: [Currency.bgn, Validators.required],
});

export const touchAllFormFields = (formGroup: FormGroup) =>
   Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormControl) {
         control.markAsTouched({onlySelf: true});
      } else if (control instanceof FormGroup) {
         touchAllFormFields(control);
      }
   });

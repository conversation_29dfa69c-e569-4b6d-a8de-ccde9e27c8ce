import {ID, Identifiable, MaybeID, NoID} from '../data/identifiable';
import {DataService} from '../services/data-service';
import {forkJoin, mergeMap, Observable, of, throwError} from 'rxjs';
import {
   catchError,
   defaultIfEmpty,
   map,
   shareReplay,
   switchMap,
   tap
} from 'rxjs/operators';
import {Injectable} from '@angular/core';
import {addId, serverUrl} from './http-utility';
import {HttpClient} from '@angular/common/http';

@Injectable()
export abstract class DtoCache<T extends Identifiable, DTO extends Identifiable = T>
   extends DataService<T> {
   protected readonly baseUrl: string;
   protected idUrl: (id: string) => string;
   private cache = new Map<ID, Observable<T>>();
   private cacheHasAll = false;

   protected constructor(url: string, protected http: HttpClient) {
      super();
      this.baseUrl = serverUrl(url);
      this.idUrl = addId.bind(null, this.baseUrl);
   }

   override getAll(): Observable<T[]> {
      if (this.cacheHasAll) {
         return forkJoin(Array.from(this.cache.values()));
      }

      return this.http.get<DTO[]>(this.baseUrl).pipe(
         mergeMap(cs => forkJoin(cs.map(c => this.mapToLocal(c)))),
         tap(all => {
            all.forEach(entry => this.cache.set(entry.id, of(entry)));
            this.cacheHasAll = true;
         }),
         defaultIfEmpty([]),
      );
   }

   override get(id: ID): Observable<T> {
      const cachedEntry = this.cache.get(id);
      if (cachedEntry) {
         return cachedEntry;
      }

      const request$ = this.http.get<DTO>(this.idUrl(id)).pipe(
         mergeMap(c => this.mapToLocal(c)),
         catchError(err => {
            console.warn(`Removing cached entry '${id}' due to error`, err);
            this.cache.delete(id);
            this.cacheHasAll = false;
            return throwError(() => err);
         }),
         shareReplay(1)
      );

      this.cache.set(id, request$);
      return request$;
   }

   override add(newEntry: NoID<T>): Observable<T> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newEntry)).pipe(
         switchMap(id => this.get(id))
      );
   }

   override update(entry: T): Observable<void> {
      return this.http.put<void>(this.idUrl(entry.id), this.mapToRemote(entry)).pipe(
         tap(() => this.cache.delete(entry.id)),
         switchMap(() => this.get(entry.id)),
         map(() => undefined)
      );
   }

   override delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id)).pipe(
         tap(() => this.cache.delete(id)),
      );
   }

   batchDelete(ids: ID[]): Observable<void> {
      return this.http.post<void>(`${this.baseUrl}/batch-delete`, {ids}).pipe(
         tap(() => ids.forEach(id => this.cache.delete(id))),
      );
   }

   invalidateCache(): void {
      this.cacheHasAll = false;
      this.cache.clear();
   }

   protected setCacheEntries(entries: T[]) {
      entries.forEach(e => this.cache.set(e.id, of(e)));
   }

   protected deleteCacheEntries(ids: ID[]) {
      ids.forEach(id => this.cache.delete(id));
   }

   protected abstract mapToLocal(dto: DTO): Observable<T>;

   protected abstract mapToRemote(entry: MaybeID<T>): MaybeID<DTO>;
}

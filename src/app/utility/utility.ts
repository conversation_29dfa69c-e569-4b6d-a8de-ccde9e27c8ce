import {DateTime} from 'luxon';
import {Identifiable} from '../data/identifiable';
import {<PERSON><PERSON><PERSON>cy, DateRange, Money} from '../data/common';
import {CustomerGroup} from '../data/customers/customer-group';
import {AbstractControl, ValidationErrors, ValidatorFn} from '@angular/forms';

export const getDuration = (start: DateTime, end: DateTime): number =>
   Math.round(Math.abs(start.diff(end).as('days')));

export const isSameDate = (d1: DateTime, d2: DateTime): boolean =>
   d1.day === d2.day && d1.month === d2.month && d1.year === d2.year;

export const blockEvent = (event: Event): void => {
   event.stopPropagation();
   event.preventDefault();
};

export const canonicalDate = (d: DateTime): DateTime => d.setLocale('bg')
   .set({hour: 12, minute: 0, second: 0, millisecond: 0});

export const today = (): DateTime => canonicalDate(DateTime.local());

export const yesterday = (): DateTime => today().minus({day: 1});

type AssertFn = (cond: any, msg?: string) => asserts cond;
export const assert: AssertFn = (cond, msg?) => {
   if (!cond) {
      throw new Error(msg ?? `Assertion error: ${cond}`);
   }
};

export const last = <T>(arr: T[]): T => arr[arr.length - 1];

export const equalIdentifiables = (o1?: Identifiable, o2?: Identifiable): boolean =>
   !!o1 && !!o2 && o1.id === o2.id;

export const isRangeInView = (range: DateRange,
                              start: DateTime,
                              end: DateTime): boolean =>
   areRangesIntertwined(range.start, range.end, start, end);

export const areRangesIntertwined = (start1: DateTime,
                                     end1: DateTime,
                                     start2: DateTime,
                                     end2: DateTime): boolean =>
   (start1 <= end2 && start2 <= end1);

export const isDateInRange = (range: DateRange, date: DateTime): boolean =>
   range.start <= date && date <= range.end;

export const secretId = (value: string): string => {
   const length = value.length;
   if (length < 5) {
      return '*'.repeat(length);
   } else {
      return value.substring(0, length - 4) + '****';
   }
};

export const isNotClick = (event: MouseEvent) => event.detail === 0;

export const sum = (a: number, b: number) => a + b;

export const DEBOUNCE_TIME = 230; // milliseconds

export const AUTO_FOCUS_TIME = 250; // milliseconds

export const nullMoney = () => ({amount: 0, currency: Currency.bgn});

export const moneyToString = (money?: Money | null, defaultValue = ''): string => {
   if (!money || (!money.amount && money.amount !== 0)) {
      return defaultValue;
   }

   let currency = '';
   switch (money.currency) {
      case Currency.bgn:
         currency = 'лв';
         break;
      case Currency.eur:
         currency = '€';
         break;
      case Currency.usd:
         currency = '$';
   }

   return `${money.amount.toFixed(2)} ${currency}`;
};

export const rangesToString = (ranges: DateRange[] | null | undefined): string => {
   if (!ranges) {
      return '';
   }

   return ranges
      .map(r => `${longDateFmt(r.start)} до ${longDateFmt(r.end)}`)
      .join(' и ');
};

export const logJson = (data: any, name?: string): void => {
   console.group(name ?? 'data');
   console.log(JSON.stringify(data, undefined, 4));
   console.groupEnd();
};

export const strcmp = (key?: string) => (a: any, b: any) =>
   key ? a[key].localeCompare(b[key]) : a.localeCompare(b);
export const cmp = strcmp();
export const cmpName = strcmp('name');

export const cumulativeOffsetTop = (element: HTMLElement): number => {
   let top = 0;
   do {
      if (element.tagName !== 'MAT-DRAWER-CONTAINER') {
         top += element.offsetTop || 0;
      }
      element = element.offsetParent as HTMLElement;
   } while (element);

   return top;
};

export const dateFmt = (d: DateTime) => d.toFormat('dd.LL', {locale: 'bg'});
export const longDateFmt = (d: DateTime) => d.toFormat('DDD', {locale: 'bg'});

// @ts-expect-error DateTimes can be subtracted form each other which results in a number
export const dateCmp = (d1: DateTime, d2: DateTime): number => d1 - d2;

export const filterProperties = (obj: any,
                                 filterFn: (entry: [string, any]) => boolean) =>
   Object.entries(obj)
      .filter(filterFn)
      .reduce((result, [prop, value]) => {
         result[prop] = value;
         return result;
      }, {} as any);

export const includes = (filter: string, ...ss: (string | undefined)[]): boolean =>
   ss.some(s => s?.toLowerCase().includes(filter));

export const sortCustomerGroups = (cgs: CustomerGroup[]) =>
   cgs.sort((a, b) => (a.minAge ?? -1) - (b.minAge ?? -1));

export const normalizeMoney = (x: Money): Money => ({
   currency: x.currency,
   amount: Number(x.amount.toFixed(2)) || 0
});

export const notNull = (obj: any): boolean => !!obj;

export const commaJoin = (list: any[], prop?: string): string =>
   (prop ? list.map(item => item[prop]) : list).join(', ');

export const nameJoin = (list: ({ name: string })[]): string => commaJoin(list, 'name');

export const ids = (list: Identifiable[]) => list.map(item => item.id);

export const names = (list: ({ name: string })[]): string[] =>
   list.map(item => item.name);

export const titleCase = (str: string) => str.split(' ')
   .map(w => w.length > 1 ? `${w.charAt(0).toUpperCase()}${w.slice(1).toLowerCase()}` : w)
   .join(' ');

export function enumValidator<T extends object>(enumType: T): ValidatorFn {
   return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
         return null; // no value, no validation
      }

      const enumValues = Object.values(enumType);
      if (enumValues.includes(control.value)) {
         return null; // valid value
      }

      return {invalidEnumValue: {value: control.value}}; // invalid value
   };
}

export const parseDate = (date?: string) => date != undefined ?
   DateTime.fromMillis(parseInt(date, 10)).setLocale('bg') : undefined;

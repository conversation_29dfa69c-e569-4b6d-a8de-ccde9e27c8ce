import {filterProperties, getDuration, isSameDate, secretId, today} from './utility';

describe('Utility', () => {
   it('getDuration should calculate duration of 1 day', () => {
      expect(getDuration(today(), today()))
         .toBe(0);
   });
   it('getDuration should calculate duration of 3 days', () => {
      expect(getDuration(today(), today().plus({days: 2})))
         .toBe(2);
   });
   it('isSameDate should be true', () => {
      expect(isSameDate(today(), today()))
         .toBeTrue();
   });
   it('isSameDate should be false', () => {
      expect(isSameDate(today(), today().plus({days: 1})))
         .toBeFalse();
   });
   it('secretId should return nothing with an empty string', () => {
      expect(secretId(''))
         .toBe('');
   });
   it('secretId should return only asterisks with a string below 5 characters', () => {
      expect(secretId('a'))
         .toBe('*');
      expect(secretId('aa'))
         .toBe('**');
      expect(secretId('aaa'))
         .toBe('***');
      expect(secretId('aaaa'))
         .toBe('****');
   });
   it('secretId should replace the last 4 characters of a string with asterisks', () => {
      expect(secretId('aaaaa'))
         .toBe('a****');
      expect(secretId('aaaaaa'))
         .toBe('aa****');
   });
   it('filterProperties should not filter if it matches all', () => {
      expect(filterProperties({a: 1, b: 1}, () => true))
         .toEqual({a: 1, b: 1});
   });
   it('filterProperties should return an empty object if it matches none', () => {
      expect(filterProperties({a: 1, b: 1}, () => false))
         .toEqual({});
   });
   it('filterProperties should filter properties by key', () => {
      expect(filterProperties({a: 1, b: 1}, ([name, _]) => name !== 'a'))
         .toEqual({b: 1});
   });
   it('filterProperties should filter properties by value', () => {
      expect(filterProperties({a: 1, b: 1}, ([_, value]) => value !== 1))
         .toEqual({});
   });
   it('filterProperties should filter some properties by value', () => {
      expect(filterProperties({a: 1, b: 2}, ([_, value]) => value !== 1))
         .toEqual({b: 2});
   });
});

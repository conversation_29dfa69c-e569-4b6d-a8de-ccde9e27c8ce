import {MatDialog} from '@angular/material/dialog';
import {filter, map, Observable, tap} from 'rxjs';
import {Customer} from '../data/customers/customer';
import {ID, NoID} from '../data/identifiable';
import {Document, Invoice, InvoiceCategory} from '../data/invoice';
import {
   CustomerPickerDialogComponent,
   CustomerPickerDialogData
} from '../dialogs/customer-picker-dialog/customer-picker-dialog.component';
import {InvoiceService} from '../services/invoice.service';
import {switchMap} from 'rxjs/operators';

export interface InvoiceInput {
   category: InvoiceCategory;
   sender: Customer;
   accountId: ID;
   titular: Customer;
   items?: ID[];
}

export interface InvoiceData {
   document: Document;
   invoice: Invoice;
   isDocumentCopy: boolean;
}

export const issueInvoice = ({titular, items, ...input}: InvoiceInput,
   sInvoice: InvoiceService,
   dialog: MatDialog): Observable<InvoiceData> => {
   const data: CustomerPickerDialogData = {
      customer: titular,
      useCase: 'фактура'
   };
   const customerPicker = dialog.open(CustomerPickerDialogComponent, {data});

   return customerPicker.afterClosed()
      .pipe(
         filter(result => !!result),
         switchMap((customer: Customer): Observable<Invoice> => {
            const invoice: NoID<Invoice> = {receiver: customer, ...input};
            if (items?.length) {
               return sInvoice.addPartial(invoice, items);
            } else {
               return sInvoice.add(invoice);
            }
         }),
         switchMap(invoice => generateInvoiceDocument(invoice, sInvoice)),
      );
};

export const generateInvoiceDocument = (invoice: Invoice,
   sInvoice: InvoiceService,
   isDocumentCopy = false): Observable<InvoiceData> =>
   sInvoice.generateDocument(invoice.id, isDocumentCopy).pipe(
      tap(invoiceDocument => {
         const wnd = window.open('about:blank', '', '_blank');
         if (wnd) {
            wnd.document.write(invoiceDocument.content);
            wnd.print();
         }
      }),
      map(document => ({document, invoice, isDocumentCopy}))
   );

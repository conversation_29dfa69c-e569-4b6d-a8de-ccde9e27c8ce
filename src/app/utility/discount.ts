import {Money} from '../data/common';

export enum DiscountType {
   amount = 'Amount',
   percentage = 'Percentage',
}

export interface DiscountBase {
   type: DiscountType;
}

export interface AmountDiscount extends DiscountBase {
   type: DiscountType.amount;
   amount: Money;
}

export interface PercentageDiscount extends DiscountBase {
   type: DiscountType.percentage;
   percentage: number;
}

export type Discount = AmountDiscount | PercentageDiscount;

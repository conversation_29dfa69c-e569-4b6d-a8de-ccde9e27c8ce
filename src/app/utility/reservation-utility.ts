import {GuestGroupCount, Reservation, ReservationStatus} from '../data/reservation';
import {DateTime} from 'luxon';
import {sum, yesterday} from './utility';
import {Observable, of} from 'rxjs';
import {ReservationService} from '../services/reservation.service';
import {Money} from '../data/common';
import {validMoney} from './money-utility';
import {CustomerGroup} from '../data/customers/customer-group';

export const guestNumber = (res: Partial<Reservation>): number => res.guestGroupCount ?
   Object.values(res.guestGroupCount).reduce((a, b) => a + b, 0) : 0;

export const roomCapacityFull = (res: Partial<Reservation>): boolean =>
   res.room && res.guests
      ? res.guests.length >= res.room.baseCapacity + res.room.additionalCapacity
      : false;

export const getResStatusName = (status: ReservationStatus): string => {
   switch (status) {
      case ReservationStatus.pending:
         return 'Предстояща';
      case ReservationStatus.ongoing:
         return 'Настанена';
      case ReservationStatus.completed:
         return 'Завършена';
   }
};

export const fixDates = (value: { start: DateTime; end: DateTime }): any => {
   const {start, end} = value;

   if (DateTime.isDateTime(start)) {
      value.start = start.set({hour: 12, minute: 0, second: 1});
   }
   if (DateTime.isDateTime(end)) {
      value.end = end.set({hour: 12, minute: 0, second: 0});
   }

   return value;
};

export const validGGC = (ggc?: GuestGroupCount): ggc is GuestGroupCount =>
   !!ggc && Object.values(ggc).reduce(sum, 0) > 0;

export const getDays = (count: number, firstDay: DateTime = yesterday()): DateTime[] =>
   Array(count).fill(0).map((_, i) => firstDay.plus({days: i}));

export const getReservationPrice = (reservation: Partial<Reservation>,
                                    svc: ReservationService): Observable<Money> => {
   const {manualPrice: mp} = reservation;

   if (mp && hasManualPrice(reservation)) {
      return of(mp);
   } else {
      delete reservation.manualPrice;
      return svc.getPrice(reservation);
   }
};

export const hasManualPrice = ({manualPrice: mp}: { manualPrice?: Money }): boolean =>
   validMoney(mp);

export const formatGGC = (ggc: GuestGroupCount, allGroups: CustomerGroup[]): string =>
   Object.entries(ggc).map(([id, count]) => {
      const group = allGroups.find(cg => cg.id === id) as CustomerGroup;
      return `${count} ${count > 1 ? group.pluralName : group.singularName}`;
   }).join(', ');


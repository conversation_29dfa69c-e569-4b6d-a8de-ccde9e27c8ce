import {DateTime} from 'luxon';
import {fixDates} from './reservation-utility';

describe('Reservation utility', () => {
   it('fixDates should work', () => {
      const reservation: any = {start: DateTime.now(), end: DateTime.now()};
      const fixed = fixDates(reservation);

      expect(fixed.start.hour).toBe(12);
      expect(fixed.start.minute).toBe(0);
      expect(fixed.start.second).toBe(1);

      expect(fixed.end.hour).toBe(12);
      expect(fixed.end.minute).toBe(0);
      expect(fixed.end.second).toBe(0);
   });
});

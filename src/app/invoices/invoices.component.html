<div class="main-view-header full-width">
   <mat-form-field class="search filter">
      <mat-label>Номер на документ</mat-label>
      <input [formControl]="filterInput" autocomplete="off" matInput>
      <mat-icon matSuffix>search</mat-icon>
   </mat-form-field>

   <mat-form-field class="filter">
      <mat-label>Период</mat-label>
      <mat-date-range-input [rangePicker]="rangePicker">
         <input (ngModelChange)="onDatesInputChange()" [(ngModel)]="startDate"
                matStartDate placeholder="Начало">
         <input (ngModelChange)="onDatesInputChange()" [(ngModel)]="endDate" matEndDate
                placeholder="Край">
      </mat-date-range-input>
      <mat-datepicker-toggle [for]="rangePicker" matSuffix/>
      <mat-date-range-picker #rangePicker/>
   </mat-form-field>
</div>
<div class="main-view-table mat-elevation-z5" style="width: 90vw;">
   <table [dataSource]="dataSource" mat-table>
      <mat-text-column [dataAccessor]="getType" headerText="Тип" name="type"/>
      <mat-text-column [dataAccessor]="getNumber" headerText="Номер" name="number"/>
      <mat-text-column [dataAccessor]="getIssuedAt" headerText="Издаден на"
                       name="timestamp"/>
      <mat-text-column [dataAccessor]="getReceiver" headerText="Получател"
                       name="receiver"/>
      <mat-text-column [dataAccessor]="getOperator" headerText="Издадена от"
                       name="operator"/>

      <ng-container matColumnDef="actions">
         <th *matHeaderCellDef mat-header-cell>Действия</th>
         <td *matCellDef="let invoice" mat-cell>
            <button [matMenuTriggerData]="{invoice}" [matMenuTriggerFor]="menu"
                    mat-icon-button>
               <mat-icon>more_vert</mat-icon>
            </button>
         </td>
      </ng-container>

      <tr *matHeaderRowDef="columns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: columns;" mat-row></tr>

      <tr *matNoDataRow>
         <td [colSpan]="columns.length" class="text-center">Няма фактури за периода</td>
      </tr>
   </table>

   <mat-paginator [pageSizeOptions]="[20, 50, 100]" showFirstLastButtons/>
</div>

<mat-menu #menu>
   <ng-template let-invoice="invoice" matMenuContent>
      <button (click)="showInvoice(invoice, false)" mat-menu-item>
         <mat-icon>insert_drive_file</mat-icon>
         Оригинал
      </button>
      @if (invoice.category.isFiscalDocument) {
         <button (click)="showInvoice(invoice, true)" mat-menu-item>
            <mat-icon>file_copy</mat-icon>
            Копие
         </button>
      }
   </ng-template>
</mat-menu>

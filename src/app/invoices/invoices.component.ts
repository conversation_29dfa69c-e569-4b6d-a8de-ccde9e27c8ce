import {AfterViewInit, Component, inject, OnD<PERSON>roy, ViewChild} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';
import {FormatCustomerPipe} from '../pipes/format-customer.pipe';
import {DEBOUNCE_TIME, today} from '../utility/utility';
import {MatPaginator} from '@angular/material/paginator';
import {debounceTime, filter, Subject} from 'rxjs';
import {FormControl} from '@angular/forms';
import {InvoiceService} from '../services/invoice.service';
import {Invoice} from '../data/invoice';
import {DateTimePipe} from '../pipes/date-time.pipe';
import {generateInvoiceDocument} from '../utility/invoice-utility';

const timestampSort = (a: Invoice, b: Invoice) => a.timestamp!.valueOf() -
   b.timestamp!.valueOf();

@Component({
   selector: 'app-invoices',
   templateUrl: './invoices.component.html',
   styleUrl: './invoices.component.scss',
   standalone: false
})
export class InvoicesComponent implements AfterViewInit, OnDestroy {
   @ViewChild(MatPaginator) paginator!: MatPaginator;

   dataSource = new MatTableDataSource<Invoice>();
   columns = ['type', 'number', 'receiver', 'timestamp', 'operator', 'actions'];
   startDate = today().minus({days: 15});
   endDate = today().plus({days: 15});
   filterInput = new FormControl();

   getType = (invoice: Invoice) => invoice.category.name;
   getNumber = (invoice: Invoice) => invoice.invoiceNumber?.toString();
   getIssuedAt = (invoice: Invoice) => DateTimePipe.toString(invoice.timestamp);
   getReceiver = (invoice: Invoice) => FormatCustomerPipe.toString(invoice.receiver);
   getOperator = (invoice: Invoice) => invoice.cashier?.name;

   private sInvoice = inject(InvoiceService);
   private dateChangeSubject = new Subject<void>();
   private updateDatesSubscription = this.dateChangeSubject.pipe(
      debounceTime(DEBOUNCE_TIME),
      filter(() => !!this.startDate && !!this.endDate),
   ).subscribe(() => this.renewData());

   ngAfterViewInit(): void {
      this.dataSource.paginator = this.paginator;
      this.dataSource.filterPredicate =
         (i: Invoice, fltr) => !!i.invoiceNumber?.toString().includes(fltr);
      this.filterInput.valueChanges.subscribe(
         fltr => this.dataSource.filter = fltr.toLowerCase());
      this.renewData();
   }

   ngOnDestroy() {
      this.updateDatesSubscription.unsubscribe();
   }

   onDatesInputChange() {
      this.dateChangeSubject.next();
   }

   protected showInvoice(invoice: Invoice, isCopy: boolean) {
      return generateInvoiceDocument(invoice, this.sInvoice, isCopy).subscribe();
   }

   private renewData(): void {
      this.sInvoice.getInRange(this.startDate.startOf('day'), this.endDate.endOf('day'))
         .subscribe(invoices => this.dataSource.data = invoices.sort(timestampSort));
   }
}

<div class="queries-container">
   <div class="query-groups">
      @for (group of groups$ | async; track group.id) {
         <div class="query-group">
            <span class="group-title">
               <mat-icon>{{group.icon}}</mat-icon>
               {{group.name}}
            </span>
            @for (sg of group.subgroups | keyvalue; track sg) {
               <div>
                  @if (sg.key !== 'default') {
                     <span class="subgroup-title">{{sg.key}}</span>
                  }
                  <div class="subgroup-container">
                     @for (q of sg.value; track q.id) {
                        <span (click)="selectQuery(q)"
                              [class.selected-query]="q.id === query!.id"
                              [class.primary-text]="q.id === query!.id"
                              class="clickable no-select query-name query-highlight">
                           {{q.name}}
                        </span>
                     }
                  </div>
               </div>
            }
         </div>
      }
   </div>
   <div class="query-container">
      @if (query) {
         <h1><b>{{query.name}}</b></h1>
      }
      <div class="child-hm-s flex-row">
         <div>
            <mat-form-field>
               <mat-label>Период</mat-label>
               <mat-date-range-input [rangePicker]="rangePicker">
                  <input [(ngModel)]="startDate" matStartDate placeholder="Начало">
                  <input [(ngModel)]="endDate" matEndDate placeholder="Край">
               </mat-date-range-input>
               <mat-datepicker-toggle [for]="rangePicker" matSuffix/>
               <mat-date-range-picker #rangePicker/>
            </mat-form-field>
         </div>
         <button (click)="execute()" [disabled]="loading" class="query-execute"
                 color="primary" mat-raised-button>
            @if (loading) {
               <mat-icon>
                  <mat-spinner diameter="18" color="primary"/>
               </mat-icon>
            }
            Изпълни
         </button>
         @if (result && allowExport) {
            <button (click)="downloadResult()" mat-stroked-button>
               <mat-icon>file_download</mat-icon>
               Свали резултата
            </button>
         }
      </div>
      <ng-template appViewContainerRef/>
   </div>
</div>

<ng-template #resultReady>
   <p>Данните са готови за изтегляне.</p>
</ng-template>
<ng-template #noResults>
   <p>Няма резултат за избрания период.</p>
</ng-template>

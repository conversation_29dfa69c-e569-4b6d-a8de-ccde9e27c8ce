import {Component, inject, Injector, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {
   Query,
   QueryExportType,
   QueryGroup,
   QueryInput,
   QueryResult,
   QueryType
} from '../../data/queries/query';
import {QueryService} from '../../services/query.service';
import {forkJoin, Observable, tap, timer} from 'rxjs';
import {ViewContainerRefDirective} from '../../directives/view-container-ref.directive';
import {QUERY, QUERY_DATA} from '../display/query-display';
import {TableQueryComponent} from '../display/table-query/table-query.component';
import {cmpName, today} from '../../utility/utility';
import {
   PeriodicTableQueryComponent
} from '../display/periodic-table-query/periodic-table-query.component';
import {AnalyticsService} from '../../services/analytics.service';
import {
   GroupedTableQueryComponent
} from '../display/grouped-table-query/grouped-table-query.component';
import {map} from 'rxjs/operators';
import {
   MultipleQueriesComponent
} from '../display/multiple-queries/multiple-queries.component';
import {downloadCsv, downloadXlsx, isESTI} from '../../utility/query-utility';

const resStart = {hour: 12, minute: 0, second: 1, millisecond: 0};

@Component({
   selector: 'app-queries',
   templateUrl: './queries.component.html',
   styleUrls: ['./queries.component.scss'],
   standalone: false
})
export class QueriesComponent implements OnInit {
   @ViewChild(ViewContainerRefDirective, {static: true})
   resultsContainer!: ViewContainerRefDirective;

   @ViewChild('resultReady') resultReady!: TemplateRef<any>;
   @ViewChild('noResults') noResults!: TemplateRef<any>;

   query: Query | undefined;
   groups$: Observable<QueryGroup[]> | undefined;

   loading = false;
   allowExport = false;
   result?: QueryResult;

   startDate = today();
   endDate = today();

   private sQuery = inject(QueryService);
   private sAnalytics = inject(AnalyticsService);

   ngOnInit(): void {
      this.groups$ = this.sQuery.getGroups().pipe(
         tap(groups => {
            for (const group of groups) {
               for (const query of Object.values(group.subgroups).flat()) {
                  if (isESTI(query)) {
                     this.query = query;
                  }
               }
            }
         }),
         map(gs => {
            const result = gs.sort(cmpName);
            for (const group of gs) {
               const {subgroups} = group;
               for (const subgroup of Object.keys(subgroups)) {
                  subgroups[subgroup] = subgroups[subgroup].sort(cmpName);
               }
            }
            return result;
         }),
      );
   }

   execute(): void {
      const isReservationQuery = this.query?.action === 'FreeRoomsInWholeRange';
      const queryInput: QueryInput = {
         dateRange: {
            start: isReservationQuery ? this.startDate.set(resStart) :
               this.startDate.startOf('day'),
            end: this.endDate.endOf('day'),
         },
      };

      if (this.query) {
         this.sAnalytics.event('query', 'execute', this.query.name);

         this.clear();
         this.loading = true;

         forkJoin([
            timer(500), // TODO(vlado): lower this value update by update
            this.sQuery.execute(this.query.id, queryInput)
         ]).subscribe({
            next: ([_, result]) => {
               this.result = result;

               const hasData = this.resultHasData(result);
               this.allowExport = hasData && !isESTI(this.query);
               if (this.query?.displayResult) {
                  this.renderQueryComponent(result);
               } else {
                  const vcr = this.resultsContainer.viewContainerRef;
                  vcr.clear();
                  if (hasData) {
                     vcr.createEmbeddedView(this.resultReady);
                  } else {
                     vcr.createEmbeddedView(this.noResults);
                  }
               }
               this.loading = false;
            },
            error: () => this.loading = false
         });
      }
   }

   selectQuery(query: Query): void {
      if (this.query?.id !== query.id) {
         this.query = query;
         this.clear();
      }
   }

   downloadResult(): void {
      if (this.query && this.result && this.allowExport) {
         if (this.query.exportType === QueryExportType.csv &&
            this.result.type === QueryType.table) {
            const data = [this.result.columnNames, ...this.result.data];
            downloadCsv(this.query, data);
         } else {
            downloadXlsx(this.query, this.result);
         }
      }
   }

   private renderQueryComponent(result: QueryResult) {
      const vcr = this.resultsContainer.viewContainerRef;
      vcr.clear();

      const injector = Injector.create({
         providers: [
            {provide: QUERY, useValue: this.query},
            {provide: QUERY_DATA, useValue: result}
         ]
      });

      switch (result.type) {
         case QueryType.table:
            vcr.createComponent(TableQueryComponent, {injector});
            break;
         case QueryType.periodicTable:
            vcr.createComponent(PeriodicTableQueryComponent, {injector});
            break;
         case QueryType.groupedTable:
            vcr.createComponent(GroupedTableQueryComponent, {injector});
            break;
         case QueryType.multiple:
            vcr.createComponent(MultipleQueriesComponent, {injector});
            break;
      }
   }

   private resultHasData(result: QueryResult) {
      switch (result.type) {
         case QueryType.table:
            return result.data.length > 0;
         case QueryType.periodicTable:
            return result.data.length > 0;
         case QueryType.groupedTable:
            return result.data.length > 0;
         case QueryType.multiple:
            return Object.keys(result.results).length > 0;
      }
   }

   private clear(): void {
      this.resultsContainer.viewContainerRef.clear();
      this.result = undefined;
      this.allowExport = false;
   }
}

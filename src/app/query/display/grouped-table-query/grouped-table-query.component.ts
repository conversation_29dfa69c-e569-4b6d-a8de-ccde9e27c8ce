import {Component, inject, Input, OnInit} from '@angular/core';
import {TableQueryData} from '../../../data/queries/table-query';
import {QUERY, QUERY_DATA} from '../query-display';
import {GroupedTableQueryResult} from '../../../data/queries/grouped-table-query';

@Component({
   selector: 'app-grouped-table-query',
   template: `
      @for (group of groups | keyvalue; track group.key) {
         <div style="margin-top: 16px;">
            <span class="mat-h3">{{group.key}}</span>

            <div class="mat-elevation-z3">
               <table [dataSource]="group.value" class="max-width" mat-table>
                  @for (column of columnNames; track column) {
                     <mat-text-column [headerText]="column" [name]="$index.toString()"/>
                  }
                  <tr *matHeaderRowDef="columnIds" mat-header-row class="q-header"></tr>
                  <tr *matRowDef="let row; columns: columnIds" mat-row class="q-row"></tr>
               </table>
            </div>
         </div>
      }
   `,
   standalone: false
})
export class GroupedTableQueryComponent implements OnInit {
   columnNames: string[] = [];
   columnIds: string[] = [];
   groups = new Map<string, TableQueryData>();

   @Input() query = inject(QUERY);
   @Input() result = inject(QUERY_DATA) as GroupedTableQueryResult;

   ngOnInit(): void {
      this.renewData(this.result);
   }

   private renewData(data: GroupedTableQueryResult): void {
      this.columnNames = data.columnNames;
      this.columnIds = Object.keys(this.columnNames);

      for (const periodicTableRow of data.data) {
         this.groups.set(periodicTableRow.header, periodicTableRow.data);
      }
   }
}

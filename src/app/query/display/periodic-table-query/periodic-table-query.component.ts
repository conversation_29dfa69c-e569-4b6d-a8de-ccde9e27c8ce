import {Component, inject, Input, OnInit} from '@angular/core';
import {QUERY, QUERY_DATA} from '../query-display';
import {
   PeriodicTableQueryData,
   PeriodicTableQueryResult
} from '../../../data/queries/periodic-table-query';
import {TableQueryData} from '../../../data/queries/table-query';
import {DateTime} from 'luxon';
import {downloadEstiCsv, isESTI} from '../../../utility/query-utility';

@Component({
   selector: 'app-periodic-table-query',
   templateUrl: './periodic-table-query.component.html',
   standalone: false
})
export class PeriodicTableQueryComponent implements OnInit {
   columnNames: string[] = [];
   columnIds: string[] = [];
   periods = new Map<number, TableQueryData>();

   isESTI = false;

   @Input() query = inject(QUERY);
   @Input() result = inject(QUERY_DATA) as PeriodicTableQueryResult;

   ngOnInit(): void {
      this.isESTI = isESTI(this.query);
      this.renewData(this.result);
   }

   downloadAsCsv(date: number, value: PeriodicTableQueryData) {
      if (this.isESTI) {
         const data = [this.columnNames, ...value];
         const time = DateTime.fromMillis(date);
         downloadEstiCsv(this.query, data, time);
      }
   }

   private renewData(data: PeriodicTableQueryResult): void {
      this.columnNames = data.columnNames;
      this.columnIds = Object.keys(this.columnNames);

      for (const periodicTableRow of data.data) {
         this.periods.set(periodicTableRow.time, periodicTableRow.data);
      }
   }
}

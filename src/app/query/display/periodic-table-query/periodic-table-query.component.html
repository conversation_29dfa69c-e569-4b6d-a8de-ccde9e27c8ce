@for (period of periods | keyvalue; track period) {
   <div style="margin-top: 16px;">
      <span class="mat-h3">{{period.key | dateTime}}</span>
      @if (isESTI) {
         <button (click)="downloadAsCsv(period.key, period.value)" mat-icon-button
                 matTooltip="Свали файл за ЕСТИ" matTooltipPosition="right">
            <mat-icon>description</mat-icon>
         </button>
      }

      @if (!isESTI) {
         <div class="mat-elevation-z3">
            <table [dataSource]="period.value" class="max-width" mat-table>
               @for (column of columnNames; track column; let i = $index) {
                  <mat-text-column [headerText]="column" [name]="i.toString()"/>
               }
               <tr *matHeaderRowDef="columnIds" mat-header-row class="q-header"></tr>
               <tr *matRowDef="let row; columns: columnIds" mat-row class="q-row"></tr>

               <tr *matNoDataRow class="mat-row">
                  <td [colSpan]="columnIds.length" class="mat-cell text-center">
                     Няма резултат за този период.
                  </td>
               </tr>
            </table>
         </div>
      }
   </div>
} @empty {
   <p>Няма резултат за избрания период.</p>
}

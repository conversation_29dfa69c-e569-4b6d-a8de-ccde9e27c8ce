import {Component, inject} from '@angular/core';
import {QUERY, QUERY_DATA} from '../query-display';
import {MultipleQueriesResult} from '../../../data/queries/multiple-queries';
import {QueryType} from '../../../data/queries/query';

@Component({
   selector: 'app-multiple-queries',
   template: `
      @for (result of data.results | keyvalue; track result.key) {
         <div style="margin-top: 8px;">
            <b class="mat-h2">{{result.key}}</b>
         </div>

         @switch (result.value.type) {
            @case (qt.table) {
               <app-table-query [query]="query" [result]="$any(result.value)"/>
            }
            @case (qt.periodicTable) {
               <app-periodic-table-query [query]="query" [result]="$any(result.value)"/>
            }
            @case (qt.groupedTable) {
               <app-grouped-table-query [query]="query" [result]="$any(result.value)"/>
            }
            @default {
               <h2 class="warn-text">Типът справка {{result.key}} не се поддържа!</h2>
            }
         }
      }
   `,
   standalone: false
})
export class MultipleQueriesComponent {
   query = inject(QUERY);
   data = inject(QUERY_DATA) as MultipleQueriesResult;

   qt = QueryType;
}

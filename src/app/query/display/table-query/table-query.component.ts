import {Component, inject, Input, OnInit} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';
import {QUERY, QUERY_DATA} from '../query-display';
import {TableQueryData, TableQueryResult} from '../../../data/queries/table-query';

@Component({
   selector: 'app-table-query',
   template: `
      <div class="mat-elevation-z3">
         <table [dataSource]="dataSource" class="max-width" mat-table>
            @for (column of columns; track column) {
               <ng-container [matColumnDef]="column">
                  <th *matHeaderCellDef mat-header-cell>{{column}}</th>
                  <td *matCellDef="let list" mat-cell>{{list[$index]}}</td>
               </ng-container>
            }

            <tr *matHeaderRowDef="columns" mat-header-row class="q-header"></tr>
            <tr *matRowDef="let row; columns: columns;" mat-row class="q-row"></tr>

            <tr *matNoDataRow class="mat-row">
               <td [colSpan]="columns.length" class="mat-cell" style="text-align: center">
                  Няма резултат за избрания период.
               </td>
            </tr>
         </table>
      </div>
   `,
   standalone: false
})
export class TableQueryComponent implements OnInit {
   columns: string[] = [];
   data: TableQueryData = [];
   dataSource = new MatTableDataSource();

   @Input() query = inject(QUERY);
   @Input() result = inject(QUERY_DATA) as TableQueryResult;

   ngOnInit(): void {
      this.renewData(this.result);
   }

   private renewData(result: TableQueryResult): void {
      this.columns = result.columnNames;
      this.dataSource.data = result.data;
      this.data = result.data;
   }
}

import {Payment, PaymentMethod} from '../payment';

// FPA = Fiscal Printer Agent

export enum FiscalPrinterPaymentType {
   cash = 'cash',
   check = 'check',
   coupons = 'coupons',
   extCoupons = 'ext-coupons',
   packaging = 'packaging',
   internalUsage = 'internal-usage',
   damage = 'damage',
   card = 'card',
   bank = 'bank',
   reserved1 = 'reserved1',
   reserved2 = 'reserved2',
}

export enum MessageSeverity {
   info = 'info',
   warning = 'warning',
   error = 'error',
}

export interface FPAMessageInfo {
   type: MessageSeverity.info;
   text: string;
}

export interface FPAMessageError {
   type: MessageSeverity.warning | MessageSeverity.error;
   text: string;
   code: string;
}

export type FPMessage = FPAMessageInfo | FPAMessageError;

export interface FiscalAgentPrinter {
   uri: string;
   serialNumber: string;
   fiscalMemorySerialNumber: string;
   manufacturer: string;
   model: string;
   firmwareVersion: string;
   itemTextMaxLength: number;
   commentTextMaxLength: number;
   operatorPasswordMaxLength: number;
   taxIdentificationNumber: string;
   supportedPaymentTypes: FiscalPrinterPaymentType[];
}

export interface FPAResponse {
   ok: boolean;
   messages?: FPMessage[];
   deviceDateTime?: string;
}

export interface FPAReceiptResponse extends FPAResponse {
   receiptNumber: string;
   receiptDateTime: string;
   receiptAmount: number;
   fiscalMemorySerialNumber: string;
}

export interface FPAAmountResponse extends FPAResponse {
   amount: number;
}

export interface FPAPurchase {
   text: string;
   unitPrice: number;
   taxGroup: number;
   quantity?: number;
}

export interface FPAPayment {
   amount: number;
   paymentType: FiscalPrinterPaymentType;
}

export interface FPAReceiptRequest {
   uniqueSaleNumber: string;
   items: FPAPurchase[];
   payments?: FPAPayment[];
   operator?: string;
   operatorPassword?: string;
}

export enum ReceiptReversalReason {
   operatorError = 'operator-error',
   refund = 'refund',
   taxBaseReduction = 'tax-base-reduction',
}

export interface FPAReceiptReversalRequest extends FPAReceiptRequest {
   receiptNumber: string;
   receiptDateTime: string;
   fiscalMemorySerialNumber: string;
   reason: ReceiptReversalReason;
}

export interface FPAAmount {
   amount: number;
}

export const methodToFiscal = (method: PaymentMethod): FiscalPrinterPaymentType => {
   switch (method) {
      case PaymentMethod.cash:
         return FiscalPrinterPaymentType.cash;
      case PaymentMethod.bank:
         return FiscalPrinterPaymentType.bank;
      case PaymentMethod.card:
         return FiscalPrinterPaymentType.card;
      case PaymentMethod.voucher:
         return FiscalPrinterPaymentType.coupons;
   }
};

export const paymentToFiscal = (payment: Payment): FPAPayment => ({
   amount: payment.price.amount,
   paymentType: methodToFiscal(payment.method),
});

import {ID, Identifiable} from '../identifiable';
import {FPAPurchase} from './fiscal-agent';

export interface FiscalPrinterCreateRequest extends Identifiable {
   printerId: ID;
   serialNumber: string;
}

export interface FiscalPrinter extends FiscalPrinterCreateRequest {
   receiptNumber: number;
}

export interface Receipt extends Identifiable {
   printerId: ID;
   uniqueSaleNumber: string;
   paymentId: ID;
   items: FPAPurchase[];
   closed: boolean;
   success: boolean;
   reservationId?: ID;
   receiptNumber?: string;
   receiptAmount?: number;
   receiptDateTime?: string;
   fiscalMemorySerialNumber?: string;
}

import {Identifiable} from './identifiable';
import {Contact, Customer} from './customers/customer';
import {DateTime} from 'luxon';
import {Bundle} from './bundles/bundle';
import {Money} from './common';
import {GuestGroupCount} from './reservation';
import {ReservationSource} from './reservation-source';
import {Note} from './notes';

export interface Offer extends Identifiable {
   titular: Customer;
   guestGroupCount: GuestGroupCount;
   bundle: Bundle;
   start: DateTime;
   end: DateTime;
   price: Money;
   isConfirmed: boolean;
   source: ReservationSource;
   notes?: Note[];
}

export interface MessageContent {
   contact: Contact;
   content: string;
   subject: string;
}

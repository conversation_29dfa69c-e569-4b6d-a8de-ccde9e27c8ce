import {DateTime} from 'luxon';
import {Bundle} from './bundles/bundle';
import {Customer} from './customers/customer';
import {Identifiable} from './identifiable';
import {GuestGroupCount} from './reservation';
import {ReservationSource} from './reservation-source';

export interface Cancellation extends Identifiable {
   titular: Customer;
   guestGroupCount: GuestGroupCount;
   bundle: Bundle;
   start: DateTime;
   end: DateTime;
   source: ReservationSource;
}

import {DateTime} from 'luxon';
import {Operator} from './auth/operator';
import {Customer} from './customers/customer';
import {ID, Identifiable} from './identifiable';

export enum InvoiceCategoryType {
   tax = 'TAX',
   proforma = 'PROFORMA',
   hotelBill = 'HOTEL_BILL',
   creditNote = 'CREDIT_NOTE',
}

export interface InvoiceCategory extends Identifiable {
   name: string;
   isFiscalDocument: boolean;
   nextNumber: number;
   type: InvoiceCategoryType;
}

export interface Invoice extends Identifiable {
   category: InvoiceCategory;
   accountId: ID;
   sender: Customer;
   receiver: Customer;
   cashier?: Operator;
   parentInvoice?: Invoice;
   invoiceNumber?: number;
   timestamp?: DateTime;
}

export interface Document {
   content: string;
}

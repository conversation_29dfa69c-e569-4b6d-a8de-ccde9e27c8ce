import {Identifiable} from '../identifiable';
import {TableQueryResult} from './table-query';
import {PeriodicTableQueryResult} from './periodic-table-query';
import {GroupedTableQueryResult} from './grouped-table-query';
import {DateTime} from 'luxon';
import {MultipleQueriesResult} from './multiple-queries';

export enum QueryType {
   table = 'TableQuery',
   periodicTable = 'PeriodicTableQuery',
   groupedTable = 'GroupedTableQuery',
   multiple = 'MultipleQueries',
}

export enum QueryExportType {
   csv = 'CSV',
   excel = 'EXCEL',
}

export interface Query extends Identifiable {
   name: string;
   type: QueryType;
   action: string;
   displayResult: boolean;
   exportType?: QueryExportType;
}

export type QueryDataPrimitive = string | number | boolean;

export interface QueryInput {
   dateRange: {
      start: DateTime;
      end: DateTime;
   };
}

export type SingleQueryResult =
   TableQueryResult
   | PeriodicTableQueryResult
   | GroupedTableQueryResult;

export type QueryResult = SingleQueryResult | MultipleQueriesResult;

export interface QueryGroup extends Identifiable {
   name: string;
   icon: string;
   subgroups: Record<string, Query[]>;
}

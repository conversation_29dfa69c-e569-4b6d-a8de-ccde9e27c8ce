import {ID, Identifiable} from './identifiable';
import {Money} from './common';
import {ReservationInfo} from './reservation';
import {Bundle} from './bundles/bundle';

export interface Purchase extends Identifiable {
   account: ID;
   bundle: Bundle;
   quantity: number;
   price: Money;
   isManual: boolean;
   originalReservation?: ReservationInfo;
   description?: string;
}

export const isPurchase = (o: any): o is Purchase =>
   o && o.account && o.bundle && o.price;

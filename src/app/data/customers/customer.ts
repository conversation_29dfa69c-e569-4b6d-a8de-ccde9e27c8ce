import {DateTime} from 'luxon';
import {Identifiable} from '../identifiable';

export enum Gender {
   male = 'Male',
   female = 'Female',
}

export enum PersonDocumentType {
   card = 'Card',
   passport = 'Passport',
   driverLicense = 'DriverLicense'
}

export enum CustomerType {
   individual = 'Individual',
   legal = 'Legal',
   service = 'Service'
}

export interface PersonDocument {
   type: PersonDocumentType;
   id: string;
   issuedAt: DateTime;
}

export interface Contact {
   name: string;
   lastName?: string;
   address?: string;
   phone?: string;
   email?: string;
   webSite?: string;
   notes?: string;
}

export interface IndividualPerson {
   birthDate: DateTime;
   gender: Gender;
}

export interface LegalPerson {
   mol: string;
}

export interface Customer extends Identifiable {
   type: CustomerType;
   contact: Contact;
   idNumber?: string;
   country?: string;
   vatNumber?: string;
   document?: PersonDocument;
   individual?: IndividualPerson;
   legal?: LegalPerson;
   isForgotten?: boolean;
}

export function fullName(customer: Customer) {
   let result = customer.contact.name;
   if (customer.contact.lastName) {
      result += ` ${customer.contact.lastName}`;
   }

   return result;
}

export function matchCustomerProperty(customer: Customer, string: string): boolean {
   return !!(fullName(customer).toLowerCase().includes(string)
      || customer.contact.email?.includes(string)
      || customer.contact.phone?.includes(string)
      || customer.document?.id.includes(string)
      || customer.idNumber?.includes(string)
      || customer.legal?.mol?.includes(string)
      || customer.vatNumber?.includes(string));
}

import {ID, Identifiable} from '../identifiable';
import {Discount} from '../../utility/discount';
import {DateTime} from 'luxon';
import {Pricing} from '../pricing';
import {Consumable} from '../bundles/consumable';
import {CustomerGroup} from './customer-group';

export enum CustomerDiscountType {
   oldCustomer = 'OldCustomer',
   reservationDuration = 'ReservationDuration',
   customerCount = 'CustomerCount',
   roomConsumable = 'RoomConsumable',
   reservationSources = 'ReservationSources'
}

export interface BaseAccommodationCondition {
   type: CustomerDiscountType;
}

export interface ReservationDurationCondition extends BaseAccommodationCondition {
   type: CustomerDiscountType.reservationDuration;
   minDuration?: number;
   maxDuration?: number;
   dateStart?: DateTime;
   dateEnd?: DateTime;
}

export interface OldCustomerCondition extends BaseAccommodationCondition {
   type: CustomerDiscountType.oldCustomer;
   minYears?: number;
   maxYears?: number;
}

export interface CustomerCountCondition extends BaseAccommodationCondition {
   type: CustomerDiscountType.customerCount;
   minCount?: number;
   maxCount?: number;
}

export interface RoomConsumableCondition extends BaseAccommodationCondition {
   type: CustomerDiscountType.roomConsumable;
   roomConsumable: ID;
}

export interface ReservationSourcesCondition extends BaseAccommodationCondition {
   type: CustomerDiscountType.reservationSources;
   sources: ID[];
}

export type AccommodationCondition =
   ReservationDurationCondition
   | OldCustomerCondition
   | CustomerCountCondition
   | RoomConsumableCondition
   | ReservationSourcesCondition;

export interface CustomerDiscount extends Identifiable {
   name: string;
   discount: Discount;
   accommodationCondition?: AccommodationCondition;
   activePricings?: Pricing[];
   applicableConsumables?: Consumable[];
   applicableCustomerGroups?: CustomerGroup[];
   appliesForRegularBed: boolean;
   appliesForAdditionalBed: boolean;
}

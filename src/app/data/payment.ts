import {ID, Identifiable} from './identifiable';
import {Money} from './common';
import {DateTime} from 'luxon';

export enum PaymentMethod {
   cash = 'Cash',
   bank = 'BankTransfer',
   card = 'Card',
   voucher = 'Voucher'
}

export interface Payment extends Identifiable {
   account: ID;
   price: Money;
   method: PaymentMethod;
   isDownPayment: boolean;
   cashier: string;
   timestamp: DateTime;
   receiptId?: ID;
}

export const isPayment = (o: any): o is Payment => o && o.account && o.price && o.method;

import {Customer} from './customers/customer';
import {DateTime} from 'luxon';
import {Room} from './room';
import {ID, Identifiable} from './identifiable';
import {Bundle} from './bundles/bundle';
import {Money} from './common';
import {ReservationSource} from './reservation-source';
import {FinancialAccount} from './financial-account';
import {VoucherDiscount} from './voucher';
import {Note} from './notes';
import {SurveyAnswers} from './survey';

export enum ReservationStatus {
   pending = 'Pending',
   ongoing = 'Ongoing',
   completed = 'Completed',
}

export interface Reservation extends Identifiable {
   serialNumber: number;
   room: Room;
   titular: Customer;
   guests: Customer[];
   accounts: FinancialAccount[];
   activeAccount: FinancialAccount;
   guestGroupCount: GuestGroupCount;
   start: DateTime;
   end: DateTime;
   bundle: Bundle;
   balance: Money;
   manualPrice?: Money;
   status: ReservationStatus;
   source: ReservationSource;
   notes?: Note[];
   color?: string;
   isShadow: boolean;
   invoiceReceiver?: Customer;
   isLeisure: boolean;
   voucherDiscount?: VoucherDiscount;
   surveyAnswers?: SurveyAnswers;
   otaIdentifier?: string;
}

export type GuestGroupCount = Record<ID, number>;

export interface ReservationInfo extends Identifiable {
   serialNumber: number;
   room: Room;
   titular: Customer;
   start: DateTime;
   end: DateTime;
   status: ReservationStatus;
   color?: string;
   isLeisure?: boolean;
}

export interface ReservationHistory extends Identifiable {
   operator: string;
   titular: string;
   room: string;
   bundle: string;
   start: DateTime;
   end: DateTime;
   status: ReservationStatus;
   timestamp: DateTime;
   manualPrice?: Money;
}

import {Identifiable} from './identifiable';
import {Consumable} from './bundles/consumable';
import {Cleaning} from './cleaning';
import {includes} from '../utility/utility';
import {DateTime} from 'luxon';

export interface Room extends Identifiable {
   name: string;
   baseConsumable: Consumable;
   baseCapacity: number;
   additionalCapacity: number;
   cleaning?: Cleaning;
   blocked?: {
      start: DateTime;
      end: DateTime;
      reason: string;
   };
}

export const roomFilter = (r: Room, filter: string): boolean => {
   const fltr = filter.toLowerCase();
   return includes(fltr, r.name, r.baseConsumable.name);
};

import {Customer, CustomerType, Gender, PersonDocumentType} from '../customers/customer';
import {DateTime} from 'luxon';

export enum GemaltoValidationResult {
   invalid = 'Invalid',
   valid = 'Valid',
   warning = 'Warning',
   notValidated = 'NotValidated',
}

export interface GemaltoDate {
   Date: number;
   Month: number;
   Year: number;
}

export interface GemaltoData {
   Data: string;
   LineCount: number;
   Line1: string;
   Line2: string;
   Line3: string;
   DocId: string;
   DocType: string;
   Surname: string;
   Forename: string;
   SecondName: string;
   Forenames: string;
   DateOfBirth: GemaltoDate;
   ExpiryDate: GemaltoDate;
   IssuingState: string;
   Nationality: string;
   DocNumber: string;
   Sex: string;
   ShortSex: string;
   OptionalData1: string;
   OptionalData2: string;
   CodelineValidationResult: GemaltoValidationResult;
   ExpiredDocumentFlag: boolean;
   ImageSource: number;
}

export const gemaltoToCustomer = (data: GemaltoData): Customer => {
   return {
      type: CustomerType.individual,
      contact: {
         name: data.Forename,
         lastName: data.Surname,
      },
      idNumber: data.OptionalData2,
      document: {
         type: data.DocType === 'IDENTITY CARD' ? PersonDocumentType.card :
            PersonDocumentType.passport,
         id: data.DocNumber,
         issuedAt: expiryDateToIssued(data.ExpiryDate),
      },
      country: data.IssuingState.substring(0, 2),
      individual: {
         birthDate: dobToString(data.DateOfBirth),
         gender: data.Sex === 'Male' ? Gender.male : Gender.female,
      },
   } as Customer;
};

const expiryDateToIssued = (d: GemaltoDate): DateTime => DateTime.fromObject({
   year: d.Year + 2000 - 10,
   month: d.Month,
   day: d.Date,
});

const dobToString = (d: GemaltoDate): DateTime => DateTime.fromObject({
   year: d.Year + (d.Year > 24 ? 1900 : 2000),
   month: d.Month,
   day: d.Date,
});

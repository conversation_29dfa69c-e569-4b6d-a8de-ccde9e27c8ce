import {ID, Identifiable} from './identifiable';
import {CalendarRange, Money} from './common';
import {ReservationSource} from './reservation-source';

export interface BundleRate {
   price: Money;
}

export interface BundleEntry {
   bundleId: ID;
   bundleRate: BundleRate;
}

export interface Rate extends Identifiable {
   name: string;
   activeRanges: CalendarRange[];
   reservationSources?: ReservationSource[];
   entries: BundleEntry[];
}

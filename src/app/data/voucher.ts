import {Money} from './common';
import {Identifiable} from './identifiable';
import {ReservationSource} from './reservation-source';

export interface VoucherSource extends Identifiable {
   name: string;
   reservationSources: ReservationSource[];
}

export interface Voucher extends Identifiable {
   name: string;
   price: Money;
   multiplier: number;
}

export interface VoucherDiscount {
   voucher: Voucher;
   identifier: string;
}

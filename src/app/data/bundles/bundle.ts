import {Identifiable} from '../identifiable';
import {VATGroup} from '../VATGroup';
import {Consumable} from './consumable';
import {CustomerGroup} from '../customers/customer-group';
import {Pricing} from '../pricing';
import {includes} from '../../utility/utility';
import {AccommodationCondition} from '../customers/customer-discount';

export interface ConsumptionRule extends Identifiable {
   name: string;
   appliesPerCustomer: boolean;
   appliesForRegularBed: boolean;
   appliesForAdditionalBed: boolean;
   applicableCustomerGroups: CustomerGroup[];
}

export interface ConsumableWithRule {
   consumable: Consumable;
   rule: ConsumptionRule;
}

export interface Bundle extends Identifiable {
   name: string;
   consumableRules: ConsumableWithRule[];
   vatGroup: VATGroup;
   activePricings: Pricing[];
   fiscalName: string;
   accommodationConditions: AccommodationCondition[];
}

export const bundleFilter = (b: Bundle, filter: string): boolean => {
   const fltr = filter.toLowerCase();
   return includes(fltr, b.name, b.fiscalName);
};

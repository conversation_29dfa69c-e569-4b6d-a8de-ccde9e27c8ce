import {Identifiable} from './identifiable';
import {Customer} from './customers/customer';
import {Money} from './common';
import {DateTime} from 'luxon';

export interface FinancialAccount extends Identifiable {
   titular: Customer;
   balance?: Money;
   createdAt?: DateTime;
   deactivatedAt?: DateTime;
}

export interface FinancialAccountChain extends Identifiable {
   activeAccount: FinancialAccount;
   accounts: FinancialAccount[];
   invoiceReceiver?: Customer;
}

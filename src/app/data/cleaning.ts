import {Identifiable} from './identifiable';
import {Consumable} from './bundles/consumable';

export enum CleaningPriority {
   highest = 'Highest',
   high = 'High',
   medium = 'Medium',
   low = 'Low',
   lowest = 'Lowest',
}

export interface RoomTypeDays {
   roomType: Consumable;
   days: number;
}

export interface Cleaning extends Identifiable {
   name: string;
   periodDays: RoomTypeDays[];
   applyOnCompleted: boolean;
   priority: CleaningPriority;
}

export const getCleaningPriorityName = (cp: CleaningPriority): string => {
   switch (cp) {
      case CleaningPriority.highest:
         return 'Най-висок';
      case CleaningPriority.high:
         return 'Висок';
      case CleaningPriority.medium:
         return 'Среден';
      case CleaningPriority.low:
         return 'Нисък';
      case CleaningPriority.lowest:
         return 'Най-нисък';
   }
};

export const roomTypeDaysToString = (rtd: RoomTypeDays): string =>
   `${rtd.roomType.name}: ${rtd.days} ${rtd.days === 1 ? 'ден' : 'дни'}`;

const priorities: CleaningPriority[] = [
   CleaningPriority.highest,
   CleaningPriority.high,
   CleaningPriority.medium,
   CleaningPriority.low,
   CleaningPriority.lowest,
];

export const cmpCleaning = (a: Cleaning, b: Cleaning) =>
   priorities.indexOf(a.priority) - priorities.indexOf(b.priority);

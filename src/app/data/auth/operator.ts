import {Identifiable} from '../identifiable';
import {Credentials} from './credentials';
import {includes} from '../../utility/utility';

interface NamedType<T> {
   type: T;
   name: string;
}

export enum UserRoleType {
   manager = 'MANAGER',
   receptionist = 'RECEPTIONIST',
   taskAssignee = 'TASK_ASSIGNEE',
}

export enum Privilege {
   manualPrice = 'MANUAL_PRICE',
   bankPayments = 'BANK_PAYMENTS',
   balanceEdit = 'BALANCE_EDIT',
}

export const ROLES: Record<UserRoleType, NamedType<UserRoleType>> = {
   [UserRoleType.manager]: {type: UserRoleType.manager, name: 'Управител'},
   [UserRoleType.receptionist]: {type: UserRoleType.receptionist, name: 'Рецепционист'},
   [UserRoleType.taskAssignee]: {type: UserRoleType.taskAssignee, name: 'Камериер'}
};

export const PRIVILEGES: Record<Privilege, NamedType<Privilege>> = {
   [Privilege.manualPrice]: {
      type: Privilege.manualPrice,
      name: 'Промяна на ръчна цена',
   },
   [Privilege.bankPayments]: {
      type: Privilege.bankPayments,
      name: 'Плащане по банков път',
   },
   [Privilege.balanceEdit]: {
      type: Privilege.balanceEdit,
      name: 'Редактиране на начисления и плащания',
   }
};

export interface Operator extends Identifiable {
   code: string;
   name: string;
   role: UserRoleType;
   credentials: Credentials;
   privileges: Privilege[];
   fiscalPassword?: string;
}

export const operatorFilter = (o: Operator, filter: string): boolean => {
   const fltr = filter.toLowerCase();
   return includes(fltr, o.code, o.name, o.credentials.username);
};

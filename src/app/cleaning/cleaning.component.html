<div class="container" data-event-category="cleaning">
   <div class="actions">
      <div class="flex-row btn-row">
         <button (click)="cleanSelected()" [disabled]="selection.isEmpty()"
                 appEvent="clean_rooms"
                 color="primary" mat-raised-button>
            Маркиране като почистени
         </button>
         <button (click)="openNotes()" [disabled]="selection.isEmpty()" mat-raised-button>
            Добави бележка
         </button>
      </div>
      <button (click)="assignCleaning()" mat-raised-button>
         Начисляване на почистване
      </button>
   </div>

   <div class="mat-elevation-z3">
      <table [dataSource]="rooms" mat-table style="width: 100%">
         <ng-container matColumnDef="select">
            <th *matHeaderCellDef mat-header-cell>
               <mat-checkbox (change)="$event ? masterToggle() : null"
                             [checked]="selection.hasValue() && isAllSelected()"
                             [indeterminate]="selection.hasValue() && !isAllSelected()"
                             appEvent="mark_all"
                             color="primary">
               </mat-checkbox>
            </th>
            <td *matCellDef="let room" mat-cell>
               <mat-checkbox (change)="$event ? selection.toggle(room) : null"
                             [checked]="selection.isSelected(room)"
                             appEvent="mark_single"
                             color="primary">
               </mat-checkbox>
            </td>
         </ng-container>

         <mat-text-column headerText="Стая" name="name"/>

         <ng-container matColumnDef="cleaning">
            <th *matHeaderCellDef mat-header-cell>Почистване</th>
            <td *matCellDef="let room" mat-cell>{{room.cleaning.name}}</td>
         </ng-container>

         <ng-container matColumnDef="notes">
            <th *matHeaderCellDef mat-header-cell>Бележки</th>
            <td *matCellDef="let room" mat-cell style="
                  max-width: 30em;">
               <div style="max-width: 20em;white-space: normal;word-wrap: break-word;">
                  @if (cleaningNotes[room.id]; as notes) {
                     <ul>
                        @for (note of notes; track note.id) {
                           <li>{{note.content}}</li>
                        }
                     </ul>
                  }
               </div>

            </td>
         </ng-container>

         <tr *matHeaderRowDef="columns" mat-header-row></tr>
         <tr *matRowDef="let row; columns: columns;" mat-row></tr>

         <tr *matNoDataRow>
            <td [colSpan]="columns.length" class="text-center">Всички стаи са чисти</td>
         </tr>
      </table>
   </div>
</div>

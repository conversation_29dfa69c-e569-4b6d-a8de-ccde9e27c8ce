import {Component, inject} from '@angular/core';
import {RoomService} from '../services/room.service';
import {Room} from '../data/room';
import {SelectionModel} from '@angular/cdk/collections';
import {forkJoin, switchMap, tap} from 'rxjs';
import {cmpName, commaJoin} from '../utility/utility';
import {NotificationService} from '../services/notification.service';
import {MatDialog} from '@angular/material/dialog';
import {
   AssignCleaningDialogComponent
} from '../dialogs/assign-cleaning-dialog/assign-cleaning-dialog.component';
import {filter} from 'rxjs/operators';
import {Cleaning} from '../data/cleaning';
import {
   DynamicFormDialogComponent
} from '../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';
import {NewNoteFormComponent} from '../forms/new-note-form/new-note-form.component';
import {NotesService} from '../services/notes.service';
import {ID} from '../data/identifiable';
import {Note} from '../data/notes';

@Component({
   selector: 'app-cleaning',
   templateUrl: './cleaning.component.html',
   styles: [`
      .container {
         padding: 100px;

         .actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
         }
      }
   `],
   standalone: false
})
export class CleaningComponent {
   rooms: Room[] = [];
   columns = ['select', 'name', 'cleaning', 'notes'];
   selection = new SelectionModel<Room>(true, []);
   cleaningNotes: Record<ID, Note[]> = {};

   private sRoom = inject(RoomService);
   private sNotification = inject(NotificationService);
   private sNotes = inject(NotesService);
   private dialog = inject(MatDialog);

   constructor() {
      this.renewData();
   }

   openNotes() {
      this.dialog.open(DynamicFormDialogComponent, {
         data: {form: NewNoteFormComponent, title: 'Добавяне на бележка'}
      }).afterClosed()
         .pipe(filter(result => !!result))
         .subscribe(note => this.addNote(note));
   }

   isAllSelected(): boolean {
      return this.selection.selected.length === this.rooms.length;
   }

   masterToggle(): void {
      if (this.isAllSelected()) {
         this.selection.clear();
      } else {
         this.selection.select(...this.rooms);
      }
   }

   cleanSelected() {
      const rooms = this.selection.selected;
      const names = commaJoin(rooms, 'name');

      this.sNotification.openConfirmationDialog({
         title: 'Почистване на стаи',
         description: `Искате ли да маркирате стаи ${names} като почистени?`,
         yesText: 'Маркирай като почистени!',
      }).pipe(
         switchMap(() => forkJoin(rooms.map(r => this.sRoom.cleanRoom(r.id))))
      ).subscribe(() => {
         this.selection.clear();
         this.renewData();
      });
   }

   assignCleaning(): void {
      this.dialog.open(AssignCleaningDialogComponent).afterClosed().pipe(
         filter(result => !!result),
         switchMap(({rooms: rs, cleaning: c}: { rooms: Room[]; cleaning: Cleaning }) =>
            forkJoin(rs.map(r => this.sRoom.assignCleaning(r.id, c.id)))
         )
      ).subscribe(() => this.renewData());
   }

   private renewData(): void {
      this.sRoom.getAll()
         .subscribe(rs => this.rooms = rs.filter(r => !!r.cleaning).sort(cmpName));

      this.sRoom.getCleaningNotes().subscribe(notes => this.cleaningNotes = notes);
   }

   private addNote(noteText: string) {
      const roomIds = this.selection.selected.map(room => room.id);
      this.selection.clear();
      this.sNotes.batchAdd({content: noteText, parents: roomIds})
         .pipe(tap(() => this.sRoom.invalidateCache()))
         .subscribe(() => this.renewData());
   }
}

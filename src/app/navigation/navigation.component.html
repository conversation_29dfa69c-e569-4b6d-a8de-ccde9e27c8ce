@if (showExpiryBanner()) {
   <div class="banner mat-app-background">
      ⚠️ Лицензът ви изтича в {{expiryDate() | dateTime:false}}
      Моля направете плащане, за да не бъде преустановен достъпът ви до системата. ⚠️
   </div>
}
<mat-toolbar class="toolbar">
   <div>
      @for (element of toolbarItems; track element.link) {
         <button [color]="router.url === element.link ? 'primary' : undefined"
                 [routerLink]="element.link"
                 class="toolbar-button mat-primary" mat-button>
            <mat-icon>{{element.icon}}</mat-icon>
            {{element.name}}
         </button>
      }
      @if (moreItems.length > 0) {
         <button [matMenuTriggerFor]="moreMenu" mat-button>
            <mat-icon>expand_more</mat-icon>
            Други
         </button>
      }
   </div>
   <button (click)="nextMode()" extended mat-fab>
      <mat-icon>
         @switch (colorMode()) {
            @case (ColorMode.light) {
               light_mode
            }
            @case (ColorMode.dark) {
               dark_mode
            }
            @case (ColorMode.auto) {
               brightness_auto
            }
         }
      </mat-icon>
      {{username()}}
   </button>

   <div>
      @if (sFeature.fiscal()) {
         <app-fiscal-button/>
      }
      @if (sLoading.loading()) {
         <mat-spinner color="primary" diameter="24" style="margin: 12px;"/>
      } @else {
         <button [matMenuTriggerFor]="topMenu" mat-icon-button>
            <mat-icon>settings</mat-icon>
         </button>
      }
   </div>
</mat-toolbar>

<mat-menu #topMenu>
   <span mat-menu-item>Overview {{version}}</span>
   @for (item of settingsMenu; track item.name) {
      @switch (item.type) {
         @case (mit.anchor) {
            <a [href]="item.link" mat-menu-item
               target="_blank">
               <mat-icon>{{item.icon}}</mat-icon>
               <span>{{item.name}}</span>
            </a>
         }
         @case (mit.routerLink) {
            <button [routerLink]="item.link" mat-menu-item>
               <mat-icon>{{item.icon}}</mat-icon>
               <span>{{item.name}}</span>
            </button>
         }
      }
   }
</mat-menu>

<mat-menu #moreMenu>
   @for (item of moreItems; track item.name) {
      <button [routerLink]="item.link" mat-menu-item>
         <mat-icon>{{item.icon}}</mat-icon>
         <span>{{item.name}}</span>
      </button>
   }
</mat-menu>

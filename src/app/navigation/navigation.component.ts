import {Component, computed, effect, inject, Signal, signal} from '@angular/core';
import {Router} from '@angular/router';
import {VersioningService} from '../services/versioning.service';
import {FeatureService} from '../services/feature.service';
import {AuthService} from '../auth/auth.service';
import {LoadingService} from './loading.service';
import {HotelService} from '../services/hotel.service';
import {DateTime} from 'luxon';

enum ColorMode {
   _key = 'color_mode',
   _darkClass = 'dark-mode',
   light = 'light',
   dark = 'dark',
   auto = 'auto',
}

enum MenuItemType {
   routerLink,
   anchor,
}

interface MenuItemBase {
   name: string;
   icon: string;
   link: string;
}

interface LinkMenuItem extends MenuItemBase {
   type: MenuItemType.routerLink;
}

interface AnchorMenuItem extends MenuItemBase {
   type: MenuItemType.anchor;
}

type MenuItem = LinkMenuItem | AnchorMenuItem;

@Component({
   selector: 'app-navigation',
   templateUrl: './navigation.component.html',
   styleUrls: ['./navigation.component.scss'],
   standalone: false
})
export class NavigationComponent {
   toolbarItems: MenuItemBase[] = [
      {
         name: 'Резервации',
         icon: 'insert_invitation',
         link: '/reservation'
      },
      {
         name: 'Справки',
         icon: 'insights',
         link: '/query'
      },
      {
         name: 'Почиствания',
         icon: 'cleaning_services',
         link: '/cleaning'
      },
      {
         name: 'Клиенти',
         icon: 'people',
         link: '/customer'
      },
      {
         name: 'Разговори',
         icon: 'phone_callback',
         link: '/leads'
      },
   ];

   moreItems: MenuItemBase[] = [
      {
         name: 'Анулации',
         icon: 'event_busy',
         link: '/cancellations'
      },
      {
         name: 'Фактури',
         icon: 'receipt_long',
         link: '/invoices'
      },
   ];

   settingsMenu: MenuItem[] = [
      {
         type: MenuItemType.routerLink,
         name: 'Настройки',
         icon: 'settings',
         link: '/settings'
      },
      {
         type: MenuItemType.anchor,
         name: 'Актуализации',
         icon: 'new_releases',
         link: 'https://help.overview-pms.com/releases/',
      },
      {
         type: MenuItemType.anchor,
         name: 'Документация',
         icon: 'description',
         link: 'https://help.overview-pms.com/docs/',
      },
      {
         type: MenuItemType.routerLink,
         name: 'Изход',
         icon: 'input',
         link: '/logout'
      },
   ];

   username: Signal<string>;
   version: string;

   colorMode = signal(localStorage.getItem(ColorMode._key) ?? ColorMode.auto);
   prefersDark = signal(false);

   expiryDate = signal<DateTime | undefined>(undefined);
   showExpiryBanner = computed(() => {
      const ex = this.expiryDate();
      return ex ? ex.diff(DateTime.local()).as('days') < 5 : false;
   });

   ColorMode = ColorMode;
   mit = MenuItemType;

   router = inject(Router);
   sFeature = inject(FeatureService);
   sLoading = inject(LoadingService);

   private sAuth = inject(AuthService);
   private sVersioning = inject(VersioningService);
   private sHotel = inject(HotelService);

   constructor() {
      this.username = computed(() => this.sAuth.current()?.name ?? '');
      this.version = this.sVersioning.currentVersion;
      this.updateLicenseExpiryDate();

      const prefersDarkQuery = window.matchMedia('(prefers-color-scheme: dark)');
      if (prefersDarkQuery.matches) {
         this.prefersDark.set(true);
      }
      prefersDarkQuery.onchange = (e) => this.prefersDark.set(e.matches);

      const realMode = computed(() => {
         const colorMode = this.colorMode();
         const prefersDark = this.prefersDark();

         if (colorMode === ColorMode.auto) {
            return prefersDark ? ColorMode.dark : ColorMode.light;
         }
         return colorMode;
      });

      const body = document.getElementById('body');
      effect(() => {
         const mode = realMode();

         if (mode === ColorMode.dark) {
            body!.classList.add(ColorMode._darkClass);
         } else {
            body!.classList.remove(ColorMode._darkClass);
         }
      });

      effect(() => localStorage.setItem(ColorMode._key, this.colorMode().toString()));
   }

   nextMode(): void {
      this.colorMode.update(s => {
         switch (s) {
            case ColorMode.light:
               return ColorMode.dark;
            case ColorMode.dark:
               return ColorMode.auto;
            case ColorMode.auto:
               return ColorMode.light;
            default:
               return ColorMode.light;
         }
      });
   }

   private updateLicenseExpiryDate(): void {
      this.sHotel.getCurrentNoCache().subscribe(({expiryDate}) =>
         expiryDate && this.expiryDate.set(expiryDate));

      const tenMinutes = 600_000;
      setTimeout(() => this.updateLicenseExpiryDate(), tenMinutes);
   }
}

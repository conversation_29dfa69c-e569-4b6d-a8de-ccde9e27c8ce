import {Component, inject, Input, OnInit} from '@angular/core';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {ReactiveFormsModule, UntypedFormArray, UntypedFormBuilder} from '@angular/forms';
import {Cleaning, CleaningPriority, RoomTypeDays} from '../../data/cleaning';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatSelectModule} from '@angular/material/select';
import {ConsumableService} from '../../services/consumable.service';
import {ConsumableType} from '../../data/bundles/consumable';
import {DataTableService} from '../../settings/data-table/data-table.service';
import {cmpName} from '../../utility/utility';

@Component({
   selector: 'app-cleaning-form',
   imports: [
      MatFormFieldModule,
      MatInputModule,
      MatSlideToggleModule,
      ReactiveFormsModule,
      MatSelectModule
   ],
   template: `
      <form [formGroup]="form" class="inline-two-column-grid">
         <mat-form-field>
            <mat-label>Име</mat-label>
            <input autocomplete="off" formControlName="name" matInput type="text">
         </mat-form-field>
         <mat-form-field>
            <mat-label>Приоритет</mat-label>
            <mat-select formControlName="priority">
               <mat-option [value]="cp.highest">Най-висок</mat-option>
               <mat-option [value]="cp.high">Висок</mat-option>
               <mat-option [value]="cp.medium">Среден</mat-option>
               <mat-option [value]="cp.low">Нисък</mat-option>
               <mat-option [value]="cp.lowest">Най-нисък</mat-option>
            </mat-select>
         </mat-form-field>
         <div></div>
         <div class="end-row">
            <mat-slide-toggle formControlName="applyOnCompleted"
                              labelPosition="before">
               Начисли след напускане
            </mat-slide-toggle>
         </div>
         <div class="native-input-table colspan-2" formArrayName="periodDays">
            <div class="row header">
               <span>Тип стая</span>
               <span>Период</span>
            </div>
            @for (rt of periodDays.controls; track rt) {
               <div class="row data" [formGroupName]="$index">
                  <span>{{rt.value.roomType.name}}</span>
                  <input autocomplete="off" class="mat-app-background" type="number"
                         min="0" matInput formControlName="days">
               </div>
            }
         </div>
      </form>
   `,
   styles: ``
})
export class CleaningFormComponent implements OnInit {
   @Input() data?: Cleaning;
   @Input() edit = false;

   form = this.fb.group({
      id: '',
      name: '',
      periodDays: this.fb.array([]),
      applyOnCompleted: false,
      priority: CleaningPriority.highest,
   });
   cp = CleaningPriority;

   private sConsumable = inject(ConsumableService);
   private sDataTable = inject(DataTableService);

   constructor(private fb: UntypedFormBuilder) {
   }


   get valid(): boolean {
      return this.form.valid;
   }

   get value(): boolean {
      const {periodDays, ...result} = this.form.value;
      result.periodDays = periodDays.filter((rtd: RoomTypeDays) => rtd.days);
      return result;
   }

   get periodDays(): UntypedFormArray {
      return this.form.get('periodDays') as UntypedFormArray;
   }

   ngOnInit(): void {
      if (this.data) {
         const {periodDays: _, ...data} = this.data;
         this.form.patchValue(data);
      }

      this.sConsumable.getAllOfType(ConsumableType.room)
         .subscribe(cs => cs.sort(cmpName).forEach(c =>
            this.periodDays.push(this.fb.group({
               roomType: c,
               days: this.data?.periodDays.find(pd => pd.roomType.id === c.id)?.days,
            }))));

      this.form.valueChanges.subscribe(() => this.sDataTable.result = this.value);
   }
}

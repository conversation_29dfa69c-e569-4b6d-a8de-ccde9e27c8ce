<form [formGroup]="form">
   <div class="two-column-grid">
      <div style="margin-right: 16px;">
         <mat-form-field class="max-width">
            <mat-icon matPrefix>spa</mat-icon>
            <mat-label>Име</mat-label>
            <input autocomplete="off" formControlName="name" matInput type="text">
         </mat-form-field>
         <div class="flex-row">
            <mat-form-field class="max-width">
               <mat-icon matPrefix>receipt_long</mat-icon>
               <mat-label>Фискално име</mat-label>
               <input autocomplete="off" formControlName="fiscalName" matInput
                      type="text">
            </mat-form-field>
            <mat-form-field style="max-width: 30%;">
               <mat-label>ДДС</mat-label>
               <mat-select [compareWith]="compareVATGroups" formControlName="vatGroup">
                  @for (vatGroup of vatGroups$ | async; track vatGroup.id) {
                     <mat-option [value]="vatGroup">{{vatGroup | vat}}</mat-option>
                  }
               </mat-select>
            </mat-form-field>
         </div>
         <app-price-select [control]="activePricings" class="max-width" multiple/>
      </div>
      <app-consumable-amounts-input [formArray]="consumableRules"/>
   </div>
   <div>
      <div class="apart-row">
         <h2>Условия на пакета</h2>
         <button (click)="addAccommodationCondition()" mat-icon-button>
            <mat-icon>add</mat-icon>
         </button>
      </div>
      <mat-accordion multi="true">
         @for (control of accommodationConditions.controls; track control) {
            <mat-expansion-panel [expanded]="$index === expandedConditionIndex">
               <mat-expansion-panel-header>
                  <mat-panel-title>
                     {{getCondition($index)?.type | accommodationConditionType}}

                     <button (click)="removeCondition($index)" mat-icon-button
                             style="margin-right: 16px;" color="warn">
                        <mat-icon>delete</mat-icon>
                     </button>
                  </mat-panel-title>
               </mat-expansion-panel-header>
               <app-accommodation-condition-input [formControl]="$any(control)"/>
            </mat-expansion-panel>
         }
      </mat-accordion>
   </div>
</form>


import {Component, inject, Input, OnInit} from '@angular/core';
import {Bundle} from '../../data/bundles/bundle';
import {
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormControl,
   ValidatorFn,
   Validators
} from '@angular/forms';
import {VATGroup} from 'src/app/data/VATGroup';
import {Observable} from 'rxjs';
import {VATGroupService} from 'src/app/services/vat-group.service';
import {equalIdentifiables} from 'src/app/utility/utility';
import {DataTableService} from '../../settings/data-table/data-table.service';
import {AccommodationCondition} from '../../data/customers/customer-discount';

const validateConsumables: ValidatorFn = (control) => {
   for (const ca of control.value) {
      if (!ca.consumable || !ca.rule) {
         return {invalidElement: ca};
      }
   }

   return null;
};

@Component({
   selector: 'app-bundle-form',
   templateUrl: './bundle-form.component.html',
   standalone: false
})
export class BundleFormComponent implements OnInit {
   @Input() data?: Bundle;
   @Input() edit = false;

   form = this.fb.group({
      id: '',
      name: ['', Validators.required],
      consumableRules: this.fb.array([], [Validators.required, validateConsumables]),
      vatGroup: ['', Validators.required],
      activePricings: this.fb.control([], Validators.required),
      fiscalName: ['', Validators.required],
      accommodationConditions: this.fb.array([])
   });

   vatGroups$: Observable<VATGroup[]> | undefined;
   compareVATGroups = equalIdentifiables;

   expandedConditionIndex: number | undefined = undefined;

   private sVat = inject(VATGroupService);
   private sDataTable = inject(DataTableService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get consumableRules(): UntypedFormArray {
      return this.form.get('consumableRules') as UntypedFormArray;
   }

   get accommodationConditions(): UntypedFormArray {
      return this.form.get('accommodationConditions') as UntypedFormArray;
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Bundle {
      this.consumableRules.updateValueAndValidity();
      return this.form.value;
   }

   get activePricings(): UntypedFormControl {
      return this.form.get('activePricings') as UntypedFormControl;
   }

   ngOnInit(): void {
      this.vatGroups$ = this.sVat.getAll();
      this.form.valueChanges.subscribe(v => this.sDataTable.result = v);

      if (this.data) {
         const {consumableRules, accommodationConditions, ...rest} = this.data;
         this.form.patchValue(rest);
         consumableRules?.forEach(
            cr => this.consumableRules.controls.push(this.fb.group(cr)));
         accommodationConditions.forEach(
            condition => this.accommodationConditions.push(this.fb.control(condition))
         );
      }
   }

   addAccommodationCondition(data?: AccommodationCondition) {
      this.expandedConditionIndex = this.accommodationConditions.length;
      this.accommodationConditions.push(this.fb.control(data));
   }

   getCondition(index: number): AccommodationCondition {
      return this.accommodationConditions.at(index)?.value as AccommodationCondition;
   }

   removeCondition(index: number) {
      this.accommodationConditions.removeAt(index);
   }
}

import {
   ChangeDetectorRef,
   Component,
   Input,
   OnChanges,
   OnInit,
   SimpleChanges
} from '@angular/core';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {Bank} from 'src/app/data/hotel';

@Component({
   selector: 'app-bank-form',
   templateUrl: './bank-form.component.html',
   styleUrls: ['./bank-form.component.scss'],
   standalone: false
})
export class BankFormComponent implements OnInit, OnChanges {
   @Input() data: Partial<Bank> = {};

   form = this.fb.group({
      bankName: ['', Validators.required],
      iban: ['', Validators.required],
      bic: ['', Validators.required],
   });

   constructor(private fb: UntypedFormBuilder,
               private cd: ChangeDetectorRef) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): any {
      return this.form.value;
   }

   get isChanged(): boolean {
      return this.form.dirty;
   }

   set bank(value: Bank) {
      this.data = value;
      this.renewData();
      this.form.markAsPristine();
   }

   ngOnInit(): void {
      this.renewData();
   }

   ngOnChanges(changes: SimpleChanges): void {
      if (changes.data) {
         this.renewData();
      }
   }

   private renewData() {
      if (!this.data) {
         return;
      }

      this.form.patchValue(this.data);
      this.cd.detectChanges();
   }
}

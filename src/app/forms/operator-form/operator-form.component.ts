import {Component, inject, Input, OnInit} from '@angular/core';
import {
   AbstractControl,
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   ValidationErrors,
   Validators
} from '@angular/forms';
import {Operator, PRIVILEGES, ROLES} from 'src/app/data/auth/operator';
import {OperatorService} from '../../services/operator.service';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
   selector: 'app-operator-form',
   templateUrl: './operator-form.component.html',
   styles: [`
      .operator-form {
         display: flex;
         flex-direction: row;
         align-items: center;
         padding: 16px;

         & > * {
            padding: 0 16px;
         }
      }
   `],
   standalone: false
})
export class OperatorFormComponent implements OnInit {
   @Input() data?: Operator;
   @Input() edit = false;

   fb = inject(UntypedFormBuilder);
   form = this.fb.group({
      id: '',
      code: ['', Validators.required],
      name: ['', Validators.required],
      credentials: this.fb.group({
         username: [''],
         password: [''],
      }),
      role: ['', Validators.required],
      privileges: [[]],
      fiscalPassword: null,
   });

   hide = true;

   private usernames: string[] = [];
   private sOperator = inject(OperatorService);
   private sDataTable = inject(DataTableService);

   get usernameExists(): boolean {
      const u = this.username;
      return u && u.invalid && (u.dirty || u.touched) && u.errors?.['usernameClash'];
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): any {
      const {credentials, ...result} = this.form.getRawValue();
      if (credentials && credentials.username && credentials.password) {
         result.credentials = credentials;
      }

      return result;
   }

   get credentials(): UntypedFormGroup {
      return this.form.get('credentials') as UntypedFormGroup;
   }

   get username(): UntypedFormControl {
      return this.credentials.get('username') as UntypedFormControl;
   }

   get password(): UntypedFormControl {
      return this.credentials.get('password') as UntypedFormControl;
   }

   get roles() {
      return Object.values(ROLES);
   }

   get privileges() {
      return Object.values(PRIVILEGES);
   }

   ngOnInit(): void {
      this.sOperator.getAllUsernames().subscribe(usernames => {
         this.usernames = usernames;
      });

      this.username.addValidators((c) => this.uniqueUsername(c));
      if (this.edit && this.data) {
         this.form.patchValue(this.data);
         this.username.disable();
         this.password.disable();
      } else {
         this.username.addValidators(Validators.required);
         this.password.addValidators(Validators.required);
      }

      this.form.valueChanges.subscribe(() => this.sDataTable.result = this.value);
   }

   private uniqueUsername(control: AbstractControl): ValidationErrors | null {
      if (this.usernames.includes(control.value)) {
         return {usernameClash: true};
      } else {
         return null;
      }
   }
}

<form [formGroup]="form" class="operator-form">
   <div class="flex-column">
      <mat-form-field>
         <mat-icon matPrefix>account_circle</mat-icon>
         <mat-label>Име</mat-label>
         <input autocomplete="off" formControlName="name" matInput type="text">
      </mat-form-field>
      <mat-form-field>
         <mat-icon matPrefix>fact_check</mat-icon>
         <mat-label>Роля</mat-label>
         <mat-select formControlName="role">
            @for (role of roles; track role.type) {
               <mat-option [value]="role.type">{{role.name}}</mat-option>
            }
         </mat-select>
      </mat-form-field>
      <mat-form-field>
         <mat-icon matPrefix>ballot</mat-icon>
         <mat-label>Права</mat-label>
         <mat-select formControlName="privileges" multiple>
            @for (privilege of privileges; track privilege.type) {
               <mat-option [value]="privilege.type">{{privilege.name}}</mat-option>
            }
         </mat-select>
      </mat-form-field>
   </div>
   <div class="flex-column">
      <mat-form-field>
         <mat-icon matPrefix>fingerprint</mat-icon>
         <mat-label>Код на оператора за ФУ</mat-label>
         <input autocomplete="off" formControlName="code" matInput type="text">
      </mat-form-field>
      <mat-form-field>
         <mat-icon matPrefix>point_of_sale</mat-icon>
         <mat-label>Парола за ФУ</mat-label>
         <input autocomplete="off" formControlName="fiscalPassword" matInput type="text">
      </mat-form-field>
   </div>
   <div class="flex-column" formGroupName="credentials">
      <mat-form-field>
         <mat-icon matPrefix>person</mat-icon>
         <mat-label>Потребителско име</mat-label>
         <input autocomplete="off" formControlName="username" matInput type="text">
         @if (usernameExists) {
            <mat-error>Вече съществува такъв потребител</mat-error>
         }
      </mat-form-field>
      <mat-form-field>
         <mat-label>Парола</mat-label>
         <input [type]="hide ? 'password' : 'text'" autocomplete="off"
                formControlName="password" matInput>
         <button (click)="hide = !hide" mat-icon-button matSuffix>
            <mat-icon>
               @if (hide) {
                  visibility
               } @else {
                  visibility_off
               }
            </mat-icon>
         </button>
      </mat-form-field>
      @if (edit) {
         <mat-slide-toggle
            (change)="$event.checked ? password.enable() : password.disable()"
            color="primary">
            Промяна на парола
         </mat-slide-toggle>
      }
   </div>
</form>

import {AfterViewInit, Component, inject, Input, OnInit} from '@angular/core';
import {Purchase} from '../../data/purchase';
import {
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {moneyFormGroup} from '../../utility/form-utility';
import {ID} from 'src/app/data/identifiable';
import {validMoney} from '../../utility/money-utility';
import {Money} from '../../data/common';
import {Observable, of} from 'rxjs';
import {debounceTime, filter, switchMap} from 'rxjs/operators';
import {DEBOUNCE_TIME} from '../../utility/utility';
import {PurchaseService} from '../../services/purchase.service';

@Component({
   selector: 'app-purchase-form',
   templateUrl: './purchase-form.component.html',
   standalone: false
})
export class PurchaseFormComponent implements OnInit, AfterViewInit {
   @Input() data?: Purchase;
   @Input() account!: ID;

   form = this.fb.group({
      id: '',
      account: ['', Validators.required],
      bundle: ['', Validators.required],
      quantity: [1, [Validators.required, Validators.min(1)]],
      price: moneyFormGroup(this.fb),
      description: ['', Validators.maxLength(255)]
   });

   price$?: Observable<Money>;

   private sPurchase = inject(PurchaseService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Purchase {
      const {price, ...result} = this.form.value;
      if (validMoney(price)) {
         result.price = price;
      }

      return result;
   }

   get quantity(): UntypedFormControl {
      return this.form.get('quantity') as UntypedFormControl;
   }

   get bundle(): UntypedFormControl {
      return this.form.get('bundle') as UntypedFormControl;
   }

   get price(): UntypedFormGroup {
      return this.form.get('price') as UntypedFormGroup;
   }

   ngOnInit(): void {
      this.price$ = this.form.valueChanges.pipe(
         debounceTime(DEBOUNCE_TIME * 2),
         filter(p => p && p.bundle && p.quantity),
         switchMap(p => {
            if (validMoney(p.price)) {
               return of(p.price);
            } else {
               delete p.price;
               return this.sPurchase.getPrice(p.bundle.id, p.quantity);
            }
         }),
      );

      this.quantity.valueChanges.subscribe(() => this.price.patchValue({amount: ''}));
   }

   ngAfterViewInit(): void {
      if (this.data) {
         this.form.patchValue(this.data);
      } else {
         this.form.patchValue({account: this.account});
      }
   }
}

<form [formGroup]="form" class="column-form">
   <mat-form-field>
      <mat-label>Тип</mat-label>
      <mat-icon matPrefix>discount</mat-icon>
      <mat-select formControlName="type">
         @for (type of cdtKeys; track type) {
            <mat-option [value]="type">{{type | accommodationConditionType}}</mat-option>
         }
      </mat-select>
   </mat-form-field>
   <div class="flex-column">
      @switch (type) {
         @case (cdt.reservationDuration) {
            <ng-container formGroupName="duration">
               <mat-form-field>
                  <mat-label>Минимален брой нощувки</mat-label>
                  <input autocomplete="off" formControlName="minDuration" matInput
                         type="number">
               </mat-form-field>
               <mat-form-field>
                  <mat-label>Максимален брой нощувки</mat-label>
                  <input autocomplete="off" formControlName="maxDuration" matInput
                         type="number">
               </mat-form-field>
               <mat-form-field>
                  <mat-label>Резервация направена от</mat-label>
                  <input [matDatepicker]="startPicker" formControlName="dateStart"
                         matInput>
                  <mat-datepicker-toggle [for]="startPicker" matSuffix>
                  </mat-datepicker-toggle>
                  <mat-datepicker #startPicker></mat-datepicker>
               </mat-form-field>
               <mat-form-field>
                  <mat-label>Резервация направена до</mat-label>
                  <input [matDatepicker]="endPicker" formControlName="dateEnd" matInput>
                  <mat-datepicker-toggle [for]="endPicker" matSuffix>
                  </mat-datepicker-toggle>
                  <mat-datepicker #endPicker></mat-datepicker>
               </mat-form-field>
            </ng-container>
         }
         @case (cdt.oldCustomer) {
            <ng-container formGroupName="oldCustomer">
               <mat-form-field>
                  <mat-label>Минимален брой години</mat-label>
                  <input autocomplete="off" formControlName="minYears" matInput
                         type="number">
               </mat-form-field>
               <mat-form-field>
                  <mat-label>Максимален брой години</mat-label>
                  <input autocomplete="off" formControlName="maxYears" matInput
                         type="number">
               </mat-form-field>
            </ng-container>
         }
         @case (cdt.customerCount) {
            <ng-container formGroupName="customerCount">
               <mat-form-field>
                  <mat-label>Минимален брой гости</mat-label>
                  <input autocomplete="off" formControlName="minCount" matInput
                         type="number">
               </mat-form-field>
               <mat-form-field>
                  <mat-label>Максимален брой гости</mat-label>
                  <input autocomplete="off" formControlName="maxCount" matInput
                         type="number">
               </mat-form-field>
            </ng-container>
         }
         @case (cdt.roomConsumable) {
            <ng-container formGroupName="roomConsumable">
               <mat-form-field>
                  <mat-label>Тип стая</mat-label>
                  <mat-icon matPrefix>king_bed</mat-icon>
                  <mat-select formControlName="roomConsumable">
                     @for (consumable of roomConsumables; track consumable.id) {
                        <mat-option [value]="consumable.id">
                           {{consumable.name}}
                        </mat-option>
                     }
                  </mat-select>
               </mat-form-field>
            </ng-container>
         }
         @case (cdt.reservationSources) {
            <ng-container formGroupName="reservationSources">
               <mat-form-field>
                  <mat-label>Източници на резервация</mat-label>
                  <mat-icon matPrefix>source</mat-icon>
                  <mat-select formControlName="sources" multiple>
                     @for (source of sources; track source.id) {
                        <mat-option [value]="source.id">{{source.name}}</mat-option>
                     }
                  </mat-select>
               </mat-form-field>
            </ng-container>
         }
      }
   </div>
</form>

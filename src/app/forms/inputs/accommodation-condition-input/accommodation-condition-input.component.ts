import {Component, forwardRef, inject, OnInit} from '@angular/core';
import {
   AccommodationCondition,
   CustomerDiscountType
} from '../../../data/customers/customer-discount';
import {
   ControlValueAccessor,
   NG_VALUE_ACCESSOR,
   UntypedFormBuilder,
   Validators
} from '@angular/forms';
import {Consumable, ConsumableType} from '../../../data/bundles/consumable';
import {ConsumableService} from '../../../services/consumable.service';
import {ReservationSourceService} from '../../../services/reservation-source.service';
import {ReservationSource} from '../../../data/reservation-source';

@Component({
   selector: 'app-accommodation-condition-input',
   templateUrl: './accommodation-condition-input.component.html',
   providers: [
      {
         provide: NG_VALUE_ACCESSOR,
         useExisting: forwardRef(() => AccommodationConditionInputComponent),
         multi: true,
      },
   ],
   standalone: false
})
export class AccommodationConditionInputComponent implements OnInit,
   ControlValueAccessor {

   roomConsumables: Consumable[] = [];
   sources: ReservationSource[] = [];

   form = this.fb.group({
      type: CustomerDiscountType.reservationDuration,
      duration: this.fb.group({
         minDuration: null,
         maxDuration: null,
         dateStart: null,
         dateEnd: null
      }),
      oldCustomer: this.fb.group({
         minYears: '',
         maxYears: ''
      }),
      customerCount: this.fb.group({
         minCount: '',
         maxCount: ''
      }),
      roomConsumable: this.fb.group({
         roomConsumable: null
      }),
      reservationSources: this.fb.group({
         sources: this.fb.control([], Validators.required)
      })
   });

   cdt = CustomerDiscountType;
   cdtKeys: CustomerDiscountType[] = Object.values(CustomerDiscountType);

   private sConsumable = inject(ConsumableService);
   private sReservationSource = inject(ReservationSourceService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get type(): CustomerDiscountType {
      return this.form!.get('type')!.value;
   }

   get value(): AccommodationCondition {
      const accommodationCondition = this.form.value;

      const {type} = accommodationCondition;
      const result = {type};

      if (type === CustomerDiscountType.reservationDuration) {
         Object.assign(result, accommodationCondition.duration);
      } else if (type === CustomerDiscountType.oldCustomer) {
         Object.assign(result, accommodationCondition.oldCustomer);
      } else if (type === CustomerDiscountType.customerCount) {
         Object.assign(result, accommodationCondition.customerCount);
      } else if (type === CustomerDiscountType.roomConsumable) {
         Object.assign(result, accommodationCondition.roomConsumable);
      } else if (type === CustomerDiscountType.reservationSources) {
         Object.assign(result, accommodationCondition.reservationSources);
      } else {
         throw Error(`Unhandled customer discount type ${type}`);
      }

      return result;
   }

   ngOnInit(): void {
      this.sConsumable.getAllOfType(ConsumableType.room)
         .subscribe(rcs => this.roomConsumables = rcs);

      this.sReservationSource.getAll().subscribe(ss => this.sources = ss);
   }

   registerOnChange(fn: (condition: AccommodationCondition) => void): void {
      this.form.valueChanges.subscribe(() => fn(this.value));
   }

   registerOnTouched(_: any): void {
   }

   writeValue(value: AccommodationCondition | null): void {
      if (value) {
         const {type, ...rest} = value;
         const formValue = {type};

         if (type === CustomerDiscountType.reservationDuration) {
            Object.assign(formValue, {duration: rest});
         } else if (type === CustomerDiscountType.oldCustomer) {
            Object.assign(formValue, {oldCustomer: rest});
         } else if (type === CustomerDiscountType.customerCount) {
            Object.assign(formValue, {customerCount: rest});
         } else if (type === CustomerDiscountType.roomConsumable) {
            Object.assign(formValue, {roomConsumable: rest});
         } else if (type === CustomerDiscountType.reservationSources) {
            Object.assign(formValue, {reservationSources: rest});
         } else {
            throw Error(`Unhandled customer discount type ${type}`);
         }

         this.form.patchValue(formValue);
      }
   }
}

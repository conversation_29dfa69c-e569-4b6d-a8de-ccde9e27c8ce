import {booleanAttribute, Component, Input} from '@angular/core';
import {UntypedFormGroup} from '@angular/forms';
import {MatFormFieldAppearance} from '@angular/material/form-field';

@Component({
   selector: 'app-money-input',
   templateUrl: './money-input.component.html',
   styles: [`
      .amount-input {
         width: 150px;
      }
   `],
   standalone: false
})
export class MoneyInputComponent {
   @Input() group!: UntypedFormGroup;
   @Input() label = 'Цена';
   @Input() appearance: MatFormFieldAppearance = 'outline';
   @Input() info?: string;
   @Input() min?: string;
   @Input({transform: booleanAttribute}) wide?: boolean;
}

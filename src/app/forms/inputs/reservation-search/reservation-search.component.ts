import {AfterViewInit, Component, inject, Input, ViewChild} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';
import {ReservationService} from '../../../services/reservation.service';
import {debounceTime, filter, tap} from 'rxjs/operators';
import {
   AUTO_FOCUS_TIME,
   dateCmp,
   DEBOUNCE_TIME,
   equalIdentifiables
} from '../../../utility/utility';
import {forkJoin, of, switchMap} from 'rxjs';
import {Reservation, ReservationInfo} from '../../../data/reservation';
import {FinancialAccount} from '../../../data/financial-account';
import {FinancialAccountService} from '../../../services/financial-account.service';
import {MatSelect} from '@angular/material/select';
import {MatSelectSearchComponent} from 'ngx-mat-select-search';

@Component({
   selector: 'app-reservation-search',
   templateUrl: './reservation-search.component.html',
   styles: [`
      .reservation-search {
         width: 500px;
      }

      .past-toggle {
         margin-top: 8px;
         margin-bottom: 16px;
         display: block;
      }
   `],
   standalone: false
})
export class ReservationSearchComponent implements AfterViewInit {
   @ViewChild(MatSelect) matSelect!: MatSelect;
   @ViewChild(MatSelectSearchComponent) ss!: MatSelectSearchComponent;

   @Input() includeFinancialAccounts = false;
   @Input() label = 'Резервация';

   reservations: ReservationInfo[] = [];
   accounts: FinancialAccount[] = [];

   control = new UntypedFormControl('');
   pastControl = new UntypedFormControl(false);
   filteringCtrl = new UntypedFormControl('');
   searching = false;

   equalReservations = equalIdentifiables;

   private sReservation = inject(ReservationService);
   private sFinancialAccount = inject(FinancialAccountService);

   constructor() {
      this.filteringCtrl.valueChanges.pipe(
         debounceTime(DEBOUNCE_TIME),
         filter(search => !!search),
         tap(() => this.searching = true),
         switchMap(search => forkJoin({
            rs: this.sReservation.findAll(search, this.pastControl.value),
            accs: this.includeFinancialAccounts ?
               this.sFinancialAccount.search(search) : of([]),
         }))
      ).subscribe({
         next: ({rs, accs}) => {
            this.reservations = rs.sort((r1, r2) => dateCmp(r1.start, r2.start));
            if (this.includeFinancialAccounts) {
               this.accounts =
                  accs.sort((a1, a2) => a1.titular.contact.name.localeCompare(
                     a2.titular.contact.name));
            }

            this.searching = false;
         },
         error: () => this.searching = false
      });
   }

   get value(): Reservation {
      return this.control.value;
   }

   ngAfterViewInit(): void {
      setTimeout(() => {
         this.matSelect.open();
         this.ss.updateInputWidth();
      }, AUTO_FOCUS_TIME);
   }
}

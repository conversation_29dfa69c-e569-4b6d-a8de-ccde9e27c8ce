<div>
   @if (!disabled) {
      @if (hasMoreSpace) {
         <div>
            <mat-form-field [class.wide]="wide">
               <mat-icon matPrefix>people</mat-icon>
               <mat-label>Добавяне на гости</mat-label>
               <input #guestInput (keydown.enter)="blockEvent($event)"
                      [formControl]="guestFilteringCtrl" [matAutocomplete]="auto"
                      autocomplete="off" matInput type="text">
               <mat-autocomplete #auto="matAutocomplete"
                                 (optionSelected)="selectGuests($event)">
                  @for (guest of filteredGuests | async; track guest.id) {
                     <mat-option [value]="guest">
                        {{guest | formatCustomer}}
                        @if (guest.contact.notes; as notes) {
                           <mat-icon [matTooltip]="notes" inline
                                     matTooltipShowDelay="0" matTooltipPosition="above">
                              info_outlined
                           </mat-icon>
                        }
                     </mat-option>
                  }
               </mat-autocomplete>
               @if (searchingGuests) {
                  <mat-spinner diameter="25" matSuffix/>
               }
            </mat-form-field>
            <button (click)="addGuest()" mat-icon-button matTooltip="Създаване на гост">
               <mat-icon>person_add</mat-icon>
            </button>
         </div>
      } @else {
         <p class="text-center">Капацитетът на стаята е достигнат.</p>
      }
   }
   <div>
      <mat-list>
         @for (customerControl of customerArray.controls; track customerControl) {
            <mat-list-item>
               @if (!disabled) {
                  <button (click)="removeGuest($index)" color="warn" mat-icon-button
                          matTooltip="Премахване">
                     <mat-icon>clear</mat-icon>
                  </button>
               }
               {{customerControl.value | formatCustomer}}
               @if (customerControl.value.contact.notes; as notes) {
                  <mat-icon [matTooltip]="notes" inline matTooltipShowDelay="0"
                            matTooltipPosition="above">
                     info_outlined
                  </mat-icon>
               }
               <button (click)="editGuest(customerControl.value, $index)" mat-icon-button>
                  <mat-icon>edit</mat-icon>
               </button>
            </mat-list-item>
         }
      </mat-list>
   </div>
</div>


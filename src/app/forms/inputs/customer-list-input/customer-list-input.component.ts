import {
   booleanAttribute,
   Component,
   ElementRef,
   EventEmitter,
   inject,
   Input,
   OnInit,
   Output,
   ViewChild
} from '@angular/core';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {blockEvent, DEBOUNCE_TIME, equalIdentifiables} from '../../../utility/utility';
import {UntypedFormArray, UntypedFormControl} from '@angular/forms';
import {Observable, ReplaySubject} from 'rxjs';
import {Customer, CustomerType} from '../../../data/customers/customer';
import {debounceTime, filter, map, switchMap, tap} from 'rxjs/operators';
import {CustomerService} from '../../../services/customer.service';
import {
   CustomerDialogComponent,
   CustomerDialogData
} from '../../../dialogs/customer-dialog/customer-dialog.component';
import {MatDialog} from '@angular/material/dialog';
import {ID, NULL_ID} from '../../../data/identifiable';
import {NotificationService} from '../../../services/notification.service';

@Component({
   selector: 'app-customer-list-input',
   templateUrl: './customer-list-input.component.html',
   styles: [`
      .wide {
         min-width: 70%;
      }
   `],
   standalone: false
})
export class CustomerListInputComponent implements OnInit {
   @Input({required: true}) customerArray!: UntypedFormArray;
   @Input({transform: booleanAttribute}) hasMoreSpace = true;
   @Input({transform: booleanAttribute}) allCustomerFieldsRequired = false;
   @Input({transform: booleanAttribute}) disabled = false;
   @Input({transform: booleanAttribute}) wide = false;

   @Output() countChanged = new EventEmitter<number>();
   @Output() customerChanged = new EventEmitter<Customer>();

   @ViewChild('guestInput') guestInput!: ElementRef<HTMLInputElement>;

   customers: Customer[] = [];

   filteredGuests = new ReplaySubject<Customer[]>(1);
   guestFilteringCtrl = new UntypedFormControl();
   searchingGuests = false;

   blockEvent = blockEvent;

   private sCustomer = inject(CustomerService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);

   ngOnInit(): void {
      this.guestFilteringCtrl.valueChanges.pipe(
         filter(search => search && !search.contact?.name),
         tap(() => this.searchingGuests = true),
         debounceTime(DEBOUNCE_TIME),
         switchMap(search => this.sCustomer.find(search, false)),
         map(gs => gs.filter(g => g.type == CustomerType.individual)), // TODO(vlado): filter legal on back-end
      ).subscribe(filteredGuests => {
         this.searchingGuests = false;
         this.filteredGuests.next(filteredGuests);
      });
   }

   selectGuests(event: MatAutocompleteSelectedEvent): void {
      this.pushGuest(event.option.value);

      this.guestInput.nativeElement.value = '';
      this.guestFilteringCtrl.setValue(null);
      this.filteredGuests.next([]);
   }

   addGuest(): void {
      this.openCustomerDialog()
         .subscribe(customer => {
            this.pushGuest(customer);
            this.customerChanged.emit(customer);
         });
   }

   editGuest(customer: Customer, index: number) {
      this.openCustomerDialog(customer)
         .subscribe(result => {
            this.customerArray.at(index).setValue(result);
            this.customerChanged.emit(result);
         });
   }

   removeGuest(index: number) {
      const {name} = this.customerArray.at(index).value.contact;

      this.sNotification.openConfirmationDialog({
         title: 'Премахване на клиент',
         description: `Наистина ли искате да премахнете ${name} от резервацията?`
      }).subscribe(() => this.dropGuest(NULL_ID, index));
   }

   pushGuest(customer: Customer, front: boolean = false) {
      const notInList = this.customerArray.controls.findIndex(
         ctrl => equalIdentifiables(ctrl.value, customer)) === -1;

      if (notInList && customer.type == CustomerType.individual) {
         if (front) {
            this.customerArray.insert(0, new UntypedFormControl(customer));
         } else {
            this.customerArray.push(new UntypedFormControl(customer));
         }
      }

      this.emitCount();
   }

   dropGuest(guestId: ID, index?: number) {
      const i = index ??
         this.customerArray.controls.findIndex(ctrl => ctrl.value.id === guestId);

      if (i !== -1) {
         this.customerArray.removeAt(i);
      }

      this.emitCount();
   }

   updateGuest(guest: Customer) {
      const index = this.customerArray.controls.findIndex(
         control => control.value.id == guest.id);
      if (index != -1) {
         this.customerArray.at(index).setValue(guest);
      }
   }

   openCustomerDialog(customer?: Customer): Observable<Customer> {
      const data: CustomerDialogData = {
         data: customer,
         edit: !!customer,
         allRequired: this.allCustomerFieldsRequired
      };

      return this.dialog.open(CustomerDialogComponent, {
         data,
         closeOnNavigation: true
      }).afterClosed().pipe(filter(result => !!result));
   }

   private emitCount(): void {
      this.countChanged.emit(
         this.customerArray.value.length);
   }
}

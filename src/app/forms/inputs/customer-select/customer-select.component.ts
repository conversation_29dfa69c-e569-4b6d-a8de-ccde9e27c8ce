import {
   AfterViewInit,
   booleanAttribute,
   Component,
   EventEmitter,
   inject,
   Input,
   OnInit,
   Output,
   ViewChild
} from '@angular/core';
import {ReplaySubject} from 'rxjs';
import {UntypedFormControl} from '@angular/forms';
import {debounceTime, filter, switchMap, tap} from 'rxjs/operators';
import {Customer} from '../../../data/customers/customer';
import {CustomerService} from '../../../services/customer.service';
import {
   AUTO_FOCUS_TIME,
   DEBOUNCE_TIME,
   equalIdentifiables,
   isNotClick
} from '../../../utility/utility';
import {
   CustomerDialogComponent,
   CustomerDialogData
} from '../../../dialogs/customer-dialog/customer-dialog.component';
import {MatDialog} from '@angular/material/dialog';
import {MatSelect} from '@angular/material/select';
import {MatSelectSearchComponent} from 'ngx-mat-select-search';

@Component({
   selector: 'app-customer-select',
   templateUrl: './customer-select.component.html',
   styles: [`
      .customer-button {
         margin: 0 auto auto 16px;
      }
   `],
   standalone: false
})
export class CustomerSelectComponent implements OnInit, AfterViewInit {
   @ViewChild(MatSelect) matSelect!: MatSelect;
   @ViewChild(MatSelectSearchComponent) ss!: MatSelectSearchComponent;

   @Input() control!: UntypedFormControl;
   @Input({transform: booleanAttribute}) allRequired = false;
   @Input({transform: booleanAttribute}) legalOnly = false;
   @Input({transform: booleanAttribute}) noAction = false;
   @Input({transform: booleanAttribute}) autoOpen = false;
   @Input({transform: booleanAttribute}) wide = false;
   @Input() label = 'Титуляр';
   @Input() icon = 'person';

   @Output() customerChanged = new EventEmitter<Customer>();

   filtered = new ReplaySubject<Customer[]>(1);
   filteringCtrl = new UntypedFormControl();
   searching = false;

   equalCustomers = equalIdentifiables;

   private sCustomer = inject(CustomerService);
   private dialog = inject(MatDialog);

   get value(): Customer | undefined {
      return this.control.value;
   }

   ngOnInit(): void {
      this.filteringCtrl.valueChanges
         .pipe(
            filter(search => !!search),
            tap(() => this.searching = true),
            debounceTime(DEBOUNCE_TIME * 2),
            switchMap(search => this.sCustomer.find(search, this.legalOnly)),
         )
         .subscribe({
            next: filteredCustomers => {
               this.searching = false;
               this.filtered.next(filteredCustomers);
            },
            error: () => this.searching = false,
         });

      if (this.control.value) {
         this.next([this.control.value]);
      }
   }

   ngAfterViewInit(): void {
      if (this.autoOpen && !this.control.value) {
         setTimeout(() => {
            this.matSelect.open();
            this.ss.updateInputWidth();
         }, AUTO_FOCUS_TIME);
      }
   }

   hasCustomer(): boolean {
      return !!this.control.value;
   }

   setCustomer(customer: Customer): void {
      this.next([customer]);
      this.control.setValue(customer);
   }

   protected customerDailogEvent(event: MouseEvent, customer?: Customer) {
      if (isNotClick(event)) {
         return;
      }

      this.openCustomerDialog(customer);
   }

   private next(customers: Customer[]) {
      this.filtered.next(customers);
   }

   private openCustomerDialog(customer?: Customer) {
      const data: CustomerDialogData = {
         data: customer,
         edit: !!customer,
         allRequired: this.allRequired
      };
      const dialog = this.dialog.open(CustomerDialogComponent, {
         data,
         closeOnNavigation: true,
      });

      dialog.afterClosed()
         .pipe(filter(result => !!result))
         .subscribe(result => {
            this.setCustomer(result);
            this.customerChanged.emit(result);
         });
   }
}

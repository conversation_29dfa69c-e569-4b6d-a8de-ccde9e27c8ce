<div>
   <mat-form-field [class.wide-form-field]="wide">
      <mat-icon matPrefix>{{icon}}</mat-icon>
      <mat-label>{{label}}</mat-label>
      <mat-select [compareWith]="equalCustomers" [formControl]="control" cdkFocusInitial>
         <mat-option>
            <ngx-mat-select-search [formControl]="filteringCtrl"
                                   [searching]="searching"
                                   noEntriesFoundLabel="Няма намерени хора"
                                   placeholderLabel="Търсене">
            </ngx-mat-select-search>
         </mat-option>
         @for (customer of filtered | async; track customer.id) {
            <mat-option [value]="customer">
               {{customer | formatCustomer}}
               @if (customer.contact.notes; as notes) {
                  <mat-icon [matTooltip]="notes" matTooltipShowDelay="0"
                            matTooltipPosition="above" inline>
                     info_outlined
                  </mat-icon>
               }
            </mat-option>
         }
         <mat-select-trigger>
            {{value | formatCustomer}}
            @if (value?.contact?.notes; as notes) {
               <mat-icon [matTooltip]="notes" matTooltipShowDelay="0"
                         matTooltipPosition="above" inline>
                  info_outlined
               </mat-icon>
            }
         </mat-select-trigger>
      </mat-select>
   </mat-form-field>

   @if (!noAction) {
      @if (!value) {
         <button (click)="customerDailogEvent($event)" class="customer-button"
                 mat-icon-button matTooltip="Създаване на клиент">
            <mat-icon>person_add</mat-icon>
         </button>
      } @else {
         <button (click)="customerDailogEvent($event, value)" class="customer-button"
                 mat-icon-button
                 matTooltip="Редактиране на клиент">
            <mat-icon>edit</mat-icon>
         </button>
      }
   }
</div>

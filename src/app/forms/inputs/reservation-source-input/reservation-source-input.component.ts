import {booleanAttribute, Component, inject, Input, OnInit} from '@angular/core';
import {equalIdentifiables} from '../../../utility/utility';
import {ReservationSource} from '../../../data/reservation-source';
import {Observable} from 'rxjs';
import {ReservationSourceService} from '../../../services/reservation-source.service';
import {UntypedFormControl} from '@angular/forms';
import {map, tap} from 'rxjs/operators';

@Component({
   selector: 'app-reservation-source-input',
   templateUrl: './reservation-source-input.component.html',
   standalone: false
})
export class ReservationSourceInputComponent implements OnInit {
   @Input() control!: UntypedFormControl;
   @Input({transform: booleanAttribute}) accountOnly = false;
   @Input({transform: booleanAttribute}) multiple = false;
   @Input({transform: booleanAttribute}) wide = false;

   reservationSources$?: Observable<ReservationSource[]>;
   equalReservationSources = equalIdentifiables;

   private sReservationSource = inject(ReservationSourceService);

   ngOnInit(): void {
      this.reservationSources$ = this.sReservationSource.getAll().pipe(
         map(rss => this.accountOnly ? rss.filter(rs => !!rs.financialAccount) : rss),
         tap(rss => {
            if (!this.control.value && !this.multiple) {
               this.control.setValue(rss.find(rs => rs.isDefault));
            }
         }),
      );
   }
}

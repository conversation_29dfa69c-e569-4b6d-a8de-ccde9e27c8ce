import {Component, Input} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';

@Component({
   selector: 'app-sequence-input',
   template: `
      <mat-form-field subscriptSizing="fixed">
         <mat-label>{{name}}</mat-label>
         <input [formControl]="control" matInput type="number">
         @if (control.errors?.['required']) {
            <mat-error>Полето е задължително.</mat-error>
         }
         @if (control.errors?.['min']; as min) {
            <mat-error>Номерът не може да бъде по-малък от {{min.min}}.</mat-error>
         }
      </mat-form-field>
   `,
   standalone: false
})
export class SequenceInputComponent {
   @Input({required: true}) name!: string;
   @Input({required: true}) control!: UntypedFormControl;
}

import {Component, inject, Input} from '@angular/core';
import {CustomerGroupService} from '../../../services/customer-group.service';
import {UntypedFormControl, UntypedFormGroup} from '@angular/forms';
import {Observable} from 'rxjs';
import {CustomerGroup} from '../../../data/customers/customer-group';
import {ID} from '../../../data/identifiable';
import {MatFormFieldAppearance} from '@angular/material/form-field';
import {map} from 'rxjs/operators';
import {sortCustomerGroups} from '../../../utility/utility';

@Component({
   selector: 'app-guest-group-count-input',
   templateUrl: './guest-group-count-input.component.html',
   styleUrls: ['./guest-group-count-input.component.scss'],
   standalone: false
})
export class GuestGroupCountInputComponent {
   @Input() control!: UntypedFormGroup;
   @Input() row = true;
   @Input() appearance: MatFormFieldAppearance = 'outline';

   groups$: Observable<CustomerGroup[]> | null = null;

   constructor() {
      this.groups$ = inject(CustomerGroupService).getAll().pipe(map(sortCustomerGroups));
   }

   getControl(id: ID): UntypedFormControl {
      return this.control.get(id) as UntypedFormControl;
   }
}

<div [ngClass]="row ? 'row' : 'column'" class="inputs">
   @for (group of groups$ | async; track group.id) {
      <mat-form-field [appearance]="appearance">
         <mat-label>{{group | customerGroup:true}}</mat-label>
         <mat-icon matPrefix>people</mat-icon>
         <input [formControl]="getControl(group.id)" autocomplete="off" matInput min="0"
                type="number">
      </mat-form-field>
   }
</div>

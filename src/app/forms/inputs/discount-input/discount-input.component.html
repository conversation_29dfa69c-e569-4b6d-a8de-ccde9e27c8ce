<ng-container [formGroup]="group">
   <mat-form-field class="type-input">
      <mat-label>Тип</mat-label>
      <mat-select #type formControlName="type">
         <mat-option [value]="dt.percentage">%</mat-option>
         <mat-option [value]="dt.amount">Сума</mat-option>
      </mat-select>
   </mat-form-field>
   @if (type.value === dt.amount) {
      <app-money-input [group]="amount" label="Сума"/>
   } @else {
      <mat-form-field style="width: 150px">
         <mat-label>Процент</mat-label>
         <input formControlName="percentage" matInput min="0" type="number">
         <span matSuffix>%</span>
      </mat-form-field>
   }
</ng-container>

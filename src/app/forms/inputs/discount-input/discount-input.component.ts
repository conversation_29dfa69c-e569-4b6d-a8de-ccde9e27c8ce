import {Component, Input} from '@angular/core';
import {UntypedFormGroup} from '@angular/forms';
import {DiscountType} from '../../../utility/discount';

@Component({
   selector: 'app-discount-input',
   templateUrl: './discount-input.component.html',
   styles: [`
      .type-input {
         width: 100px;
      }
   `],
   standalone: false
})
export class DiscountInputComponent {
   @Input() group!: UntypedFormGroup;

   dt = DiscountType;

   get amount(): UntypedFormGroup {
      return this.group.get('amount') as UntypedFormGroup;
   }
}

import {Component, EventEmitter, inject, Input, Output} from '@angular/core';
import {Observable} from 'rxjs';
import {
   VoucherDialogComponent
} from '../../../dialogs/voucher-dialog/voucher-dialog.component';
import {filter} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import {Voucher, VoucherDiscount} from '../../../data/voucher';

@Component({
   selector: 'app-voucher-input',
   templateUrl: './voucher-input.component.html',
   standalone: false
})
export class VoucherInputComponent {
   @Input() vouchers!: Voucher[];
   @Input() voucherDiscount: VoucherDiscount | undefined;
   @Output() changeVoucher = new EventEmitter<VoucherDiscount>();

   private dialog = inject(MatDialog);

   addVoucher(): void {
      this.openVoucherDialog().subscribe(voucher => this.setVoucher(voucher));
   }

   editVoucher(): void {
      this.openVoucherDialog().subscribe(voucher => this.setVoucher(voucher));
   }

   removeVoucher(): void {
      this.setVoucher(undefined);
   }

   private setVoucher(newValue: VoucherDiscount | undefined) {
      this.voucherDiscount = newValue;
      this.changeVoucher.emit(newValue);
   }

   private openVoucherDialog(): Observable<VoucherDiscount> {
      const data = {voucherDiscount: this.voucherDiscount, vouchers: this.vouchers};
      const dialog = this.dialog.open(VoucherDialogComponent, {data});
      return dialog.afterClosed().pipe(filter(result => !!result));
   }
}

export interface Color {
   text: string[];
   background: string[];
}

export const COLORS: Map<string, Color> = new Map([
   ['#e57373', {
      text: ['red-text', 'text-lighten-2'],
      background: ['red', 'lighten-2', 'dark-text']
   }],
   ['#ba68c8', {
      text: ['purple-text', 'text-lighten-2'],
      background: ['purple', 'lighten-2', 'dark-text']
   }],
   ['#9575cd', {
      text: ['deep-purple-text', 'text-lighten-2'],
      background: ['deep-purple', 'lighten-2', 'dark-text']
   }],
   ['#7986cb', {
      text: ['indigo-text', 'text-lighten-2'],
      background: ['indigo', 'lighten-2', 'dark-text']
   }],
   ['#64b5f6', {
      text: ['blue-text', 'text-lighten-2'],
      background: ['blue', 'lighten-2', 'dark-text']
   }],
   ['#4fc3f7', {
      text: ['light-blue-text', 'text-lighten-2'],
      background: ['light-blue', 'lighten-2', 'dark-text']
   }],
   ['#4dd0e1', {
      text: ['cyan-text', 'text-lighten-2'],
      background: ['cyan', 'lighten-2', 'dark-text']
   }],
   ['#4db6ac', {
      text: ['teal-text', 'text-lighten-2'],
      background: ['teal', 'lighten-2', 'dark-text']
   }],
   ['#81c784', {
      text: ['green-text', 'text-lighten-2'],
      background: ['green', 'lighten-2', 'dark-text']
   }],
   ['#dce775', {
      text: ['lime-text', 'text-lighten-2'],
      background: ['lime', 'lighten-2', 'dark-text']
   }],
   ['#ffd54f', {
      text: ['amber-text', 'text-lighten-2'],
      background: ['amber', 'lighten-2', 'dark-text']
   }],
   ['#ffb74d', {
      text: ['orange-text', 'text-lighten-2'],
      background: ['orange', 'lighten-2', 'dark-text']
   }],
   ['#ff8a65', {
      text: ['deep-orange-text', 'text-lighten-2'],
      background: ['deep-orange', 'lighten-2', 'dark-text']
   }],
   ['#e0e0e0', {
      text: ['grey-text', 'text-lighten-2'],
      background: ['grey', 'lighten-2', 'dark-text']
   }],
   ['#90a4ae', {
      text: ['blue-grey-text', 'text-lighten-2'],
      background: ['blue-grey', 'lighten-2', 'dark-text']
   }],
]);

<button [matMenuTriggerFor]="colorMenu" mat-icon-button
        matTooltip="Цвят" matTooltipPosition="right">
   <mat-icon [ngClass]="currentColor">circle</mat-icon>
</button>

<mat-menu #colorMenu="matMenu" xPosition="after" yPosition="above">
   <div class="color-menu">
      <div class="colors">
         @for (color of colors | keyvalue; track color.key) {
            <button (click)="setColor(color.key)" [ngClass]="color.value.text"
                    mat-icon-button>
               <mat-icon>circle</mat-icon>
            </button>
         }
      </div>
      <button (click)="setColor('')" mat-button>
         <mat-icon>format_color_reset</mat-icon>
      </button>
   </div>
</mat-menu>

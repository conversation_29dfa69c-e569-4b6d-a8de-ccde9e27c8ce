import {Component, Input} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';
import {COLORS} from './colors';

const noColor = ['material-icons-outlined'];

@Component({
   selector: 'app-color-input',
   templateUrl: './color-input.component.html',
   styles: [`
      .color-menu {
         display: flex;
         flex-direction: column;
         align-items: center;

         .colors {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            max-width: 144px;
         }
      }
   `],
   standalone: false
})
export class ColorInputComponent {
   @Input() control!: UntypedFormControl;

   colors = COLORS;

   get currentColor(): string | string[] {
      const {value} = this.control;
      return value ? (this.colors.get(value)?.text ?? noColor) : noColor;
   }

   setColor(color: string) {
      this.control.setValue(color);
   }
}

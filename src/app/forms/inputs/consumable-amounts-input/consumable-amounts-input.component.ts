import {Component, inject, Input, OnInit} from '@angular/core';
import {UntypedFormArray, UntypedFormBuilder} from '@angular/forms';
import {Consumable} from '../../../data/bundles/consumable';
import {ConsumableService} from '../../../services/consumable.service';
import {ConsumableWithRule, ConsumptionRule} from '../../../data/bundles/bundle';
import {equalIdentifiables} from '../../../utility/utility';
import {ConsumptionRuleService} from '../../../services/consumption-rule.service';

@Component({
   selector: 'app-consumable-amounts-input',
   templateUrl: './consumable-amounts-input.component.html',
   styles: [`
      .consumable-amount {
         display: flex;
         flex-direction: row;
         justify-content: space-between;
         align-items: center;
      }
   `],
   standalone: false
})
export class ConsumableAmountsInputComponent implements OnInit {
   @Input() formArray!: UntypedFormArray;

   allConsumables: Consumable[] = [];
   allRules: ConsumptionRule[] = [];
   equalIds = equalIdentifiables;

   private sConsumable = inject(ConsumableService);
   private sConsumptionRule = inject(ConsumptionRuleService);
   private fb = inject(UntypedFormBuilder);

   ngOnInit(): void {
      this.sConsumable.getAll().subscribe(cs => this.allConsumables = cs);
      this.sConsumptionRule.getAll().subscribe(rules => this.allRules = rules);
   }

   addConsumable() {
      const emptyConsumableRule: Partial<ConsumableWithRule> = {
         consumable: undefined,
         rule: undefined
      };

      this.formArray.push(this.fb.group(emptyConsumableRule));
   }

   removeAt(index: number): void {
      this.formArray.removeAt(index);
   }
}

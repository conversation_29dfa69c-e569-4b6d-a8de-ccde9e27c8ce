<div>
   <div class="settings-header">
      <span class="mat-h2"
            style="margin-bottom: 8px; margin-top: 8px;">Услуги в пакета</span>
      <button (click)="addConsumable()" mat-icon-button>
         <mat-icon>add</mat-icon>
      </button>
   </div>
   <div>
      @for (group of formArray.controls; track group; let i = $index) {
         <div [formGroup]="$any(group)" class="consumable-amount">
            <mat-select [compareWith]="equalIds" formControlName="consumable"
                        placeholder="Услуга" style="border-bottom: 1px solid;">
               @for (consumable of allConsumables; track consumable.id) {
                  <mat-option [value]="consumable">{{consumable.name}}</mat-option>
               }
            </mat-select>
            <mat-select [compareWith]="equalIds" formControlName="rule"
                        placeholder="Правило за консумация"
                        style="border-bottom: 1px solid">
               @for (rule of allRules; track rule.id) {
                  <mat-option [value]="rule">{{rule.name}}</mat-option>
               }
            </mat-select>
            <button (click)="removeAt(i)" color="warn" mat-icon-button>
               <mat-icon>clear</mat-icon>
            </button>
         </div>
      }
   </div>
</div>

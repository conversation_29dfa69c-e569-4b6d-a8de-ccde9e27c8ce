<form [formGroup]="form" class="customer-form">
   @if (duplicates().length > 0) {
      <div>
         <div>Профил за клиента вече съществува! Моля изберете някой от следните:</div>
         @for (duplicate of duplicates(); track duplicate.id) {
            <div (click)="handleDuplicate(duplicate)" class="primary-text clickable">
               {{duplicate| formatCustomer}}
            </div>
         }
      </div>
   }

   <section class="column" formGroupName="contact">
      <div class="flex-center-row">
         <mat-form-field>
            <mat-icon matPrefix>person</mat-icon>
            <mat-label>Име</mat-label>
            <input autocomplete="off" formControlName="name" matInput type="text">
         </mat-form-field>

         <mat-form-field>
            <mat-label>Фамилия</mat-label>
            <mat-icon matPrefix>person</mat-icon>
            <input [formControl]="lastName" autocomplete="off" matInput type="text">
         </mat-form-field>
      </div>

      <div class="flex-center-row">
         <mat-form-field>
            <mat-icon matPrefix>home</mat-icon>
            <mat-label>Адрес</mat-label>
            <input autocomplete="off" formControlName="address" matInput type="text">
         </mat-form-field>

         <mat-form-field>
            <mat-icon matPrefix>phone</mat-icon>
            <mat-label>Телефон</mat-label>
            <input autocomplete="off" formControlName="phone" matInput type="tel">
         </mat-form-field>
      </div>

      <div class="flex-center-row">
         <mat-form-field>
            <mat-icon matPrefix>email</mat-icon>
            <mat-label>Имейл</mat-label>
            <input autocomplete="off" formControlName="email" matInput type="email">
         </mat-form-field>

         @if (!hideCustomerFields) {
            <mat-form-field>
               <mat-icon matPrefix>notes</mat-icon>
               <mat-label>Бележки</mat-label>
               <input autocomplete="off" formControlName="notes" matInput type="text">
            </mat-form-field>
         }
      </div>
   </section>

   <mat-divider></mat-divider>

   <section>
      <mat-tab-group (selectedTabChange)="onTabChange()"
                     [(selectedIndex)]="selectedTab" animationDuration="0ms">
         @if (isTabVisible(0)) {
            <mat-tab [disabled]="edit && selectedType !== customerType.individual"
                     label="Физическо лице">
               <div class="flex-column">
                  <div class="flex-row row-3 max-width">
                     <mat-form-field>
                        <mat-label>Националност</mat-label>
                        <mat-select formControlName="country">
                           @for (code of countryCodes | keyvalue; track code.key) {
                              <mat-option [value]="code.key">
                                 {{code.value}}
                                 <mat-label>{{code.key}}</mat-label>
                              </mat-option>
                           }
                        </mat-select>
                     </mat-form-field>
                     <mat-form-field>
                        <mat-label>ЕГН/ЛНЧ</mat-label>
                        <input autocomplete="off" formControlName="idNumber" matInput
                               maxlength="10" type="text">
                     </mat-form-field>
                     <mat-form-field>
                        <mat-label>Номер по ЗДДС</mat-label>
                        <div class="prefix-wrapper">
                           @if (focusedInput === 'vatNumber' || vatNumber.value) {
                              <span class="prefix">{{countryViesCode}}</span>
                           }
                           <input autocomplete="off" formControlName="vatNumber" matInput
                                  type="text"
                                  (focus)="onInputFocus('vatNumber')"
                                  (blur)="onInputBlur('vatNumber')">
                        </div>
                     </mat-form-field>
                  </div>
                  <div class="flex-row row-3" [formGroup]="document">
                     <mat-form-field>
                        <mat-label>Тип документ</mat-label>
                        <mat-select formControlName="type">
                           <mat-option [value]="documentType.card">
                              Лична карта
                           </mat-option>
                           <mat-option [value]="documentType.passport">
                              Паспорт
                           </mat-option>
                           <mat-option [value]="documentType.driverLicense">
                              Шофьорска книжка
                           </mat-option>
                        </mat-select>
                     </mat-form-field>
                     <mat-form-field>
                        <mat-label>Номер на документ</mat-label>
                        <input autocomplete="off" formControlName="id" matInput
                               type="text">
                     </mat-form-field>
                     <mat-form-field>
                        <mat-label>Издаден на</mat-label>
                        <input [matDatepicker]="$any(issuedAt)" [max]="maxIssuedDate"
                               formControlName="issuedAt" matInput>
                        <mat-datepicker-toggle [for]="issuedAt" matSuffix/>
                        <mat-datepicker #issuedAt startView="multi-year"/>
                     </mat-form-field>
                  </div>

                  <div class="row">
                     <ng-container [formGroup]="individual">
                        <mat-form-field [style.width]="'50%'">
                           <mat-label>Пол</mat-label>
                           <mat-select formControlName="gender">
                              <mat-option [value]="gender.male">Мъж</mat-option>
                              <mat-option [value]="gender.female">Жена</mat-option>
                           </mat-select>
                        </mat-form-field>

                        <mat-form-field [style.width]="'50%'">
                           <mat-label>Рожден ден</mat-label>
                           <input [matDatepicker]="$any(birthDate)" [max]="maxIssuedDate"
                                  formControlName="birthDate"
                                  matInput>
                           <mat-datepicker-toggle [for]="birthDate"
                                                  matSuffix></mat-datepicker-toggle>
                           <mat-datepicker #birthDate
                                           startView="multi-year"></mat-datepicker>
                        </mat-form-field>
                     </ng-container>
                  </div>
               </div>
            </mat-tab>
         }

         @if (isTabVisible(1)) {
            <mat-tab [disabled]="edit && selectedType !== customerType.legal"
                     label="Юридическо лице">
               <div class="flex-row row-3">
                  <mat-form-field>
                     <mat-label>Националност</mat-label>
                     <mat-select formControlName="country">
                        @for (code of countryCodes | keyvalue; track code.key) {
                           <mat-option [value]="code.key">
                              {{code.value}}
                              <mat-label>{{code.key}}</mat-label>
                           </mat-option>
                        }
                     </mat-select>
                  </mat-form-field>
                  <mat-form-field>
                     <mat-label>ЕИК</mat-label>
                     <div class="prefix-wrapper">
                        @if (focusedInput === 'idNumber' || idNumber.value) {
                           <span class="prefix">{{countryViesCode}}</span>
                        }
                        <input autocomplete="off" formControlName="idNumber" matInput
                               type="text"
                               (focus)="onInputFocus('idNumber')"
                               (blur)="onInputBlur('idNumber')">
                     </div>
                     @if (!idNumber.disabled) {
                        <button (click)="loadInfoForCurrentIdNumber()"
                                [disabled]="!idNumber.value || loadingUid" matSuffix
                                mat-icon-button matTooltipPosition="above"
                                matTooltip="Попълни от търговския регистър"
                                matTooltipShowDelay="0">
                           <mat-icon>policy</mat-icon>
                        </button>
                     }
                  </mat-form-field>
                  <mat-form-field>
                     <mat-label>
                        Номер по ЗДДС
                        <app-info-icon
                           tooltip="Вписва се само за фирми регистрирани по ЗДДС! Ако фирмата е българска, номерът трябва да бъде същият като ЕИК"/>
                     </mat-label>
                     <div class="prefix-wrapper">
                        @if (focusedInput === 'vatNumber' || vatNumber.value) {
                           <span class="prefix">{{countryViesCode}}</span>
                        }
                        <input autocomplete="off" formControlName="vatNumber" matInput
                               type="text"
                               (focus)="onInputFocus('vatNumber')"
                               (blur)="onInputBlur('vatNumber')">
                     </div>
                     @if (idNumber.disabled) {
                        <button (click)="loadInfoForCurrentVatNumber()"
                                [disabled]="!vatNumber.value || loadingUid" matSuffix
                                mat-icon-button matTooltipPosition="above"
                                matTooltip="Попълни от европейския регистър"
                                matTooltipShowDelay="0">
                           <mat-icon>policy</mat-icon>
                        </button>
                     }
                  </mat-form-field>
               </div>

               <div class="flex-row" [formGroup]="legal">
                  <mat-form-field class="max-width">
                     <mat-label>МОЛ</mat-label>
                     <input autocomplete="off" formControlName="mol" matInput
                            type="text">
                  </mat-form-field>
               </div>
            </mat-tab>
         }

         @if (isTabVisible(2)) {
            <mat-tab [disabled]="edit && selectedType !== customerType.service"
                     label="Служебно лице"></mat-tab>
         }
      </mat-tab-group>
   </section>
</form>

import {
   ChangeDetectorRef,
   Component,
   computed,
   inject,
   Input,
   OnInit,
   signal
} from '@angular/core';
import {
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {
   Customer,
   CustomerType,
   Gender,
   PersonDocumentType
} from '../../data/customers/customer';
import {DEBOUNCE_TIME, enumValidator, today} from '../../utility/utility';
import {countryCodes} from '../../data/country-codes';
import {debounceTime, filter, switchMap, tap} from 'rxjs/operators';
import {DateTime} from 'luxon';
import {RegistryAgencyService, VIESStatus} from '../../services/registry-agency.service';
import {NotificationService} from '../../services/notification.service';
import {CustomerService} from 'src/app/services/customer.service';
import {forkJoin, of} from 'rxjs';

@Component({
   selector: 'app-customer-form',
   templateUrl: './customer-form.component.html',
   styleUrls: ['./customer-form.component.scss'],
   standalone: false
})
export class CustomerFormComponent implements OnInit {
   @Input() data?: Partial<Customer>;
   @Input() edit = false;
   @Input() allRequired = false;
   @Input() preselectedTab = null;
   @Input() hideCustomerFields = false;
   selectedTab = 0;
   maxIssuedDate = today();
   loadingUid = false;
   focusedInput = '';

   gender = Gender;
   countryCodes = countryCodes;

   form = this.fb.group({
      id: null,
      idNumber: [null, Validators.pattern('^[0-9]+$')],
      country: 'BG',
      vatNumber: null,
      contact: this.fb.group({
         name: ['', Validators.required],
         lastName: null,
         address: null,
         phone: null,
         email: [null, Validators.email],
         notes: null
      })
   });
   document = this.fb.group({
      type: [PersonDocumentType.card, enumValidator(PersonDocumentType)],
      id: '',
      issuedAt: null
   });
   individual = this.fb.group({
      birthDate: null,
      gender: [Gender.male, enumValidator(Gender)]
   });
   legal = this.fb.group({
      mol: ''
   });

   protected docDuplicates = signal<Customer[]>([]);
   protected idNumberDuplicates = signal<Customer[]>([]);
   duplicates = computed(() => {
      const result: Customer[] = [];
      const pushUniqueInResult = (duplicate: Customer) =>
         !result.find(d => d.id === duplicate.id) && result.push(duplicate);
      this.docDuplicates().forEach(pushUniqueInResult);
      this.idNumberDuplicates().forEach(pushUniqueInResult);
      return result;
   });

   protected documentType = PersonDocumentType;
   protected customerType = CustomerType;

   private sCustomer = inject(CustomerService);
   private sRegistryAgency = inject(RegistryAgencyService);
   private sNotification = inject(NotificationService);
   private cd = inject(ChangeDetectorRef);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid && this.relevantFormGroups.every(fg => fg.valid);
   }

   get value(): any {
      const result: any = this.form.getRawValue();

      result.type = this.selectedType;
      if (result.type == CustomerType.legal) {
         const legal = this.legal.value;
         if (legal.mol) {
            result.legal = legal;
         }
      } else if (result.type == CustomerType.individual) {
         const individual = this.individual.value;
         if (individual.birthDate && individual.gender) {
            result.individual = individual;
         }

         const document = this.document.value;
         if (document.type && document.id && document.issuedAt) {
            result.document = document;
         }
      }

      return result;
   }

   get id(): UntypedFormControl {
      return this.form.get('id') as UntypedFormControl;
   }

   get lastName(): UntypedFormControl {
      return this.form.get('contact')?.get('lastName') as UntypedFormControl;
   }

   get birthDate(): UntypedFormControl {
      return this.individual.get('birthDate') as UntypedFormControl;
   }

   get country(): UntypedFormControl {
      return this.form.get('country') as UntypedFormControl;
   }

   get idNumber(): UntypedFormControl {
      return this.form.get('idNumber') as UntypedFormControl;
   }

   get vatNumber(): UntypedFormControl {
      return this.form.get('vatNumber') as UntypedFormControl;
   }

   get selectedType(): CustomerType {
      switch (this.selectedTab) {
         case 0:
            return CustomerType.individual;
         case 1:
            return CustomerType.legal;
         default:
            return CustomerType.service;
      }
   }

   get relevantFormGroups(): UntypedFormGroup[] {
      switch (this.selectedType) {
         case CustomerType.individual:
            return [this.individual, this.document];
         case CustomerType.legal:
            return [this.legal];
         default:
            return [];
      }
   }

   get isChanged(): boolean {
      return this.form.dirty || this.relevantFormGroups.some(fg => fg.dirty);
   }

   get countryViesCode(): string | undefined {
      const country = this.country.value;
      if (country) {
         if (country == 'GR') {
            return 'EL';
         } else if (country == 'GB') {
            return 'UK';
         } else {
            return country;
         }
      }
   }

   set customer(value: Customer) {
      this.data = value;
      this.renewData();
      this.form.markAsPristine();
      this.document.markAsPristine();
      this.legal.markAsPristine();
      this.individual.markAsPristine();

      this.updateControls(value.type, this.data?.country);
   }

   isTabVisible(tab: number): boolean {
      if (this.preselectedTab === null) {
         return true;
      } else {
         return tab === this.preselectedTab;
      }
   }

   ngOnInit(): void {
      this.renewData();

      if (this.allRequired) {
         this.form.get('country')?.addValidators(Validators.required);
      }

      this.updateControls(this.data?.type ?? CustomerType.individual, this.data?.country);

      this.document.valueChanges.pipe(
         debounceTime(500),
         filter(doc => {
            if (!doc?.id) {
               this.docDuplicates.set([]);
               return false;
            }
            return true;
         }),
         switchMap(doc => forkJoin([of(doc), this.sCustomer.find(doc.id, false)])),
      ).subscribe(([doc, duplicates]) => {
         this.docDuplicates.set(duplicates.filter(p => p.id != this.id.value
            && p.country === this.country.value
            && p.document?.id === doc.id
            && p.document?.type === doc.type));
      });

      this.idNumber.valueChanges.pipe(
         tap(() => {
            if (this.selectedType === CustomerType.legal && this.vatNumber.disabled) {
               this.vatNumber.enable();
               this.vatNumber.setValue(null);
            }
         }),
         debounceTime(500),
         filter(id => {
            if (!id) {
               this.idNumberDuplicates.set([]);
               return false;
            }
            return true;
         }),
         switchMap(id => forkJoin([of(id), this.sCustomer.find(id, false)])),
      ).subscribe(([id, duplicates]) => {
         this.idNumberDuplicates.set(duplicates.filter(p => p.id != this.id.value
            && p.country === this.country.value
            && p.idNumber === id));
      });

      this.idNumber.valueChanges.pipe(
         debounceTime(DEBOUNCE_TIME),
         filter(
            egn => egn && egn.length >= 6 && this.selectedType ==
               CustomerType.individual && this.country.value == 'BG')
      ).subscribe((egn: string) => {
         const [y1, y2, m1, m2, d1, d2] = Array.from(egn).map(x => parseInt(x, 10));

         if (m1 <= 1) {
            this.birthDate.setValue(DateTime.fromObject({
               year: 1900 + y1 * 10 + y2,
               month: m1 * 10 + m2,
               day: d1 * 10 + d2
            }));
         } else {
            this.birthDate.setValue(DateTime.fromObject({
               year: 2000 + y1 * 10 + y2,
               month: (m1 - 4) * 10 + m2,
               day: d1 * 10 + d2
            }));
         }
      });

      this.country.valueChanges.pipe(filter(cc => !!cc))
         .subscribe(cc => {
            this.updateControls(this.selectedType, cc);
         });

      Object.values(this.form.controls).forEach(c => c.updateValueAndValidity());
      Object.values(this.document.controls).forEach(c => c.updateValueAndValidity());
      Object.values(this.individual.controls).forEach(c => c.updateValueAndValidity());
      Object.values(this.legal.controls).forEach(c => c.updateValueAndValidity());
   }

   loadInfoForCurrentIdNumber(): void {
      const uid = this.idNumber.value;
      if (uid) {
         this.loadingUid = true;
         forkJoin({
            idNumberPerson: this.sRegistryAgency.findByIdNumber(uid),
            vatResult: this.sRegistryAgency.findByVatNumber(uid, this.country.value)
         }).subscribe({
            next: ({idNumberPerson, vatResult}) => {
               if (idNumberPerson) {
                  this.idNumber.setErrors(null);
                  this.form.patchValue(idNumberPerson);
                  if (idNumberPerson.legal) {
                     this.legal.patchValue(idNumberPerson.legal);
                  }

                  switch (vatResult.status) {
                     case VIESStatus.invalid: {
                        this.vatNumber.setValue(null);
                        this.vatNumber.disable();
                        break;
                     }

                     case VIESStatus.valid: {
                        this.vatNumber.setValue(vatResult.person!.vatNumber);
                        this.vatNumber.disable();
                        break;
                     }

                     case VIESStatus.unknown: {
                        this.vatNumber.enable();
                        this.vatNumber.setValue(null);
                        break;
                     }
                  }
               } else {
                  this.idNumber.setErrors({notFound: true});
               }
               this.loadingUid = false;
            },
            error: err => {
               this.sNotification.displayError(err);
               this.loadingUid = false;
               this.vatNumber.enable();
               this.idNumber.setErrors(null);
            }
         });
      }
   }

   loadInfoForCurrentVatNumber(): void {
      const vat = this.vatNumber.value;
      if (vat) {
         this.loadingUid = true;
         this.sRegistryAgency.findByVatNumber(vat, this.country.value).subscribe({
            next: result => {
               switch (result.status) {
                  case VIESStatus.invalid: {
                     this.vatNumber.setErrors({notFound: true});
                  }
                     break;

                  case VIESStatus.valid: {
                     this.vatNumber.setErrors(null);
                     this.form.patchValue(result.person!);
                  }
                     break;

                  case VIESStatus.unknown: {
                     this.vatNumber.setErrors(null);
                  }
                     break;
               }
               this.loadingUid = false;
            },
            error: err => {
               this.sNotification.displayError(err);
               this.loadingUid = false;
               this.vatNumber.setErrors(null);
            }
         });
      }
   }

   protected onInputFocus(inputName: string) {
      this.focusedInput = inputName;
   }

   protected onInputBlur(inputName: string) {
      if (this.focusedInput == inputName) {
         this.focusedInput = '';
      }
   }

   protected onTabChange() {
      const country = this.country.value;
      this.form.reset();
      this.legal.reset();
      this.individual.reset();
      this.document.reset();
      this.country.setValue(country);

      this.updateControls(this.selectedType, country);
   }

   protected handleDuplicate(originalCustomer: Customer) {
      if (this.id.value) {
         this.sCustomer.mergeDuplicateToOriginal(this.id.value, originalCustomer.id)
            .subscribe(customer => this.customer = customer);
      } else {
         this.customer = originalCustomer;
      }
   }

   private updateControls(customerType: CustomerType, country?: string) {
      // ID number
      const isBulgarian = country === 'BG';
      if (isBulgarian) {
         if (this.allRequired) {
            this.idNumber.addValidators(Validators.required);
         }
         this.idNumber.enable();
      } else {
         if (this.allRequired) {
            this.idNumber.removeValidators(Validators.required);
         }

         if (customerType == CustomerType.legal) {
            this.idNumber.disable();
         } else {
            this.idNumber.enable();
         }
      }
      this.idNumber.updateValueAndValidity();

      // Last name
      if (customerType != CustomerType.individual) {
         this.lastName.removeValidators(Validators.required);
         this.lastName.setValue('', {emitEvent: false});
         this.lastName.disable();
      } else if (this.allRequired) {
         this.lastName.enable();
         this.lastName.addValidators(Validators.required);
      } else {
         this.lastName.enable();
      }
      this.lastName.updateValueAndValidity();

      // Mandatory fields
      const individualMandatoryFields = [
         this.document.get('id'),
         this.document.get('type'),
         this.document.get('issuedAt'),
         this.individual.get('birthDate'),
         this.individual.get('gender')
      ];

      const legalMandatoryFields = [this.legal.get('mol')];

      if (this.allRequired) {
         if (customerType == CustomerType.legal) {
            legalMandatoryFields.forEach(
               control => control?.addValidators(Validators.required));
            individualMandatoryFields.forEach(
               control => control?.removeValidators(Validators.required));
         } else if (customerType == CustomerType.individual) {
            legalMandatoryFields.forEach(
               control => control?.removeValidators(Validators.required));
            individualMandatoryFields.forEach(
               control => control?.addValidators(Validators.required));
         }
      } else {
         legalMandatoryFields.forEach(
            control => control?.removeValidators(Validators.required));
         individualMandatoryFields.forEach(
            control => control?.removeValidators(Validators.required));
      }
   }

   private renewData() {
      if (!this.data) {
         return;
      }

      this.form.patchValue(this.data);

      switch (this.data.type) {
         case CustomerType.individual:
            this.selectedTab = 0;
            if (this.data.document) {
               this.document.patchValue(this.data.document);
            }
            if (this.data.individual) {
               this.individual.patchValue(this.data.individual);
            }
            break;

         case CustomerType.legal:
            this.selectedTab = 1;
            if (this.data.legal) {
               this.legal.patchValue(this.data.legal);
            }
            break;

         default:
            this.selectedTab = 2;
      }

      this.cd.detectChanges();
   }
}

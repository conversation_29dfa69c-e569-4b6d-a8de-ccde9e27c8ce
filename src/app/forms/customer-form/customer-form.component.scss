.customer-form {
   & > * {
      padding: 0 16px;
   }
}

.row-3 {
   justify-content: space-evenly;

   & > * {
      width: 33%;
   }
}

.duplicate-warning {
   display: flex;
   flex-direction: column;
   border: 1px solid red;
   border-radius: 4px;

   button {
      margin-left: auto;
      margin-bottom: 8px;
   }

   mat-error {
      display: flex;
      align-items: center;

      mat-icon {
         margin-right: 4px;
      }
   }
}

section {
   margin-top: 18px;
   margin-bottom: 18px;
}

:host ::ng-deep .mat-mdc-tab-body-content {
   display: flex;
   flex-direction: column;
}

:host ::ng-deep .mat-mdc-tab-header {
   margin-bottom: 18px;
}

.prefix-wrapper {
   display: flex;
   align-items: center;
   position: relative;
}

.prefix {
   position: absolute;
   left: 10px;
   font-weight: bold;
   color: gray;
}

.prefix-wrapper input {
   padding-left: 40px;
   /* Ensure space for the prefix */
   width: 100%;
}

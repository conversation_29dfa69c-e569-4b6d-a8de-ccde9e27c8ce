<form [formGroup]="form" class="group-reservation-form">
   <div class="common-controls">
      <app-customer-select [control]="titular"/>
      <app-customer-select [control]="invoiceReceiver"
                           icon="receipt_long" label="Лице за фактура"/>
      <app-reservation-source-input [control]="source"/>
      <div>
         <mat-form-field>
            <mat-label>Бележки</mat-label>
            <input autocomplete="off" formControlName="notes" matInput type="text">
         </mat-form-field>
         <app-color-input [control]="color"/>
      </div>
   </div>
   <div>
      <mat-form-field>
         <mat-icon matPrefix>meeting_room</mat-icon>
         <mat-label>Стая за начисления</mat-label>
         <mat-select [formControl]="purchasesRoom">
            @for (room of rooms; track room.id) {
               <mat-option [value]="room">{{room.name}}</mat-option>
            }
         </mat-select>
      </mat-form-field>
   </div>
   <div>
      <mat-slide-toggle (change)="roomCheckboxChange($event)" color="primary">
         Събери всички начисления в една стая
      </mat-slide-toggle>
   </div>
   @if (sLicense.guestApp()) {
      <div>
         <mat-slide-toggle [formControl]="sendInvitation" color="primary">
            Изпрати покана за мобилното приложение
         </mat-slide-toggle>
      </div>
   }
   <ng-container formArrayName="reservations">
      <table mat-table>
         <ng-container matColumnDef="room">
            <th *matHeaderCellDef mat-header-cell>Стая</th>
            <td *matCellDef="let element" mat-cell>
               {{element.get('room').value.name}}
            </td>
            <td *matFooterCellDef mat-footer-cell></td>
         </ng-container>

         <ng-container matColumnDef="start">
            <th *matHeaderCellDef mat-header-cell>Начало</th>
            <td *matCellDef="let element" mat-cell>
               {{element.get('start').value.toFormat('dd MMMM')}}
            </td>
            <td *matFooterCellDef mat-footer-cell></td>
         </ng-container>

         <ng-container matColumnDef="end">
            <th *matHeaderCellDef mat-header-cell>Край</th>
            <td *matCellDef="let element" mat-cell>
               {{element.get('end').value.toFormat('dd MMMM')}}
            </td>
            <td *matFooterCellDef mat-footer-cell></td>
         </ng-container>

         <ng-container matColumnDef="bundle">
            <th *matHeaderCellDef mat-header-cell>Пакет</th>
            <td *matCellDef="let element; index as i" mat-cell>
               <app-bundle-select [control]="element.get('bundle')"
                                  [reservation]="reservations$[i] | async"/>
            </td>
            <td *matFooterCellDef mat-footer-cell></td>
         </ng-container>

         <ng-container matColumnDef="guestGroupCount">
            <th *matHeaderCellDef mat-header-cell>Брой гости</th>
            <td *matCellDef="let element" mat-cell>
               <app-guest-group-count-input [control]="element.get('guestGroupCount')"
               />
            </td>
            <td *matFooterCellDef mat-footer-cell></td>
         </ng-container>

         <ng-container matColumnDef="manualPrice">
            <th *matHeaderCellDef mat-header-cell>Ръчна цена</th>
            <td *matCellDef="let element; index as i" mat-cell>
               <app-money-input [group]="element.get('manualPrice')"
                                label="Ръчна цена" min="0"/>
            </td>
            <td *matFooterCellDef mat-footer-cell>
               <b>Общо</b>
            </td>
         </ng-container>

         <ng-container matColumnDef="finalPrice">
            <th *matHeaderCellDef mat-header-cell>Крайна цена</th>
            <td *matCellDef="let element; index as i" mat-cell>
               {{prices$[i] | async | money}}
            </td>
            <td *matFooterCellDef mat-footer-cell>{{finalPrice$ | async | money}}</td>
         </ng-container>

         <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
         <tr *matRowDef="let row; columns: displayedColumns;" mat-row></tr>
         <tr *matFooterRowDef="displayedColumns" mat-footer-row></tr>
      </table>
   </ng-container>
</form>

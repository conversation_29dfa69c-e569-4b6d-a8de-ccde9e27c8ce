import {
   AfterViewInit,
   ChangeDetectorRef,
   Component,
   inject,
   Input,
   ViewChild
} from '@angular/core';
import {Room} from '../../data/room';
import {DateTime} from 'luxon';
import {
   FormControl,
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormControl,
   Validators
} from '@angular/forms';
import {CustomerGroupService} from '../../services/customer-group.service';
import {CustomerGroup} from '../../data/customers/customer-group';
import {catchError, combineLatest, Observable, of, switchMap} from 'rxjs';
import {DEBOUNCE_TIME} from '../../utility/utility';
import {MatTable} from '@angular/material/table';
import {Reservation, ReservationStatus} from '../../data/reservation';
import {debounceTime, filter, map, startWith} from 'rxjs/operators';
import {moneyFormGroup} from '../../utility/form-utility';
import {Money} from '../../data/common';
import {sumMoney} from '../../utility/money-utility';
import {getReservationPrice, hasManualPrice} from '../../utility/reservation-utility';
import {
   CreateGroupReservation,
   ReservationService
} from '../../services/reservation.service';
import {MatSlideToggleChange} from '@angular/material/slide-toggle';
import {Privilege} from '../../data/auth/operator';
import {AuthService} from '../../auth/auth.service';
import {LicenseService} from '../../services/license.service';

type GroupFields = 'room' | 'start' | 'end' | 'bundle' | 'guestGroupCount';

@Component({
   selector: 'app-group-reservation-form',
   templateUrl: './group-reservation-form.component.html',
   styleUrls: ['./group-reservation-form.component.scss'],
   standalone: false
})
export class GroupReservationFormComponent implements AfterViewInit {
   @Input() data!: Map<Room, [DateTime, DateTime]>;

   @ViewChild(MatTable) table!: MatTable<any>;

   form = this.fb.group({
      titular: ['', Validators.required],
      source: ['', Validators.required],
      reservations: this.fb.array([]),
      color: '',
      notes: '',
      invoiceReceiver: '',
   });

   displayedColumns = [
      'room',
      'start',
      'end',
      'guestGroupCount',
      'bundle',
      'manualPrice',
      'finalPrice'
   ];
   reservations$: Observable<Partial<Reservation>>[] = [];
   prices$: Observable<Money | undefined>[] = [];
   finalPrice$: Observable<Money | undefined> = of(undefined);

   purchasesRoom = new FormControl<Room | null>({value: null, disabled: true});
   sendInvitation = new FormControl<boolean>(false);
   rooms: Room[] = [];

   protected sLicense = inject(LicenseService);

   private sCustomerGroup = inject(CustomerGroupService);
   private reservationService = inject(ReservationService);
   private sAuth = inject(AuthService);
   private cd = inject(ChangeDetectorRef);

   constructor(private fb: UntypedFormBuilder) {
   }

   get titular(): UntypedFormControl {
      return this.form.get('titular') as UntypedFormControl;
   }

   get invoiceReceiver(): UntypedFormControl {
      return this.form.get('invoiceReceiver') as UntypedFormControl;
   }

   get source(): UntypedFormControl {
      return this.form.get('source') as UntypedFormControl;
   }

   get reservations(): UntypedFormArray {
      return this.form.get('reservations') as UntypedFormArray;
   }

   get color(): UntypedFormControl {
      return this.form.get('color') as UntypedFormControl;
   }

   get notes(): UntypedFormControl {
      return this.form.get('notes') as UntypedFormControl;
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): CreateGroupReservation {
      const {reservations, notes, ...rest} = this.form.value;

      for (const reservation of reservations) {
         if (!hasManualPrice(reservation)) {
            delete reservation.manualPrice;
         }

         Object.assign(reservation, rest);
      }

      return {
         reservations,
         titular: rest.titular.id,
         sendInvitation: !!this.sendInvitation.value,
         purchasesRoom: this.purchasesRoom.value?.id,
         notes: notes?.split('\n'),
      };
   }

   ngAfterViewInit(): void {
      this.sCustomerGroup.getAll().subscribe(cgs => this.renewData(cgs));
   }

   roomCheckboxChange(change: MatSlideToggleChange): void {
      if (!change.checked) {
         this.purchasesRoom.setValue(null);
         this.purchasesRoom.disable();
      } else {
         this.purchasesRoom.enable();
      }
   }

   private renewData(groups: CustomerGroup[]): void {
      const data = Array.from(this.data.entries())
         .sort(([r1, _], [r2, __]) => r1.name.localeCompare(r2.name));

      for (const [room, [start, end]] of data) {
         const guestGroupCount = this.fb.group({});
         groups.forEach(cg => guestGroupCount.addControl(cg.id, this.fb.control(0)));

         const formGroup = this.fb.group({
            room: [room, Validators.required],
            start: [start, Validators.required],
            end: [end, Validators.required],
            bundle: ['', Validators.required],
            status: [ReservationStatus.pending, Validators.required],
            manualPrice: moneyFormGroup(this.fb),
            guestGroupCount,
            isShadow: false,
            isLeisure: false,
            guests: this.fb.control([]),
         });
         if (!this.sAuth.privileges[Privilege.manualPrice]) {
            formGroup.get('manualPrice')!.disable();
         }
         this.reservations.push(formGroup);

         const reservation$ = formGroup.valueChanges.pipe(
            startWith(formGroup.value),
            debounceTime(DEBOUNCE_TIME * 2),
            map(value => this.getReservation(value)),
         );
         this.reservations$.push(reservation$);
         this.prices$.push(reservation$.pipe(
            filter(r => !!r.room && !!r.bundle && !!r.start && !!r.end),
            switchMap(res => getReservationPrice(res, this.reservationService).pipe(
               catchError(() => of(undefined))
            )),
         ));
      }

      this.finalPrice$ = combineLatest(this.prices$).pipe(map(sumMoney));

      this.rooms = Array.from(this.data.keys());

      this.table.dataSource = this.reservations.controls;
      this.cd.detectChanges();
   }

   private getReservation(value: Pick<Reservation, GroupFields>): Partial<Reservation> {
      const {titular, source} = this.form.value;
      return {titular, source, ...value};
   }
}

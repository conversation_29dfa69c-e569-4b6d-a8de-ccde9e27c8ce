<form [formGroup]="form" class="flex-column">
   <div>
      <mat-form-field>
         <mat-label>Тип плащане</mat-label>
         <mat-select formControlName="method">
            @for (method of methods; track method) {
               <mat-option [value]="method.method" [disabled]="method.disabled">
                  @if (method.fiscal) {
                     <mat-icon>receipt_long</mat-icon>
                  }
                  {{method.text}}
               </mat-option>
            }
         </mat-select>
      </mat-form-field>
      <app-money-input [group]="price" label="Сума"/>
   </div>
   <div class="flex-column">
      @if (allowDownPayment) {
         <mat-slide-toggle color="primary" formControlName="isDownPayment">
            Капаро
         </mat-slide-toggle>
      }
   </div>
</form>

import {booleanAttribute, Component, inject, Input, OnInit} from '@angular/core';
import {
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {moneyFormGroupRequired} from '../../utility/form-utility';
import {Payment, PaymentMethod} from '../../data/payment';
import {ID} from 'src/app/data/identifiable';
import {FeatureService} from '../../services/feature.service';
import {AuthService} from '../../auth/auth.service';
import {FiscalAgentService} from '../../services/fiscal-agent.service';
import {methodToFiscal} from '../../data/fiscal/fiscal-agent';
import {PaymentMethodPipe} from '../../pipes/payment-method.pipe';

interface SupportedPayment {
   fiscal: boolean;
   disabled: boolean;
   method: PaymentMethod;
   text: string;
}

@Component({
   selector: 'app-payment-form',
   templateUrl: './payment-form.component.html',
   standalone: false
})
export class PaymentFormComponent implements OnInit {
   @Input() data?: Partial<Payment>;
   @Input() account!: ID;
   @Input({transform: booleanAttribute}) allowDownPayment = false;

   protected form = this.fb.group({
      id: '',
      account: ['', Validators.required],
      price: moneyFormGroupRequired(this.fb),
      purchases: this.fb.array([]),
      method: [PaymentMethod.cash, Validators.required],
      isDownPayment: false,
   });

   protected methods: SupportedPayment[] = [];

   private sFeature = inject(FeatureService);
   private sAuth = inject(AuthService);
   private sFiscalAgent = inject(FiscalAgentService);

   constructor(private fb: UntypedFormBuilder) {
      if (this.sFeature.fiscal()) {
         this.sFiscalAgent.getInfo().subscribe({
            next: device => {
               const supported = device.supportedPaymentTypes;
               const isFiscal = (m: PaymentMethod) =>
                  m !== PaymentMethod.bank && supported.includes(methodToFiscal(m));

               this.methods = Object.values(PaymentMethod).map(method => ({
                  fiscal: isFiscal(method),
                  disabled: method === PaymentMethod.bank &&
                     !this.sAuth.privileges.BANK_PAYMENTS,
                  method,
                  text: PaymentMethodPipe.toString(method),
               }));
            },
            error: () => {
               this.methods = Object.values(PaymentMethod).map(method => ({
                  fiscal: method !== PaymentMethod.bank,
                  disabled: method !== PaymentMethod.bank,
                  method,
                  text: PaymentMethodPipe.toString(method),
               }));
               this.method.setValue(PaymentMethod.bank);
            },
         });
      } else {
         this.methods = Object.values(PaymentMethod).map(method => ({
            fiscal: false,
            disabled: false,
            method,
            text: PaymentMethodPipe.toString(method),
         }));
      }
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Payment {
      return this.form.value;
   }

   get price(): UntypedFormGroup {
      return this.form.get('price') as UntypedFormGroup;
   }

   get method(): UntypedFormControl {
      return this.form.get('method') as UntypedFormControl;
   }

   ngOnInit(): void {
      if (this.data) {
         this.form.patchValue(this.data);
      }
      this.form.patchValue({account: this.account});
   }
}

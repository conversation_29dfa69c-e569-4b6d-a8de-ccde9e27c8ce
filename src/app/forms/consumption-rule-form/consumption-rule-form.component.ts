import {Component, inject, Input, OnInit} from '@angular/core';
import {ConsumptionRule} from '../../data/bundles/bundle';
import {UntypedFormBuilder, UntypedFormControl, Validators} from '@angular/forms';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
   selector: 'app-consumption-rule-form',
   templateUrl: './consumption-rule-form.component.html',
   styles: [`
      .row-form {
         display: flex;
         flex-direction: row;

         & > * {
            margin: auto 10px;
         }
      }

      .toggle-group {
         display: flex;
         flex-direction: column;
      }
   `],
   standalone: false
})
export class ConsumptionRuleFormComponent implements OnInit {
   @Input() data?: ConsumptionRule;
   @Input() edit = false;

   form = this.fb.group({
      id: '',
      name: ['', Validators.required],
      appliesPerCustomer: [false, Validators.required],
      appliesForRegularBed: [false, Validators.required],
      appliesForAdditionalBed: [false, Validators.required],
      applicableCustomerGroups: [[], Validators.required]
   });

   private sDataTable = inject(DataTableService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): ConsumptionRule {
      const result = this.form.value;

      if (!result.appliesPerCustomer) {
         result.appliesForRegularBed = false;
         result.appliesForAdditionalBed = false;
      }

      return result;
   }

   get applicableCustomerGroups(): UntypedFormControl {
      return this.form.get('applicableCustomerGroups') as UntypedFormControl;
   }

   get appliesPerCustomer(): boolean {
      return this.form.get('appliesPerCustomer')?.value;
   }

   ngOnInit(): void {
      if (this.data) {
         this.form.patchValue(this.data);
      }

      this.form.valueChanges.subscribe(() => this.sDataTable.result = this.value);
   }
}

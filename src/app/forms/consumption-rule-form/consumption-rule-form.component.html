<form [formGroup]="form" class="row-form">
   <div class="flex-column">
      <mat-form-field>
         <mat-label>Име</mat-label>
         <mat-icon matPrefix>rule</mat-icon>
         <input autocomplete="off" formControlName="name" matInput type="text">
      </mat-form-field>
      <app-customer-group-select [control]="applicableCustomerGroups" [multiple]="true"/>
   </div>
   <div>
      <mat-button-toggle-group class="toggle-group" formControlName="appliesPerCustomer">
         <mat-button-toggle [value]="true">
            <mat-icon>group</mat-icon>
            <mat-label>Таксувай поотделно</mat-label>
         </mat-button-toggle>
         <mat-button-toggle [value]="false">
            <mat-icon>groups</mat-icon>
            <mat-label>Таксувай общо</mat-label>
         </mat-button-toggle>
      </mat-button-toggle-group>
   </div>
   @if (appliesPerCustomer) {
      <div class="flex-column">
         <mat-slide-toggle color="primary" formControlName="appliesForRegularBed">
            <p>Важи на редовно легло</p>
         </mat-slide-toggle>
         <mat-slide-toggle color="primary" formControlName="appliesForAdditionalBed">
            <p>Важи на доп. легло</p>
         </mat-slide-toggle>
      </div>
   }
</form>

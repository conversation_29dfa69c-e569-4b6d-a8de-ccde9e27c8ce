import {Component, inject, Input, OnInit} from '@angular/core';
import {CustomerGroup} from '../../data/customers/customer-group';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
   selector: 'app-customer-group-form',
   templateUrl: './customer-group-form.component.html',
   standalone: false
})
export class CustomerGroupFormComponent implements OnInit {
   @Input() data?: CustomerGroup;
   @Input() edit = false;

   form = inject(UntypedFormBuilder).group({
      id: '',
      singularName: ['', Validators.minLength(0)],
      pluralName: ['', Validators.minLength(0)],
      isContactRequired: [false, Validators.required],
      minAge: null,
      maxAge: null,
      inverseCountMultiplier: [1, Validators.min(1)]
   });

   private sDataTable = inject(DataTableService);

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): CustomerGroup {
      const {inverseCountMultiplier, ...rest} = this.form.value;
      return {
         countMultiplier: 1.0 / inverseCountMultiplier,
         ...rest
      };
   }

   ngOnInit(): void {
      if (this.data) {
         const {countMultiplier, ...rest} = this.data;
         this.form.patchValue({inverseCountMultiplier: 1.0 / countMultiplier, ...rest});
      }

      this.form.valueChanges.subscribe(() => this.sDataTable.result = this.value);
   }
}

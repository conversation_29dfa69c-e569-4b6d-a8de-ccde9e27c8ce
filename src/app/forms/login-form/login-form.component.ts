import {Component, Input} from '@angular/core';
import {UntypedForm<PERSON>uilder, Validators} from '@angular/forms';
import {Credentials} from '../../data/auth/credentials';

@Component({
   selector: 'app-login-form',
   templateUrl: './login-form.component.html',
   styleUrls: ['./login-form.component.scss'],
   standalone: false
})
export class LoginFormComponent {
   @Input() errMsg = '';

   form = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
   });

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Credentials {
      return this.form.value;
   }
}

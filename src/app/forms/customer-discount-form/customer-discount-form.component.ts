import {Component, inject, Input, OnInit} from '@angular/core';
import {CustomerDiscount} from '../../data/customers/customer-discount';
import {
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {DiscountType} from '../../utility/discount';
import {moneyFormGroup} from '../../utility/form-utility';
import {equalIdentifiables} from '../../utility/utility';
import {Consumable} from '../../data/bundles/consumable';
import {ConsumableService} from '../../services/consumable.service';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
   selector: 'app-customer-discount-form',
   templateUrl: './customer-discount-form.component.html',
   standalone: false
})
export class CustomerDiscountFormComponent implements OnInit {
   @Input() data?: CustomerDiscount;
   @Input() edit = false;

   form = this.fb.group({
      id: '',
      name: ['', Validators.required],
      discount: this.fb.group({
         type: DiscountType.percentage,
         amount: moneyFormGroup(this.fb),
         percentage: '',
      }),
      activePricings: [[]],
      applicableConsumables: [[]],
      applicableCustomerGroups: [[]],
      appliesForRegularBed: false,
      appliesForAdditionalBed: false,
      accommodationCondition: null,
   });

   consumables: Consumable[] = [];
   equalConsumables = equalIdentifiables;

   private sConsumable = inject(ConsumableService);
   private sDataTable = inject(DataTableService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): CustomerDiscount {
      const {discount, ...result} = this.form.value;

      const strippedDiscount: any = {type: discount.type};
      if (discount.type === DiscountType.percentage) {
         strippedDiscount.percentage = discount.percentage;
      } else if (discount.type === DiscountType.amount) {
         strippedDiscount.amount = discount.amount;
      }
      result.discount = strippedDiscount;

      return result;
   }

   get discount(): UntypedFormGroup {
      return this.form.get('discount') as UntypedFormGroup;
   }

   get activePricings(): UntypedFormControl {
      return this.form.get('activePricings') as UntypedFormControl;
   }

   get applicableCustomerGroups(): UntypedFormControl {
      return this.form.get('applicableCustomerGroups') as UntypedFormControl;
   }

   get condition(): UntypedFormControl {
      return this.form.get('accommodationCondition') as UntypedFormControl;
   }

   ngOnInit(): void {
      if (this.data) {
         const {
            activePricings,
            accommodationCondition,
            applicableCustomerGroups
         } = this.data;

         if (!activePricings || activePricings.length === 0) {
            this.activePricings.disable();
         }
         if (!applicableCustomerGroups || applicableCustomerGroups.length === 0) {
            this.applicableCustomerGroups.disable();
         }
         if (!accommodationCondition) {
            this.condition.disable();
         }

         this.form.patchValue(this.data);
      }

      this.sConsumable.getAll()
         .subscribe(consumables => this.consumables = consumables);

      this.form.valueChanges.subscribe(() => this.sDataTable.result = this.value);
   }
}

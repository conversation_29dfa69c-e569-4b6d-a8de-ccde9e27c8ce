<form [formGroup]="form" class="two-column-grid">
   <div class="flex-column" style="padding-right: 16px;">
      <mat-form-field>
         <mat-label>Име</mat-label>
         <input autocomplete="off" formControlName="name" matInput type="text">
      </mat-form-field>
      <div>
         <app-discount-input [group]="discount"/>
      </div>
      <mat-form-field>
         <mat-label>Намалени услуги</mat-label>
         <mat-select [compareWith]="equalConsumables"
                     formControlName="applicableConsumables" multiple>
            @for (consumable of consumables; track consumable.id) {
               <mat-option [value]="consumable">{{consumable.name}}</mat-option>
            }
         </mat-select>
      </mat-form-field>
      <div class="flex-column">
         <app-customer-group-select #groups [control]="applicableCustomerGroups"
                                    [multiple]="true"/>
         <mat-slide-toggle (change)="$event.checked ? groups.enable() : groups.disable()"
                           [checked]="groups.enabled"
                           color="primary">
            Важи само за някои клиентски групи
         </mat-slide-toggle>
         <mat-slide-toggle color="primary" formControlName="appliesForRegularBed">
            Важи на редовно легло
         </mat-slide-toggle>
         <mat-slide-toggle color="primary" formControlName="appliesForAdditionalBed">
            Важи на допълнително легло
         </mat-slide-toggle>
      </div>
      <div class="flex-column">
         <app-price-select #prices [control]="activePricings" [multiple]="true"/>
         <mat-slide-toggle (change)="$event.checked ? prices.enable() : prices.disable()"
                           [checked]="prices.enabled"
                           color="primary">
            Важи само за някои ценоразписи
         </mat-slide-toggle>
      </div>
   </div>
   <div>
      <mat-slide-toggle
         (change)="$event.checked ? condition.enable() : condition.disable()"
         [checked]="condition.enabled" color="primary">
         Използвай филтър
      </mat-slide-toggle>
      @if (condition.enabled) {
         <app-accommodation-condition-input formControlName="accommodationCondition"/>
      }
   </div>
</form>

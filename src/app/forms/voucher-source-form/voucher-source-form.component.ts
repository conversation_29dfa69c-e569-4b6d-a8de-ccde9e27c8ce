import {Component, inject, Input, OnInit} from '@angular/core';
import {VoucherSource} from '../../data/voucher';
import {UntypedFormBuilder, UntypedFormControl, Validators} from '@angular/forms';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
   selector: 'app-voucher-source-form',
   templateUrl: './voucher-source-form.component.html',
   standalone: false
})
export class VoucherSourceFormComponent implements OnInit {
   @Input() data?: VoucherSource;
   @Input() edit = false;

   form = inject(UntypedFormBuilder).group({
      id: '',
      name: ['', Validators.required],
      reservationSources: [[], Validators.required]
   });

   private sDataTable = inject(DataTableService);

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): VoucherSource {
      return this.form.value;
   }

   get reservationSources(): UntypedFormControl {
      return this.form.get('reservationSources') as UntypedFormControl;
   }

   ngOnInit(): void {
      if (this.data) {
         this.form.patchValue(this.data);
      }

      this.form.valueChanges.subscribe(v => this.sDataTable.result = v);
   }
}

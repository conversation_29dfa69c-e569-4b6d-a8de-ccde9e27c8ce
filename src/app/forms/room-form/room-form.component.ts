import {Component, inject, Input, OnInit} from '@angular/core';
import {Room} from '../../data/room';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {ConsumableService} from '../../services/consumable.service';
import {Consumable, ConsumableType} from '../../data/bundles/consumable';
import {equalIdentifiables} from '../../utility/utility';
import {DataTableService} from '../../settings/data-table/data-table.service';

@Component({
   selector: 'app-room-form',
   templateUrl: './room-form.component.html',
   standalone: false
})
export class RoomFormComponent implements OnInit {
   @Input() data?: Room;
   @Input() edit = true;

   consumables: Consumable[] = [];
   equalRooms = equalIdentifiables;
   form = inject(UntypedFormBuilder).group({
      id: '',
      name: ['', Validators.required],
      baseConsumable: ['', Validators.required],
      baseCapacity: [1, Validators.min(1)],
      additionalCapacity: [0, Validators.min(0)],
   });

   private sConsumable = inject(ConsumableService);
   private sDataTable = inject(DataTableService);

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): any {
      return this.form.value;
   }

   ngOnInit(): void {
      if (this.data) {
         this.form.patchValue(this.data);
      }

      this.sConsumable.getAll().subscribe(
         cs => this.consumables = cs.filter(c => c.type === ConsumableType.room)
      );

      this.form.valueChanges.subscribe(v => this.sDataTable.result = v);
   }
}

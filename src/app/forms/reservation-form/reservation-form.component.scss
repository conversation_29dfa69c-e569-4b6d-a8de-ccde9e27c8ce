:host ::ng-deep .mat-mdc-form-field {
   min-width: 70%;
}

.reservation-info-grid {
   display: grid;
   grid-template-columns: 55% 5% 10% 10% 5%;
}

.price-grid {
   width: 70%;
   display: grid;
   grid-template-columns: 50% 50%;

   .final-price {
      & > p {
         margin-bottom: 0;
         text-align: center;
      }

      & > .amount {
         font-weight: bold;
      }
   }
}

.info-icon {
   display: flex;
   align-items: center;
   font-size: 1.44em;
}

.bold-text {
   font-weight: bold;
}

.color-button {
   position: relative;
   bottom: -4px;
}

:host ::ng-deep form.readonly * {
   color: black !important;
}

.dark-mode :host ::ng-deep form.readonly * {
   color: #fafafa !important;
}

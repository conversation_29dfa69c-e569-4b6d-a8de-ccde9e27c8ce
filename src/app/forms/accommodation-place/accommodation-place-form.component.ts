import {
   ChangeDetectorRef,
   Component,
   Input,
   OnChanges,
   OnInit,
   SimpleChanges
} from '@angular/core';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {AccommodationPlace} from 'src/app/data/hotel';

@Component({
   selector: 'app-accommodation-place-form',
   templateUrl: './accommodation-place-form.component.html',
   styleUrls: ['./accommodation-place-form.component.scss'],
   standalone: false
})
export class AccommodationPlaceFormComponent implements OnInit, OnChanges {
   @Input() data: Partial<AccommodationPlace> = {};

   form = this.fb.group({
      place: ['', Validators.required],
      accommodationPlaceUin: ['', Validators.required],
   });

   constructor(private fb: UntypedFormBuilder,
               private cd: ChangeDetectorRef) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): any {
      return this.form.value;
   }

   get isChanged(): boolean {
      return this.form.dirty;
   }

   set place(value: AccommodationPlace) {
      this.data = value;
      this.renewData();
      this.form.markAsPristine();
   }

   ngOnInit(): void {
      this.renewData();
   }

   ngOnChanges(changes: SimpleChanges): void {
      if (changes.data) {
         this.renewData();
      }
   }

   private renewData() {
      if (!this.data) {
         return;
      }

      this.form.patchValue(this.data);
      this.cd.detectChanges();
   }
}

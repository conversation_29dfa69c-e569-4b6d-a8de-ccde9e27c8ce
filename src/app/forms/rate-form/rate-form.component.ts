import {AfterViewInit, Component, inject, Input, OnInit, ViewChild} from '@angular/core';
import {Rate, BundleEntry} from '../../data/rate';
import {
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {ID} from '../../data/identifiable';
import {DateTime} from 'luxon';
import {MatDateRangePicker} from '@angular/material/datepicker';
import {moneyFormGroup} from '../../utility/form-utility';
import {BundleService} from '../../services/bundle.service';
import {Bundle} from '../../data/bundles/bundle';
import {CalendarRange, DateRange, DayOfWeek} from '../../data/common';
import {cmpName} from '../../utility/utility';

@Component({
   selector: 'app-rate-form',
   templateUrl: './rate-form.component.html',
   styleUrl: './rate-form.component.scss',
   standalone: false
})
export class RateFormComponent implements OnInit, AfterViewInit {
   @ViewChild(MatDateRangePicker) datePicker!: MatDateRangePicker<DateTime>;

   @Input() data?: Rate;
   @Input() edit!: boolean;

   form = this.fb.group({
      id: '',
      name: ['', Validators.required],
      activeRanges: this.fb.array([]),
      entries: this.fb.array([]),
   });
   bundles: Bundle[] = [];
   days = DayOfWeek;

   start = this.fb.control(null);
   end = this.fb.control(null);

   private sBundle = inject(BundleService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Rate {
      return this.form.value;
   }

   get activeRanges(): UntypedFormArray {
      return this.form.get('activeRanges') as UntypedFormArray;
   }

   get entries(): UntypedFormArray {
      return this.form.get('entries') as UntypedFormArray;
   }

   ngOnInit(): void {
      this.sBundle.getAll().subscribe(bundles => {
         this.bundles = bundles.sort(cmpName);

         if (this.data) {
            const {activeRanges, entries, ...rest} = this.data;
            this.form.patchValue(rest);

            activeRanges.forEach(range => this.addRange(range));
            entries.forEach(entry => this.addEntry(entry));
         }
      });
   }

   ngAfterViewInit(): void {
      this.datePicker.openedStream.subscribe(() => {
         this.start.setValue(null);
         this.end.setValue(null);
      });

      this.datePicker.closedStream.subscribe(() => {
         if (this.start.value && this.end.value) {
            const dateRange = {start: this.start.value, end: this.end.value};
            const calendarRange = {dateRange} as CalendarRange;
            this.addRange(calendarRange);
         }
      });
   }

   addBundleEntry(): void {
      if (this.bundles.length > 0) {
         const entry: BundleEntry = {
            bundleId: this.bundles[0].id,
            bundleRate: {
               price: {amount: 0, currency: 'BGN' as any}
            }
         };
         this.addEntry(entry);
      }
   }

   getBundleName(bundleId: ID): string {
      return this.bundles.find(b => b.id === bundleId)?.name || '';
   }

   getDateRange(index: number): DateRange {
      return this.activeRanges.at(index)?.get('dateRange')?.value as DateRange;
   }

   originalOrder(_: any, __: any) {
      return 0;
   }

   private addRange(range: CalendarRange): void {
      const rangeGroup = this.fb.group({
         dateRange: this.fb.group({
            start: [range.dateRange.start, Validators.required],
            end: [range.dateRange.end, Validators.required]
         }),
         enableWeekDays: this.fb.control(false),
         weekDays: this.fb.control(range.weekDays),
      });

      rangeGroup.get('enableWeekDays')?.valueChanges
         .subscribe(newValue => this.setWeekDaysDisabledState(newValue, rangeGroup));
      rangeGroup.patchValue({
         enableWeekDays: range.weekDays !== undefined
      });

      this.activeRanges.push(rangeGroup);
   }

   private addEntry(entry: BundleEntry): void {
      const priceGroup = moneyFormGroup(this.fb);
      priceGroup.patchValue(entry.bundleRate.price);

      const entryGroup = this.fb.group({
         bundleId: [entry.bundleId, Validators.required],
         bundleRate: this.fb.group({
            price: priceGroup
         })
      });

      this.entries.push(entryGroup);
   }

   private setWeekDaysDisabledState(enabled: boolean, rangeGroup: UntypedFormGroup) {
      const control = rangeGroup.get('weekDays');
      if (enabled) {
         control?.enable();
      } else {
         control?.setValue(undefined);
         control?.disable();
      }
   }
}

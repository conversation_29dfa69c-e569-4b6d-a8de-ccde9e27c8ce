.bundle-entries-section {
  margin-top: 16px;
  
  .bundle-entry {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
    
    .price-input {
      display: flex;
      gap: 8px;
      
      mat-form-field {
        width: 120px;
      }
    }
    
    mat-form-field:first-child {
      flex: 1;
      min-width: 200px;
    }
  }
}

.add-range {
  margin-bottom: 16px;
}

.ranges {
  .range {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 16px;
    
    .remove-range {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.four-column-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  align-items: center;
  
  .colspan-1 { grid-column: span 1; }
  .colspan-2 { grid-column: span 2; }
  .colspan-3 { grid-column: span 3; }
  .colspan-4 { grid-column: span 4; }
}

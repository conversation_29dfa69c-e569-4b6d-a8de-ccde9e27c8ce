import {Component, inject, Input, OnInit, Output} from '@angular/core';
import {
   UntypedFormBuilder,
   UntypedFormControl,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {Offer} from '../../data/offer';
import {DEBOUNCE_TIME, today} from '../../utility/utility';
import {Observable} from 'rxjs';
import {Customer} from '../../data/customers/customer';
import {CustomerGroupService} from '../../services/customer-group.service';
import {moneyFormGroup} from '../../utility/form-utility';
import {debounceTime, startWith} from 'rxjs/operators';
import {fixDates} from '../../utility/reservation-utility';
import {ConsumableService} from '../../services/consumable.service';
import {Consumable, ConsumableType} from '../../data/bundles/consumable';

@Component({
   selector: 'app-offer-form',
   templateUrl: './offer-form.component.html',
   styles: [`
      .offer-form {
         display: flex;
         flex-direction: column;
         align-items: flex-start;
      }
   `],
   standalone: false
})
export class OfferFormComponent implements OnInit {
   @Input() data?: Offer;
   @Output() titularChange!: Observable<Customer>;

   offer$?: Observable<Partial<Offer>>;
   roomTypes$?: Observable<Consumable[]>;
   roomTypeCtrl = this.fb.control('');

   minDate = today();

   form = this.fb.group({
      id: null,
      titular: [null, Validators.required],
      guestGroupCount: this.fb.group({}),
      bundle: ['', Validators.required],
      start: [null, Validators.required],
      end: [null, Validators.required],
      price: moneyFormGroup(this.fb),
      source: ['', Validators.required],
   });

   private sCustomerGroup = inject(CustomerGroupService);
   private sConsumable = inject(ConsumableService);

   constructor(private fb: UntypedFormBuilder) {
      this.titularChange = this.titular.valueChanges;
   }

   get titular(): UntypedFormControl {
      return this.form.get('titular') as UntypedFormControl;
   }

   get bundle(): UntypedFormControl {
      return this.form.get('bundle') as UntypedFormControl;
   }

   get guestGroupCount(): UntypedFormGroup {
      return this.form.get('guestGroupCount') as UntypedFormGroup;
   }

   get source(): UntypedFormControl {
      return this.form.get('source') as UntypedFormControl;
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get pristine(): boolean {
      return this.form.pristine;
   }

   get value(): Offer {
      return fixDates(this.form.value);
   }

   get valueChanges(): Observable<any> {
      return this.form.valueChanges;
   }

   ngOnInit(): void {
      this.sCustomerGroup.getAll().subscribe(cgs => {
         const control = this.guestGroupCount;

         cgs.forEach(cg => control.addControl(cg.id, this.fb.control(0)));

         if (this.data) {
            this.form.patchValue(this.data);
         }
      });

      this.offer$ = this.form.valueChanges.pipe(
         startWith(this.value),
         debounceTime(DEBOUNCE_TIME * 2),
      );

      this.roomTypes$ = this.sConsumable.getAllOfType(ConsumableType.room);
   }
}

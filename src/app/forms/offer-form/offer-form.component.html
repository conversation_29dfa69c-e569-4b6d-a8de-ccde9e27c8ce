<form [formGroup]="form" class="offer-form">
   <app-guest-group-count-input [control]="guestGroupCount"/>
   <mat-form-field class="wide-form-field">
      <mat-label>Период</mat-label>
      <mat-date-range-input [min]="minDate" [rangePicker]="$any(picker)">
         <input formControlName="start" matStartDate placeholder="Начало">
         <input formControlName="end" matEndDate placeholder="Край">
      </mat-date-range-input>
      <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
      <mat-date-range-picker #picker></mat-date-range-picker>
   </mat-form-field>
   <div class="center-flex wide-form-field">
      <mat-form-field>
         <mat-icon matPrefix>filter_list</mat-icon>
         <mat-label>Тип стая</mat-label>
         <mat-select [formControl]="roomTypeCtrl">
            <mat-option value="">Всички</mat-option>
            @if (roomTypes$ | async; as roomTypes) {
               @for (type of roomTypes; track type.id) {
                  <mat-option [value]="type.id">{{type.name}}</mat-option>
               }
            }
         </mat-select>
      </mat-form-field>
      <app-reservation-source-input [control]="source"/>
   </div>
   <app-bundle-select [consumableFilter]="roomTypeCtrl.valueChanges | async"
                      [control]="bundle" [offer]="offer$ | async" wide/>
   <app-customer-select [control]="titular" wide/>
</form>

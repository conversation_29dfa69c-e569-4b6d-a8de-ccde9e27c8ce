import {Component, inject} from '@angular/core';
import {
   FormsModule,
   ReactiveFormsModule,
   UntypedFormBuilder,
   Validators
} from '@angular/forms';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Mat<PERSON>abel} from '@angular/material/form-field';
import {MatInput} from '@angular/material/input';

@Component({
   selector: 'app-new-note-form',
   imports: [
      FormsModule,
      MatFormField,
      MatInput,
      MatLabel,
      ReactiveFormsModule
   ],
   templateUrl: './new-note-form.component.html',
   styleUrl: './new-note-form.component.scss'
})
export class NewNoteFormComponent {
   private fb = inject(UntypedFormBuilder);

   newNote = this.fb.control('', Validators.required);

   get valid() {
      return this.newNote.valid;
   }

   get value() {
      return this.newNote.value;
   }
}

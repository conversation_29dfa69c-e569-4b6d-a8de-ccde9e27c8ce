import {AfterViewInit, Component, inject, Input, OnInit, ViewChild} from '@angular/core';
import {Pricing, PricingRange} from '../../data/pricing';
import {
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {ID} from '../../data/identifiable';
import {DateTime} from 'luxon';
import {MatDateRangePicker} from '@angular/material/datepicker';
import {moneyFormGroup} from '../../utility/form-utility';
import {PricingService} from 'src/app/services/pricing.service';
import {Price} from 'src/app/data/price';
import {DateRange, DayOfWeek} from '../../data/common';
import {cmpName} from '../../utility/utility';

@Component({
   selector: 'app-pricing-form',
   templateUrl: './pricing-form.component.html',
   styleUrl: 'pricing-form.component.scss',
   standalone: false
})
export class PricingFormComponent implements OnInit, AfterViewInit {
   @ViewChild(MatDateRangePicker) datePicker!: MatDateRangePicker<DateTime>;

   @Input() data?: Pricing;
   @Input() edit!: boolean;

   form = this.fb.group({
      id: '',
      name: ['', Validators.required],
      ranges: this.fb.array([]),
      bundlePrices: this.fb.group({}),
   });
   prices: Price[] = [];
   days = DayOfWeek;

   start = this.fb.control(null);
   end = this.fb.control(null);

   private sPricing = inject(PricingService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): Pricing {
      return this.form.value;
   }

   get ranges(): UntypedFormArray {
      return this.form.get('ranges') as UntypedFormArray;
   }

   ngOnInit(): void {
      this.sPricing.getAllPrices().subscribe(prices => {
         this.prices = prices.sort(cmpName);

         const group = this.form.get('bundlePrices') as UntypedFormGroup;
         this.prices.forEach(p => group.addControl(p.id, moneyFormGroup(this.fb)));

         if (this.data) {
            const {ranges, ...rest} = this.data;
            this.form.patchValue(rest);

            ranges.forEach(r => this.addRange(r));
         }
      });
   }

   ngAfterViewInit(): void {
      this.datePicker.openedStream.subscribe(() => {
         this.start.setValue(null);
         this.end.setValue(null);
      });

      this.datePicker.closedStream.subscribe(() => {
         if (this.start.value && this.end.value) {
            const dateRange = {start: this.start.value, end: this.end.value};
            const pricingRange = {dateRange, priority: 0} as PricingRange;
            this.addRange(pricingRange);
         }
      });
   }

   getPriceControl(id: ID): UntypedFormGroup {
      return (this.form.get('bundlePrices') as UntypedFormGroup).get(
         id) as UntypedFormGroup;
   }

   getDateRange(index: number): DateRange {
      return this.ranges.at(index)?.get('dateRange')?.value as DateRange;
   }

   originalOrder(_: any, __: any) {
      return 0;
   }

   // TODO(vlado): Don't invalidate the form control after setting an empty value
   private addRange(range: PricingRange): void {
      const rangeGroup = this.fb.group({
         dateRange: this.fb.group({
            start: [range.dateRange.start, Validators.required],
            end: [range.dateRange.end, Validators.required]
         }),
         enableWeekDays: this.fb.control(false),
         activeWeekDays: this.fb.control(range.activeWeekDays),
         priority: this.fb.control(range.priority)
      });

      rangeGroup.get('enableWeekDays')?.valueChanges
         .subscribe(newValue => this.setWeekDaysDisabledState(newValue, rangeGroup));
      rangeGroup.patchValue({
         enableWeekDays: range.activeWeekDays !== undefined
      });

      this.ranges.push(rangeGroup);
   }

   private setWeekDaysDisabledState(enabled: boolean, rangeGroup: UntypedFormGroup) {
      const control = rangeGroup.get('activeWeekDays');
      if (enabled) {
         control?.enable();
      } else {
         control?.setValue(undefined);
         control?.disable();
      }
   }
}

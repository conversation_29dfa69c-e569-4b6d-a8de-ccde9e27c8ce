<form [formGroup]="form" class="flex-row no-overflow">
   <div class="flex-column" style="margin-right: 20px">
      <mat-form-field>
         <mat-label>Име</mat-label>
         <input autocomplete="off" formControlName="name" matInput type="text">
      </mat-form-field>
      <div class="native-input-table">
         <div class="row header">
            <span>Пакет</span>
            <span>Цена (лв.)</span>
         </div>
         @for (price of prices; track price.id) {
            <div [formGroup]="getPriceControl(price.id)" class="row data">
               <span>{{price.name}}</span>
               <input autocomplete="off" class="mat-app-background"
                      formControlName="amount" matInput type="number">
            </div>
         }
      </div>
   </div>
   <div>
      <div class="flex-column add-range">
         <mat-form-field style="height: 0; width: 0; visibility:hidden;">
            <mat-date-range-input [rangePicker]="picker">
               <input [formControl]="start" matStartDate placeholder="Началo">
               <input [formControl]="end" matEndDate placeholder="Край">
            </mat-date-range-input>
            <mat-date-range-picker #picker></mat-date-range-picker>
         </mat-form-field>
         <div>
            <button (click)="picker.open()" color="primary" mat-raised-button>
               <mat-icon>add</mat-icon>
               Добавяне на период
            </button>
         </div>
      </div>
      <div class="ranges" formArrayName="ranges">
         @for (_ of ranges.controls; track _) {
            <div [formGroupName]="$index" class="range four-column-grid">
               <div class="colspan-3">
                  {{getDateRange($index) | dateRanges}}
               </div>
               <div class="remove-range colspan-1">
                  <button (click)="ranges.removeAt($index)" mat-icon-button color="warn">
                     <mat-icon>remove_circle</mat-icon>
                  </button>
               </div>
               <div class="colspan-2"></div>
               <mat-slide-toggle formControlName="enableWeekDays" class="colspan-2">
                  Седмичен филтър
               </mat-slide-toggle>
               <mat-form-field class="colspan-2">
                  <mat-label>Приоритет</mat-label>
                  <input autocomplete="off" formControlName="priority" matInput
                         type="number">
               </mat-form-field>
               <mat-form-field class="colspan-2">
                  <mat-label>Активни дни</mat-label>
                  <mat-select formControlName="activeWeekDays" multiple>
                     @for (day of days | keyvalue: originalOrder; track day) {
                        <mat-option [value]="day.key">{{day.value}}</mat-option>
                     }
                  </mat-select>
               </mat-form-field>
            </div>
         }
      </div>
   </div>
</form>

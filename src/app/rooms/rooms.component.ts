import {AfterViewInit, Component, inject, OnInit, ViewChild} from '@angular/core';
import {Room} from '../data/room';
import {RoomService} from '../services/room.service';
import {MatPaginator} from '@angular/material/paginator';
import {MatTableDataSource} from '@angular/material/table';

@Component({
   selector: 'app-rooms',
   templateUrl: './rooms.component.html',
   styles: [`
      .rooms-container {
         width: 80%;
         margin: 24px auto;
      }

      .rooms-table {
         width: 100%;
      }
   `],
   standalone: false
})
export class RoomsComponent implements OnInit, AfterViewInit {
   @ViewChild(MatPaginator) paginator!: MatPaginator;

   dataSource = new MatTableDataSource<Room>();

   private sRoom = inject(RoomService);

   ngOnInit(): void {
      this.renewData();
   }

   ngAfterViewInit(): void {
      this.dataSource.paginator = this.paginator;
   }

   renewData(): void {
      this.sRoom.getAll().subscribe(rooms => this.dataSource.data = rooms);
   }
}

import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {CachingService} from '../services/caching.service';
import {MatDrawer} from '@angular/material/sidenav';
import {ReservationService} from '../services/reservation.service';
import {RoomSwapComponent} from '../components/calendar/room-swap.component';
import {CalendarComponent, RoomSwapData} from '../components/calendar/calendar.component';

@Component({
   selector: 'app-reservations',
   template: `
      @if (cache.initialized) {
         <mat-drawer-container [hasBackdrop]="false">
            <mat-drawer #drawer [autoFocus]="false" position="end"
                        style="margin-top: 75px;padding: 20px;border-top-left-radius: 20px;">
               <div class="flex-row" style="justify-content: space-between;">
                  <button (click)="drawer.close()" color="warn" mat-icon-button>
                     <mat-icon>close</mat-icon>
                  </button>
                  <button [disabled]="roomSwap.clean || roomSwap.hasConflicts"
                          (click)="swapRooms()" color="primary" mat-raised-button>
                     <mat-icon>published_with_changes</mat-icon>
                     Размени стаите
                  </button>
               </div>
               <app-room-swap #roomSwap [data]="data"/>
            </mat-drawer>

            <app-calendar (swapRooms)="openSwapRoomsDrawer($event)"/>
         </mat-drawer-container>
      }
   `,
   styles: [`
      :host ::ng-deep .mat-drawer-container {
         overflow: unset;
      }

      :host ::ng-deep .mat-drawer-content {
         overflow: unset;
      }
   `],
   standalone: false
})
export class ReservationsComponent implements OnInit {
   @ViewChild(MatDrawer) drawer!: MatDrawer;
   @ViewChild(RoomSwapComponent) swap!: RoomSwapComponent;
   @ViewChild(CalendarComponent) calendar!: CalendarComponent;

   cache = inject(CachingService);
   data?: RoomSwapData;

   private sReservation = inject(ReservationService);

   ngOnInit(): void {
      this.cache.init();
   }

   openSwapRoomsDrawer(data: RoomSwapData) {
      this.data = data;
      this.drawer.open().then();
   }

   swapRooms(): void {
      this.sReservation.swapRooms(this.swap.value).subscribe({
         next: () => {
            this.drawer.close().then();
            this.calendar.renewReservationsInView();
         },
         error: () => this.drawer.close().then(),
      });
   }
}

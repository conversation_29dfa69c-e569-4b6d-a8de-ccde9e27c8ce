import {inject, Injectable} from '@angular/core';
import {
   HttpErrorResponse,
   HttpEvent,
   HttpHandler,
   HttpInterceptor,
   HttpRequest
} from '@angular/common/http';
import {BehaviorSubject, catchError, Observable, take, throwError} from 'rxjs';
import {AuthService} from './auth.service';
import {filter, switchMap} from 'rxjs/operators';
import {NotificationService} from '../services/notification.service';
import {MatDialog} from '@angular/material/dialog';

@Injectable()
export class RefreshTokenInterceptor implements HttpInterceptor {
   private refreshInProgress = false;
   private subject = new BehaviorSubject<any>(null);

   private sNotification = inject(NotificationService);
   private sAuth = inject(AuthService);
   private dialog = inject(MatDialog);

   intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
      return next.handle(req).pipe(
         catchError(err => {
            if (req.url.endsWith('operator/login')) {
               return throwError(() => err);
            }
            if (req.url.endsWith('refresh-token')) {
               this.sAuth.logout();
               return this.handleExpiredAuth(err);
            }

            if (err.status !== 401) {
               return throwError(() => err);
            }

            if (this.refreshInProgress) {
               return this.subject.pipe(
                  filter(result => result !== null),
                  take(1),
                  switchMap(() => next.handle(req)),
               );
            } else {
               this.refreshInProgress = true;
               this.subject.next(null);

               return this.sAuth.refreshToken().pipe(
                  switchMap(token => {
                     this.refreshInProgress = false;
                     this.subject.next(token);

                     return next.handle(req);
                  }),
                  catchError(error => {
                     this.refreshInProgress = false;
                     this.sAuth.logout();

                     return this.handleExpiredAuth(error);
                  })
               );
            }
         })
      );
   }

   private handleExpiredAuth(error: HttpErrorResponse): Observable<HttpEvent<any>> {
      this.dialog.closeAll();
      this.sNotification.displayNotification(
         'Сесията ви е изтекла. Моля впишете се отново.', 'Ок!');
      return throwError(() => error);
   }
}

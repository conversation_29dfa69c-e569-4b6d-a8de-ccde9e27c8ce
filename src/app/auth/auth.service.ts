import {inject, Injectable, signal, Signal} from '@angular/core';
import {Router} from '@angular/router';
import {DateTime, Settings} from 'luxon';
import {catchError, Observable, of, tap, throwError} from 'rxjs';
import {AuthToken, AuthTokenPayload} from '../data/auth/auth-token-payload';
import {serverUrl} from '../utility/http-utility';
import {Credentials} from '../data/auth/credentials';
import {environment} from '../../environments/environment';
import {Operator, Privilege} from '../data/auth/operator';
import {OperatorService} from '../services/operator.service';
import {map} from 'rxjs/operators';

const tokenKey = 'token';
const refreshTokenKey = 'refresh-token';

const decodeToken = (token: string): AuthTokenPayload => {
   const parts = token.split('.');
   const decoded = window.atob(parts[1]);
   const rawPayload = JSON.parse(decoded);

   return {
      id: rawPayload.id,
      issuer: rawPayload.iss,
      issuedAt: DateTime.fromSeconds(rawPayload.iat),
      expiresAt: DateTime.fromSeconds(rawPayload.exp),
      subject: rawPayload.sub,
   };
};

@Injectable({
   providedIn: 'root'
})
export class AuthService {
   baseUrl = serverUrl('operator');
   token: string | null = null;

   privileges: Record<Privilege, boolean> = {
      [Privilege.manualPrice]: false,
      [Privilege.bankPayments]: false,
      [Privilege.balanceEdit]: false,
   };

   private storage = environment.production ? sessionStorage : localStorage;
   private currentId = '';

   private router = inject(Router);
   private sOperator = inject(OperatorService);
   private _current = signal<Operator | undefined>(undefined);

   constructor() {
      const token = this.storage.getItem(tokenKey);
      if (token) {
         const payload = decodeToken(token);
         if (payload.expiresAt <= DateTime.now()) {
            this.storage.removeItem(tokenKey);
         } else {
            this.saveToken(token, payload);
         }
      }
   }

   get current(): Signal<Operator | undefined> {
      return this._current.asReadonly();
   }

   init(): Observable<void> {
      if (this.currentId) {
         return this.sOperator.get(this.currentId).pipe(map(operator => {
            Object.values(Privilege).forEach(privilege => {
               this.privileges[privilege] = operator.privileges.includes(privilege);
            });

            this._current.set(operator);
         }));
      } else {
         return of(undefined);
      }
   }

   login(credentials: Credentials): Observable<AuthToken> {
      return this.sOperator.login(credentials).pipe(tap(data => this.onNewToken(data)));
   }

   logout(): void {
      this.storage.removeItem(tokenKey);
      this.storage.removeItem(refreshTokenKey);
      this.token = null;
      this.router.navigate(['']).then(() => {
         location.reload();
      });
   }

   loggedIn(): boolean {
      return !!this.token;
   }

   refreshToken(): Observable<AuthToken> {
      const token = this.storage.getItem(refreshTokenKey);
      if (token) {
         return this.sOperator.refreshToken(token).pipe(
            tap(data => this.onNewToken(data)),
            catchError(err => {
               this.storage.removeItem(refreshTokenKey);
               return throwError(() => err);
            })
         );
      } else {
         return throwError(() => new Error('Липсващ токен. Моля впишете се пак.'));
      }
   }

   private onNewToken(authToken: AuthToken): void {
      this.saveToken(authToken.token);
      this.storage.setItem(refreshTokenKey, authToken.refreshToken);
      Settings.defaultZone = authToken.timeZone;
   }

   private saveToken(token: string, payload?: AuthTokenPayload): void {
      this.storage.setItem(tokenKey, token);
      this.token = token;

      payload = payload ?? decodeToken(token);

      this.currentId = payload.id;
   }
}

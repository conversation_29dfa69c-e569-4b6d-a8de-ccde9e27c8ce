import {inject, Injectable} from '@angular/core';
import {
   HttpErrorResponse,
   HttpEvent,
   HttpHandler,
   HttpInterceptor,
   HttpRequest
} from '@angular/common/http';
import {catchError, Observable, throwError} from 'rxjs';
import {AuthService} from './auth.service';
import {environment} from '../../environments/environment';
import {NotificationService} from '../services/notification.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
   private sAuth = inject(AuthService);
   private sNotification = inject(NotificationService);

   intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
      req = req.clone({setHeaders: {'x-api-token': environment.server.apiToken}});
      if (this.sAuth.token && !req.url.includes('refresh-token')) {
         req = req.clone({setHeaders: {authorization: `Bear<PERSON> ${this.sAuth.token}`}});
      }
      return next.handle(req)
         .pipe(catchError(err => this.handleAuthError(err)));
   }

   private handleAuthError(error: HttpErrorResponse): Observable<HttpEvent<any>> {
      if (error.status === 403) {
         this.sNotification.displayNotification('Забранен достъп', 'Ок!');
      }

      return throwError(() => error);
   }
}

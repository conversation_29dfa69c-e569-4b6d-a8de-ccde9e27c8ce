<div class="settings-container">
   <mat-nav-list class="settings-list">
      @for (element of settingsItems; track element.link) {
         <mat-list-item (click)="loadSetting(element.link)" class="no-select">
            <span matListItemIcon>
               <mat-icon class="material-icons-round"
                         [class.mat-primary]="router.url.endsWith(element.link)">
                  {{element.icon}}
               </mat-icon>
            </span>
            <div matListItemTitle>{{element.name}}</div>
         </mat-list-item>
      }
   </mat-nav-list>
   <div class="setting-display">
      <router-outlet></router-outlet>
   </div>
</div>

import {Component, inject, OnInit} from '@angular/core';
import {RateService} from '../../../services/rate.service';
import {Rate} from '../../../data/rate';
import {MatDialog} from '@angular/material/dialog';
import {filter, switchMap} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {NotificationService} from '../../../services/notification.service';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {RateFormComponent} from '../../../forms/rate-form/rate-form.component';
import {
   RateDialogComponent,
   RateDialogInput
} from '../../../dialogs/rate-dialog/rate-dialog.component';
import {rangesToString} from '../../../utility/utility';

@Component({
   selector: 'app-rate-settings',
   templateUrl: './rate-settings.component.html',
   standalone: false
})
export class RateSettingsComponent implements OnInit {
   rates$?: Observable<Rate[]>;
   rateHeaders = [
      'Име',
      'Активни периоди',
      'Източници',
      'Брой пакети',
   ];
   rateProperties = [
      'name',
      (r: Rate) => r.activeRanges.length > 0 ?
         r.activeRanges.map(range => rangesToString([range.dateRange])).join(', ') :
         'Няма периоди',
      (r: Rate) => r.reservationSources && r.reservationSources.length > 0 ?
         r.reservationSources.map(source => source.name).join(', ') :
         'Всички източници',
      (r: Rate) => `${r.entries.length} пакет${r.entries.length !== 1 ? 'а' : ''}`,
   ];

   rateForm = RateFormComponent;
   DataType = DataType;

   sDataTable = inject(DataTableService);

   private sRate = inject(RateService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => {
            if (type === DataType.Rate) {
               this.sRate.update(data)
                  .subscribe(() => this.renewData());
            }
         },
         ({type, data}) => {
            if (type === DataType.Rate) {
               this.deleteRates(data);
            }
         }
      );
   }

   ngOnInit(): void {
      this.renewData();
   }

   addRate(): void {
      this.openRateDialog(false)
         .pipe(switchMap(result => this.sRate.add(result)))
         .subscribe(() => this.renewData());
   }

   private renewData(): void {
      this.rates$ = this.sRate.getAll();
   }

   private deleteRates(rates: Rate[]): void {
      const rateNames = rates.map(r => r.name).join(', ');
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на тарифи',
         description: [
            `Сигурни ли сте, че искате да изтриете следните тарифи: ${rateNames}?`,
            'Това действие е необратимо!'
         ],
         yesText: 'Да, изтрий тарифите!',
      }).pipe(
         switchMap(() => this.sRate.batchDelete(rates.map(r => r.id)))
      ).subscribe(() => this.renewData());
   }

   private openRateDialog(edit: boolean, rate?: Rate): Observable<Rate> {
      const data: RateDialogInput = {edit, data: rate};
      const dialog = this.dialog.open(RateDialogComponent, {
         data,
         width: '800px',
         maxHeight: '90vh'
      });

      return dialog.afterClosed().pipe(filter(result => !!result));
   }
}

<div class="settings-header">
   <div class="description">
      <h1>Тарифи</h1>
      <h3><i>
         Управление на тарифи с различни цени за пакети и активни периоди.
      </i></h3>
   </div>

   <button (click)="addRate()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нова тариф
   </button>
</div>

<app-data-table [data]="rates$ | async" [formClass]="rateForm"
                [headers]="rateHeaders" [properties]="rateProperties"
                [type]="DataType.Rate" search/>

<div class="settings-header">
   <h1>Данни за хотела</h1>
   <button (click)="updateHotel()" [disabled]="!hotelChanged" color="primary"
           mat-raised-button>
      <mat-icon>save</mat-icon>
      Запази промените
   </button>
</div>

<div class="flex-row">
   <app-bank-form [data]="hotel?.bank"/>
   <app-accommodation-place-form [data]="hotel?.accommodationPlace"/>
</div>

<div class="settings-header">
   <h1>Юридическо лице за фактури</h1>

   <button (click)="updateCustomer()" [disabled]="!customerChanged" color="primary"
           mat-raised-button>
      <mat-icon>save</mat-icon>
      Запази промените
   </button>
</div>

<div style="width: 70%">
   <app-customer-form [allRequired]="true" [data]="legalEntity"
                      [hideCustomerFields]="true" [preselectedTab]="1"/>
</div>

<div class="settings-header">
   <h1>Пореден номер на фактури</h1>

   <button (click)="updateInvoices()" [disabled]="!invoicesChanged" color="primary"
           mat-raised-button>
      <mat-icon>save</mat-icon>
      Запази промените
   </button>
</div>
<div class="flex-row child-hm-s">
   @for (category of invoiceCategories; track category.id) {
      <app-sequence-input [control]="invoiceNumberControls[$index]"
                          [name]="category.name"/>
   }
</div>

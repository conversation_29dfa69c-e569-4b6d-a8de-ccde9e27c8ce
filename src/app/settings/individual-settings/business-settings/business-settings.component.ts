import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {UntypedFormControl, Validators} from '@angular/forms';
import {forkJoin, map, Observable, tap, throwError} from 'rxjs';
import {Customer, CustomerType} from 'src/app/data/customers/customer';
import {Hotel} from 'src/app/data/hotel';
import {ID} from 'src/app/data/identifiable';
import {InvoiceCategory, InvoiceCategoryType} from 'src/app/data/invoice';
import {
   AccommodationPlaceFormComponent
} from 'src/app/forms/accommodation-place/accommodation-place-form.component';
import {BankFormComponent} from 'src/app/forms/bank-form/bank-form.component';
import {CustomerFormComponent} from 'src/app/forms/customer-form/customer-form.component';
import {CustomerService} from 'src/app/services/customer.service';
import {HotelService} from 'src/app/services/hotel.service';
import {InvoiceCategoryService} from 'src/app/services/invoice-category.service';
import {switchMap} from 'rxjs/operators';

@Component({
   selector: 'app-business-settings',
   templateUrl: './business-settings.component.html',
   standalone: false
})
export class BusinessSettingsComponent implements OnInit {
   @ViewChild(CustomerFormComponent) customerForm!: CustomerFormComponent;
   @ViewChild(BankFormComponent) bankForm!: BankFormComponent;
   @ViewChild(AccommodationPlaceFormComponent)
   placeForm!: AccommodationPlaceFormComponent;

   hotel: Hotel | null = null;
   legalEntity: Customer | null = null;
   invoiceCategories: InvoiceCategory[] = [];
   invoiceNumberControls: UntypedFormControl[] = [];

   private sHotel = inject(HotelService);
   private sCustomer = inject(CustomerService);
   private sInvoiceCategory = inject(InvoiceCategoryService);

   get customerChanged(): boolean {
      return this.customerForm && this.customerForm.isChanged && this.customerForm.valid;
   }

   get hotelChanged(): boolean {
      return (this.bankForm && this.bankForm.isChanged && this.bankForm.valid)
         || (this.placeForm && this.placeForm.isChanged && this.placeForm.valid);
   }

   get invoicesChanged(): boolean {
      return this.invoiceNumberControls.some(ctrl => ctrl.dirty && ctrl.valid);
   }

   ngOnInit(): void {
      forkJoin({
         hotel: this.renewHotel(),
         invoices: this.renewInvoices()
      }).pipe(
         switchMap(({hotel}) => this.renewCustomer(hotel.id))
      ).subscribe(() => {
         if (this.legalEntity) {
            this.customerForm.customer = this.legalEntity;
         }
         if (this.hotel) {
            this.bankForm.bank = this.hotel.bank;
            this.placeForm.place = this.hotel.accommodationPlace;
         }
      });
   }

   updateCustomer() {
      this.sCustomer.update(this.customerForm.value).pipe(
         switchMap(() => this.hotel ? this.renewCustomer(this.hotel.id) :
            throwError(() => new Error('Missing hotel')))
      ).subscribe(() => {
         if (this.legalEntity) {
            this.customerForm.customer = this.legalEntity;
         }
      });
   }

   updateHotel() {
      if (!this.hotel) {
         console.error('Missing hotel');
         return;
      }

      const updatedHotel = Object.assign(
         this.hotel,
         {bank: this.bankForm.value},
         {accommodationPlace: this.placeForm.value}
      );

      this.sHotel.update(updatedHotel).pipe(
         switchMap(() => this.renewHotel())
      ).subscribe(hotel => {
         this.bankForm.bank = hotel.bank;
         this.placeForm.place = hotel.accommodationPlace;
      });
   }

   updateInvoices() {
      const updatedCategories = [];
      for (const [index, control] of this.invoiceNumberControls.entries()) {
         if (control.dirty && control.valid) {
            const updatedCategory = this.invoiceCategories[index];
            updatedCategory.nextNumber = control.value;
            updatedCategories.push(updatedCategory);
         }
      }

      forkJoin(updatedCategories.map(c => this.sInvoiceCategory.update(c)))
         .pipe(switchMap(() => this.renewInvoices()))
         .subscribe();
   }

   private renewHotel(): Observable<Hotel> {
      return this.sHotel.getCurrent().pipe(tap(hotel => this.hotel = hotel));
   }

   private renewCustomer(customerId: ID): Observable<Customer> {
      return this.sCustomer.get(customerId).pipe(
         map(customer => {
            if (customer.type == CustomerType.legal) {
               this.legalEntity = customer;
               return this.legalEntity;
            } else {
               this.legalEntity = null;
               console.error('Expected legal customer but got: ', customer);
               throw new Error('Невалидно юридическо лице');
            }
         })
      );
   }

   private renewInvoices(): Observable<InvoiceCategory[]> {
      return this.sInvoiceCategory.getAll().pipe(
         map(categories => categories.filter(c => c.type != InvoiceCategoryType.creditNote)),
         tap(categories => {
            this.invoiceCategories = categories;
            this.invoiceNumberControls = categories.map(c =>
               new UntypedFormControl(c.nextNumber,
                  [Validators.min(c.nextNumber), Validators.required]));
         })
      );
   }
}

<div class="settings-header">
   <h1>Ваучери</h1>
   <button (click)="addVoucher()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нов ваучер
   </button>
</div>

<app-data-table [data]="vouchers$ | async" [formClass]="VoucherSourceFormComponent"
                [headers]="voucherHeaders" [properties]="voucherProperties"
                [type]="DataType.VoucherSource" noPaginator/>

<div class="settings-header">
   <div class="description">
      <h1>Източници на резервации</h1>
      <h3><i>
         От тук може да изберете източникът, който ще бъде маркиран автоматично при
         правене на резервации и оферти.
      </i></h3>
   </div>
   <button (click)="saveDefault()" [disabled]="!dirty" color="primary" mat-raised-button>
      <mat-icon>save</mat-icon>
      Запази промените
   </button>
</div>

<mat-radio-group (change)="dirty = true" [(ngModel)]="defaultSource" class="flex-column">
   @for (source of sources; track source.id) {
      <mat-radio-button [value]="$index" color="primary">
         {{source.name}}
      </mat-radio-button>
   }
</mat-radio-group>

<div [formGroup]="sourceForm" class="input-with-button">
   <mat-form-field>
      <input formControlName="name" matInput placeholder="Нов източник" type="text">
   </mat-form-field>
   <mat-form-field>
      <mat-select formControlName="code">
         <mat-option [value]="undefined"></mat-option>
         @for (code of sourceCodes; track code.value) {
            <mat-option [value]="code.value">{{code.label}}</mat-option>
         }
      </mat-select>
   </mat-form-field>
   <button (click)="addSource()" mat-icon-button>
      <mat-icon>add</mat-icon>
   </button>
</div>

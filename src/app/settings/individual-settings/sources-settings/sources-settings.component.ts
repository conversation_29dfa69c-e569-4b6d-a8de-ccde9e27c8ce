import {
   AfterViewInit,
   ChangeDetectorRef,
   Component,
   inject,
   ViewChild
} from '@angular/core';
import {ReservationSourceService} from '../../../services/reservation-source.service';
import {
   ReservationSource,
   ReservationSourceCode,
   ReservationSourceLables
} from '../../../data/reservation-source';
import {MatRadioGroup} from '@angular/material/radio';
import {VoucherSource} from '../../../data/voucher';
import {VoucherSourceService} from '../../../services/voucher-source.service';
import {filter, map, switchMap} from 'rxjs/operators';
import {NotificationService} from '../../../services/notification.service';
import {Observable} from 'rxjs';
import {MatDialog} from '@angular/material/dialog';
import {cmpName, ids, nameJoin} from '../../../utility/utility';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {
   VoucherSourceFormComponent
} from '../../../forms/voucher-source-form/voucher-source-form.component';
import {DataTableComponent} from '../../data-table/data-table.component';
import {
   DynamicFormDialogComponent
} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';
import {FormBuilder, Validators} from '@angular/forms';

@Component({
   selector: 'app-sources-settings',
   templateUrl: './sources-settings.component.html',
   standalone: false
})
export class SourcesSettingsComponent implements AfterViewInit {
   @ViewChild(MatRadioGroup) radio!: MatRadioGroup;
   @ViewChild(DataTableComponent) dataTable!: DataTableComponent;

   sources: ReservationSource[] = [];
   defaultSource = 0;
   dirty = false;

   vouchers$?: Observable<VoucherSource[]>;
   voucherHeaders = ['Име', 'Източници'];
   voucherProperties = [
      'name',
      (vs: VoucherSource) => vs.reservationSources.map(v => v.name)
   ];

   DataType = DataType;
   VoucherSourceFormComponent = VoucherSourceFormComponent;
   sourceCodes = Object.values(ReservationSourceCode).map(value => ({
      value,
      label: ReservationSourceLables[value]
   }));

   private sReservationSource = inject(ReservationSourceService);
   private sVoucherSource = inject(VoucherSourceService);
   private sNotification = inject(NotificationService);
   private sDataTable = inject(DataTableService);
   private dialog = inject(MatDialog);
   private cd = inject(ChangeDetectorRef);
   private fb = inject(FormBuilder);

   sourceForm = this.fb.group({
      name: ['', Validators.required],
      code: this.fb.control(undefined)
   });

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => type === DataType.VoucherSource &&
            this.sVoucherSource.update(data)
               .subscribe(() => this.renewVoucherSources()),
         ({type, data}) => type === DataType.VoucherSource && this.deleteVouchers(data)
      );
   }

   ngAfterViewInit(): void {
      this.renewData();
   }

   saveDefault() {
      const newDefault = this.sources[this.defaultSource];
      this.sReservationSource.update({...newDefault, isDefault: true})
         .subscribe(() => this.renewReservationSources());
   }

   addVoucher(): void {
      this.openVoucherDialog()
         .pipe(switchMap(result => this.sVoucherSource.add(result)))
         .subscribe(() => this.renewVoucherSources());
   }

   deleteVouchers(vouchers: VoucherSource[]): void {
      const names = nameJoin(vouchers);

      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на ваучер',
         description: `Сигурни ли сте, че искате да изтриете "${names}"?`,
         yesText: `Да, изтрий ваучерите!`,
      }).pipe(
         switchMap(() => this.sVoucherSource.batchDelete(ids(vouchers)))
      ).subscribe({
         next: () => {
            this.renewVoucherSources();
            this.dataTable.clearSelection();
         },
         error: () => this.dataTable.clearSelection()
      });
   }

   addSource() {
      if (this.sourceForm.valid) {
         const {name, code} = this.sourceForm.value;
         const value = {name, code, isDefault: false} as ReservationSource;
         this.sReservationSource.add(value).subscribe(() => {
            this.sourceForm.reset();
            this.renewReservationSources();
         });
      }
   }

   private renewData(): void {
      this.renewReservationSources();
      this.renewVoucherSources();
   }

   private renewReservationSources(): void {
      this.sReservationSource.getAll().subscribe(sources => {
         this.dirty = false;
         const receptionFirst = (fa1: ReservationSource, fa2: ReservationSource) =>
            // @ts-expect-error You CAN subtract booleans
            !fa2.financialAccount - !fa1.financialAccount;
         this.sources = sources.sort(cmpName).sort(receptionFirst);

         this.defaultSource = this.sources.findIndex(s => s.isDefault);
         this.cd.detectChanges();
      });
   }

   private renewVoucherSources(): void {
      this.vouchers$ = this.sVoucherSource.getAll().pipe(map(vs => vs.sort(cmpName)));
   }

   private openVoucherDialog(): Observable<any> {
      return this.dialog.open(DynamicFormDialogComponent, {
         data: {form: VoucherSourceFormComponent, title: 'Създаване на ваучер'},
      }).afterClosed().pipe(filter(result => !!result));
   }
}

import {Component, inject, ViewChild} from '@angular/core';
import {UntypedFormBuilder, UntypedFormControl, Validators} from '@angular/forms';
import {MatSlideToggle, MatSlideToggleChange} from '@angular/material/slide-toggle';
import {Feature, FeatureService} from '../../../services/feature.service';
import {FiscalService} from '../../../services/fiscal.service';
import {NotificationService} from '../../../services/notification.service';
import {FiscalPrinter} from '../../../data/fiscal/fiscal';

@Component({
   selector: 'app-fiscal-settings',
   templateUrl: './fiscal-settings.component.html',
   standalone: false
})
export class FiscalSettingsComponent {
   @ViewChild(MatSlideToggle) toggle!: MatSlideToggle;

   printer?: FiscalPrinter;
   form = this.fb.group({
      id: ['', Validators.required],
      printerId: ['', Validators.required],
      receiptNumber: [0, Validators.required],
      serialNumber: ['', Validators.required],
   });

   sFeature = inject(FeatureService);
   private sFiscal = inject(FiscalService);
   private sNotification = inject(NotificationService);

   constructor(private fb: UntypedFormBuilder) {
      this.renewData();
   }

   get disableSave(): boolean {
      return !this.form.valid || !this.form.dirty;
   }

   get receiptNumber(): UntypedFormControl {
      return this.form.get('receiptNumber') as UntypedFormControl;
   }

   toggleFeature(change: MatSlideToggleChange): void {
      const action = change.checked ? this.sFeature.enable(Feature.fiscal) :
         this.sFeature.disable(Feature.fiscal);

      action.subscribe(() => {
         this.sFeature.fiscal.set(change.checked);
         setTimeout(() => this.renewData(), 1000);
      });
   }

   save(): void {
      const printer = this.form.value;
      if (this.form.valid && printer) {
         this.sFiscal.update(printer).subscribe({
            next: () => this.form.markAsPristine(),
            error: err => this.sNotification.displayError(err),
         });
      }
   }

   private renewData(): void {
      this.sFiscal.getAll().subscribe(ps => {
         if (ps.length > 0) {
            const printer = ps[0];
            this.printer = printer;
            this.form.setValue(printer);
            this.form.controls.receiptNumber.addValidators(
               [Validators.required, Validators.min(printer.receiptNumber)]);
         }
      });
   }
}

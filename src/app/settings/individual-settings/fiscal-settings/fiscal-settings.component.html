<div class="settings-header">
   <h1>Фискализация</h1>

   @if (sFeature.fiscal() && printer) {
      <button (click)="save()" [disabled]="disableSave" color="primary" mat-raised-button>
         <mat-icon>save</mat-icon>
         Запази промените
      </button>
   }
</div>

<mat-slide-toggle (change)="toggleFeature($event)" [checked]="sFeature.fiscal()">
   Изисквай връзка с фискално устройство
</mat-slide-toggle>

@if (sFeature.fiscal() && printer) {
   <div class="mat-h2" style="margin-top: 50px;">
      Принтер {{printer.serialNumber}}
   </div>
   <div class="two-column-grid">
      <app-sequence-input [control]="receiptNumber" name="Начален номер за касов бон"/>
      <div>
         <h3>
            Използвани ДДС групи
            <app-info-icon tooltip="ДДС групите са документирани от Наредва № Н-18 и
фискалното устройство трябва да бъде конфигурирано с тях. Група В не се използва поради
специфичното ѝ предназначение за продажба на течни горива."/>
         </h3>
         <p>Група А: <b>0%</b></p>
         <p>Група Б: <b>20%</b></p>
         <p>Група Г: <b>9%</b></p>
      </div>
   </div>
}

<div class="settings-header">
   <h1>Правила за консумация</h1>

   <button (click)="addRule()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Ново правило
   </button>
</div>

<app-data-table [data]="rules$ | async" [formClass]="consumptionRuleForm"
                [headers]="ruleHeaders" [properties]="ruleProperties"
                [type]="DataType.ConsumptionRule" noPaginator/>

<div class="settings-header">
   <h1>Пакети</h1>

   <button (click)="addBundle()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нов пакет
   </button>
</div>

<app-data-table [data]="bundles$ | async" [filterPredicate]="bundleFilter"
                [formClass]="bundleForm" [headers]="bundleHeaders"
                [properties]="bundleProperties" [type]="DataType.Bundle" search/>

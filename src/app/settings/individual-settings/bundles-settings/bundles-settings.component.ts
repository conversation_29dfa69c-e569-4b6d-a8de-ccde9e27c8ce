import {Component, inject, OnInit, QueryList, ViewChildren} from '@angular/core';
import {BundleService} from '../../../services/bundle.service';
import {Bundle, bundleFilter, ConsumptionRule} from '../../../data/bundles/bundle';
import {MatDialog} from '@angular/material/dialog';
import {filter, map, switchMap} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {cmpName, ids, nameJoin} from '../../../utility/utility';
import {NotificationService} from '../../../services/notification.service';
import {ConsumptionRuleService} from '../../../services/consumption-rule.service';
import {
   ConsumptionRuleFormComponent
} from '../../../forms/consumption-rule-form/consumption-rule-form.component';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {DataTableComponent} from '../../data-table/data-table.component';
import {BundleFormComponent} from '../../../forms/bundle-form/bundle-form.component';
import {
   DynamicFormDialogComponent
} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';

@Component({
   selector: 'app-bundles-settings',
   templateUrl: './bundles-settings.component.html',
   standalone: false
})
export class BundlesSettingsComponent implements OnInit {
   @ViewChildren(DataTableComponent) dataTables!: QueryList<DataTableComponent>;

   bundles$?: Observable<Bundle[]>;
   bundleHeaders = [
      'Име',
      'Фискално име',
      'ДДС',
      'Ценоразписи',
   ];
   bundleProperties = [
      'name',
      'fiscalName',
      (b: Bundle) => `${b.vatGroup.percentage} %`,
      (b: Bundle) => b.activePricings.map(p => p.name),
   ];

   rules$?: Observable<ConsumptionRule[]>;
   ruleHeaders = [
      'Име',
      'Клиентски групи',
      'За всеки клиент',
      'За редовно легло',
      'За допълнително легло'
   ];
   ruleProperties = [
      'name',
      (cr: ConsumptionRule) => cr.applicableCustomerGroups.map(cg => cg.pluralName),
      (cr: ConsumptionRule) => cr.appliesPerCustomer ? 'Да' : 'Не',
      (cr: ConsumptionRule) => cr.appliesForRegularBed ? 'Да' : 'Не',
      (cr: ConsumptionRule) => cr.appliesForAdditionalBed ? 'Да' : 'Не',
   ];

   consumptionRuleForm = ConsumptionRuleFormComponent;
   bundleForm = BundleFormComponent;
   bundleFilter = bundleFilter;

   sDataTable = inject(DataTableService);
   DataType = DataType;

   private sBundle = inject(BundleService);
   private sConsumptionRule = inject(ConsumptionRuleService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => {
            if (type === DataType.ConsumptionRule) {
               this.sConsumptionRule.update(data)
                  .subscribe(() => this.renewRules());
            } else if (type === DataType.Bundle) {
               this.sBundle.update(data).subscribe(() => this.renewBundles());
            }
         },
         ({type, data}) => {
            if (type === DataType.ConsumptionRule) {
               this.deleteRules(data);
            } else if (type === DataType.Bundle) {
               this.deleteBundles(data);
            }
         }
      );
   }

   ngOnInit(): void {
      this.renewData();
   }

   renewData(): void {
      this.renewBundles();
      this.renewRules();
   }

   addRule(): void {
      this.openRuleDialog()
         .pipe(switchMap(result => this.sConsumptionRule.add(result)))
         .subscribe(() => this.renewRules());
   }

   deleteRules(rules: ConsumptionRule[]): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на правило',
         description: `Сигурни ли сте, че искате да изтриете ${nameJoin(rules)}?`,
         yesText: `Да, изтрий правилата!`,
      }).pipe(
         switchMap(() => this.sConsumptionRule.batchDelete(ids(rules)))
      ).subscribe({
         next: () => {
            this.dataTables.first.clearSelection();
            this.renewRules();
         },
         error: () => this.dataTables.first.clearSelection()
      });
   }

   addBundle(): void {
      this.openBundleDialog()
         .pipe(switchMap(result => this.sBundle.add(result)))
         .subscribe(() => this.renewBundles());
   }

   deleteBundles(bundles: Bundle[]): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на пакет',
         description: `Сигурни ли сте, че искате да изтриете ${nameJoin(bundles)}?`,
         yesText: `Да, изтрий пакетите!`,
      }).pipe(
         switchMap(() => this.sBundle.batchDelete(ids(bundles)))
      ).subscribe({
         next: () => {
            this.renewBundles();
            this.dataTables.last.clearSelection();
         },
         error: () => this.dataTables.last.clearSelection()
      });
   }

   private renewRules(): void {
      this.rules$ =
         this.sConsumptionRule.getAll().pipe(map(crs => crs.sort(cmpName)));
   }

   private renewBundles(): void {
      this.bundles$ = this.sBundle.getAll().pipe(map(bs => bs.sort(cmpName)));
   }

   private openBundleDialog(): Observable<any> {
      return this.dialog.open(DynamicFormDialogComponent, {
         data: {form: BundleFormComponent, title: 'Създаване на пакет'},
         width: '80vw',
         maxWidth: '80vw',
      }).afterClosed().pipe(filter(result => !!result));
   }

   private openRuleDialog(): Observable<any> {
      return this.dialog.open(DynamicFormDialogComponent, {
         data: {form: ConsumptionRuleFormComponent, title: 'Създаване на правило'}
      }).afterClosed().pipe(filter(result => !!result));
   }
}

import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {filter, map, switchMap} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import {Operator, operatorFilter, PRIVILEGES, ROLES} from 'src/app/data/auth/operator';
import {OperatorService} from 'src/app/services/operator.service';
import {NotificationService} from '../../../services/notification.service';
import {cmpName, ids, nameJoin} from '../../../utility/utility';
import {Observable, of} from 'rxjs';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {
   OperatorFormComponent
} from '../../../forms/operator-form/operator-form.component';
import {DataTableComponent} from '../../data-table/data-table.component';
import {
   DynamicFormDialogComponent
} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';
import {AuthService} from 'src/app/auth/auth.service';

@Component({
   selector: 'app-operators-settings',
   templateUrl: './operators-settings.component.html',
   standalone: false
})
export class OperatorsSettingsComponent implements OnInit {
   @ViewChild(DataTableComponent) dataTable!: DataTableComponent;

   operators$?: Observable<Operator[]>;
   headers = ['Код', 'Име', 'Потребителско име', 'Роля', 'Права'];
   properties = [
      'code',
      'name',
      (o: Operator) => o.credentials.username,
      (o: Operator) => ROLES[o.role].name,
      (o: Operator) => o.privileges?.map(p => PRIVILEGES[p].name)
   ];
   DataType = DataType;
   OperatorFormComponent = OperatorFormComponent;
   operatorFilter = operatorFilter;

   private sOperator = inject(OperatorService);
   private sNotification = inject(NotificationService);
   private sDataTable = inject(DataTableService);
   private sAuth = inject(AuthService);
   private dialog = inject(MatDialog);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => type === DataType.Operator && this.updateOperator(data),
         ({type, data}) => type === DataType.Operator && this.deleteOperators(data),
      );
   }

   ngOnInit(): void {
      this.renewData();
   }

   addOperator(): void {
      this.dialog.open(DynamicFormDialogComponent, {
         data: {form: OperatorFormComponent, title: 'Добавяне на служител'}
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(c => this.sOperator.add(c)),
      ).subscribe(() => this.renewData());
   }

   private updateOperator(operator: Operator): void {
      this.sOperator.update(operator).pipe(
         switchMap(() => {
            this.sOperator.invalidateCache();
            if (operator.id === this.sAuth.current()?.id) {
               return this.sAuth.init();
            }
            return of();
         })
      ).subscribe(() => {
         this.renewData();
      });
   }

   private deleteOperators(operators: Operator[]): void {
      const names = nameJoin(operators);
      this.sNotification.openConfirmationDialog({
         title: 'Деактивиране',
         description: `Наистина ли искате да деактивирате профилите на ${names}?`
      }).pipe(
         switchMap(() => this.sOperator.batchDelete(ids(operators)))
      ).subscribe({
         next: () => {
            this.renewData();
            this.dataTable.clearSelection();
         },
         error: () => this.dataTable.clearSelection()
      });
   }

   private renewData(): void {
      this.operators$ = this.sOperator.getAll().pipe(map(os => os.sort(cmpName)));
   }
}

<div class="settings-header">
   <h1>Служители</h1>

   <button (click)="addOperator()" appEvent="add_operator" color="primary"
           data-event-category="operators" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нов служител
   </button>
</div>

<app-data-table [data]="operators$ | async" [filterPredicate]="operatorFilter"
                [formClass]="OperatorFormComponent" [headers]="headers"
                [properties]="properties" [type]="DataType.Operator" search/>

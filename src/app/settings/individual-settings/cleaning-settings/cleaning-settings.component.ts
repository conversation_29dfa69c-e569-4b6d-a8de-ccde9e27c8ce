import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {CleaningService} from '../../../services/cleaning.service';
import {
   Cleaning,
   cmpCleaning,
   getCleaningPriorityName,
   roomTypeDaysToString
} from '../../../data/cleaning';
import {MatDialog} from '@angular/material/dialog';
import {filter, map, switchMap} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {
   CleaningFormComponent
} from '../../../forms/cleaning-form/cleaning-form.component';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {
   DynamicFormDialogComponent
} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';
import {ids, names} from '../../../utility/utility';
import {NotificationService} from '../../../services/notification.service';
import {DataTableComponent} from '../../data-table/data-table.component';
import {RoomService} from '../../../services/room.service';

type CleaningPropertiesInfo = Partial<Record<keyof Cleaning, string>>;

@Component({
   selector: 'app-cleaning-settings',
   templateUrl: './cleaning-settings.component.html',
   standalone: false
})
export class CleaningSettingsComponent implements OnInit {
   @ViewChild(DataTableComponent) dataTable!: DataTableComponent;

   cleanings$?: Observable<Cleaning[]>;
   headers = ['Име', 'Приоритет', 'Начисли при напускане', 'Периоди'];
   properties = [
      'name',
      (c: Cleaning) => getCleaningPriorityName(c.priority),
      (c: Cleaning) => c.applyOnCompleted ? 'Да' : 'Не',
      (c: Cleaning) => c.periodDays.map(rtd => roomTypeDaysToString(rtd))
   ];
   cleaningForm = CleaningFormComponent;

   info: CleaningPropertiesInfo = {
      priority: `
Когато няколко почиствания могат да бъдат приложени зa дадена стая, най-приоритетното от
тях бива приложено.`,
      applyOnCompleted: `
Отбелязва дали почистването да бъде начислено след напускането на която и да е резервация
в съответната стая.`,
      periodDays: `
Периодът, на който почистването ще се начислява на съответната стая. Почиствания се
начисляват само когато в стаята има направена резервация. Денят на настаняване на
резервацията се брои за ден първи. Почиствания не се начисляват в деня на настаняване.`,
   };

   DataType = DataType;

   private sCleaning = inject(CleaningService);
   private sRoom = inject(RoomService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);
   private sDataTable = inject(DataTableService);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => type === DataType.Cleaning &&
            this.sCleaning.update(data).subscribe(() => this.renewData()),
         ({type, data}) => type === DataType.Cleaning && this.deleteCleanings(data)
      );
   }

   ngOnInit(): void {
      this.renewData();
   }

   addCleaning(): void {
      this.dialog.open(DynamicFormDialogComponent, {
         data: {form: CleaningFormComponent, title: 'Добавяне на почистване'}
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(cleaning => this.sCleaning.add(cleaning))
      ).subscribe(() => this.renewData());
   }

   private renewData(): void {
      this.cleanings$ =
         this.sCleaning.getAll().pipe(map(cs => cs.sort(cmpCleaning)));
   }

   private deleteCleanings(cleanings: Cleaning[]): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на почистване',
         description: ['Сигурни ли сте, че искате да изтриете почистванията:',
            ...names(cleanings)],
         yesText: 'Да, изтрий почистванията!',
      }).pipe(
         switchMap(() => this.sCleaning.batchDelete(ids(cleanings)))
      ).subscribe({
         next: () => {
            this.sRoom.invalidateCache();
            this.renewData();
            this.dataTable.clearSelection();
         },
         error: () => this.dataTable.clearSelection()
      });
   }
}

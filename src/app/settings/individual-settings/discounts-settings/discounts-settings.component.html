<div class="settings-header">
   <h1>Типове гости</h1>
   <button (click)="addGroup()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нов тип гост
   </button>
</div>

<app-data-table [data]="groups$ | async" [formClass]="customerGroupForm"
                [headers]="groupHeaders" [properties]="groupProperties"
                [type]="DataType.CustomerGroup" noPaginator/>

<div class="settings-header">
   <h1>Отстъпки</h1>
   <button (click)="addDiscount()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нова отстъпка
   </button>
</div>

<app-data-table [data]="discounts$ | async" [formClass]="customerDiscountForm"
                [headers]="discountHeaders" [properties]="discountProperties"
                [type]="DataType.CustomerDiscount" noPaginator/>

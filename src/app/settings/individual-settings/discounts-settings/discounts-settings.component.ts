import {Component, inject, OnI<PERSON>t, Query<PERSON>ist, ViewChildren} from '@angular/core';
import {CustomerGroupService} from '../../../services/customer-group.service';
import {CustomerGroup} from '../../../data/customers/customer-group';
import {MatDialog} from '@angular/material/dialog';
import {
   cmpName,
   commaJoin,
   ids,
   moneyToString,
   nameJoin,
   sortCustomerGroups
} from '../../../utility/utility';
import {filter, map, switchMap} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {CustomerDiscountService} from '../../../services/customer-discount.service';
import {CustomerDiscount} from '../../../data/customers/customer-discount';
import {NotificationService} from '../../../services/notification.service';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {
   CustomerGroupFormComponent
} from '../../../forms/customer-group-form/customer-group-form.component';
import {DataTableComponent} from '../../data-table/data-table.component';
import {
   CustomerDiscountFormComponent
} from '../../../forms/customer-discount-form/customer-discount-form.component';
import {DiscountType} from '../../../utility/discount';
import {
   DynamicFormDialogComponent
} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';

@Component({
   selector: 'app-discounts-settings',
   templateUrl: './discounts-settings.component.html',
   standalone: false
})
export class DiscountsSettingsComponent implements OnInit {
   @ViewChildren(DataTableComponent) dataTables!: QueryList<DataTableComponent>;

   groups$: Observable<CustomerGroup[]> | undefined;
   groupHeaders = [
      'Име (ед. ч.)',
      'Име (мн. ч.)',
      'Минимални години',
      'Максимални години',
      'Брой за едно легло',
      'Изисквай данни',
   ];
   groupProperties = [
      'singularName',
      'pluralName',
      'minAge',
      'maxAge',
      'countMultiplier',
      (cg: CustomerGroup) => cg.isContactRequired ? 'Да' : 'Не',
   ];

   discounts$: Observable<CustomerDiscount[]> | undefined;
   discountHeaders = [
      'Име',
      'Отстъпка',
      'Намалени услуги',
      'Клиентски групи',
   ];
   discountProperties = [
      'name',
      (cd: CustomerDiscount) => cd.discount.type === DiscountType.percentage ?
         `${cd.discount.percentage} %` : moneyToString(cd.discount.amount),
      (cd: CustomerDiscount) =>
         !cd.applicableConsumables || cd.applicableConsumables.length === 0 ?
            'Всички' : cd.applicableConsumables.map(c => c.name),
      (cd: CustomerDiscount) =>
         !cd.applicableCustomerGroups || cd.applicableCustomerGroups.length === 0 ?
            'Всички' : cd.applicableCustomerGroups.map(cg => cg.pluralName)
   ];

   DataType = DataType;
   customerGroupForm = CustomerGroupFormComponent;
   customerDiscountForm = CustomerDiscountFormComponent;

   private sDataTable = inject(DataTableService);
   private sCustomerGroup = inject(CustomerGroupService);
   private sCustomerDiscount = inject(CustomerDiscountService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => {
            if (type === DataType.CustomerGroup) {
               this.sCustomerGroup.update(data).subscribe(() => this.renewGroups());
            } else if (type === DataType.CustomerDiscount) {
               this.sCustomerDiscount.update(data)
                  .subscribe(() => this.renewDiscounts());
            }
         },
         ({type, data}) => {
            if (type === DataType.CustomerGroup) {
               this.deleteGroups(data);
            } else if (type === DataType.CustomerDiscount) {
               this.deleteDiscounts(data);
            }
         });
   }

   ngOnInit(): void {
      this.renewGroups();
      this.renewDiscounts();
   }

   addGroup(): void {
      this.openGroupDialog()
         .pipe(switchMap(g => this.sCustomerGroup.add(g)))
         .subscribe(() => this.renewGroups());
   }

   deleteGroups(groups: CustomerGroup[]): void {
      const names = commaJoin(groups, 'pluralName');
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на клиентска група',
         description: `Сигурни ли сте, че искате да изтриете групите ${names}?`,
         yesText: `Да, изтрий групите!`,
      }).pipe(
         switchMap(() => this.sCustomerGroup.batchDelete(ids(groups)))
      ).subscribe({
         next: () => {
            this.renewGroups();
            this.dataTables.first.clearSelection();
         },
         error: () => this.dataTables.first.clearSelection()
      });
   }

   addDiscount(): void {
      this.openDiscountDialog()
         .pipe(switchMap(d => this.sCustomerDiscount.add(d)))
         .subscribe(() => this.renewDiscounts());
   }

   deleteDiscounts(discounts: CustomerDiscount[]): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на отстъпки',
         description: `Сигурни ли сте, че искате да изтриете ${nameJoin(discounts)}?`,
      }).pipe(
         switchMap(() => this.sCustomerDiscount.batchDelete(ids(discounts)))
      ).subscribe({
         next: () => {
            this.renewDiscounts();
            this.dataTables.last.clearSelection();
         },
         error: () => this.dataTables.last.clearSelection()
      });
   }

   private openGroupDialog(): Observable<CustomerGroup> {
      return this.dialog.open(DynamicFormDialogComponent, {
         data: {form: CustomerGroupFormComponent, title: 'Създаване на клиентска група'}
      }).afterClosed().pipe(filter(result => !!result));
   }

   private openDiscountDialog(): Observable<CustomerDiscount> {
      return this.dialog.open(DynamicFormDialogComponent, {
         data: {form: CustomerDiscountFormComponent, title: 'Създаване на отстъпка'},
      }).afterClosed().pipe(filter(result => !!result));
   }

   private renewGroups(): void {
      this.groups$ = this.sCustomerGroup.getAll().pipe(map(sortCustomerGroups));
   }

   private renewDiscounts(): void {
      this.discounts$ =
         this.sCustomerDiscount.getAll().pipe(map(cds => cds.sort(cmpName)));
   }
}

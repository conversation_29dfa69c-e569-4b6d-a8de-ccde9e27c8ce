import {Component, inject, OnInit} from '@angular/core';
import {Message} from '../../../data/message';
import {MessageService} from '../../../services/message.service';
import {UntypedFormBuilder, UntypedFormControl, Validators} from '@angular/forms';
import {forkJoin, Observable} from 'rxjs';
import {notNull, strcmp} from '../../../utility/utility';

@Component({
   selector: 'app-messages-settings',
   templateUrl: './messages-settings.component.html',
   standalone: false
})
export class MessagesSettingsComponent implements OnInit {
   messages: Message[] = [];
   controls: UntypedFormControl[] = [];

   private sMessage = inject(MessageService);
   private fb = inject(UntypedFormBuilder);

   get allPristine(): boolean {
      return this.controls.every(c => c.pristine);
   }

   ngOnInit(): void {
      this.renewData();
   }

   saveDirty(): void {
      forkJoin(this.controls.map((c, i) => {
         if (c.dirty) {
            return this.sMessage.update({...this.messages[i], body: c.value});
         }
      }).filter(notNull) as Observable<void>[]).subscribe(() => this.renewData());
   }

   private renewData(): void {
      this.sMessage.getAll().subscribe(ms => {
         const typeCmp = strcmp('type');
         this.messages =
            // @ts-expect-error boolean subtraction
            ms.sort((a, b) => typeCmp(a, b) || b.isNewCustomer - a.isNewCustomer);
         this.controls =
            this.messages.map(m => this.fb.control(m.body, Validators.required));
      });
   }
}

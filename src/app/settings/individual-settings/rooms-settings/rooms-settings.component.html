<div class="settings-header">
   <h1>Стаи</h1>

   <button (click)="addRoom()" color="primary" mat-raised-button>
      <mat-icon>add</mat-icon>
      Нова стая
   </button>
</div>

@if (rooms.length) {
   <app-data-table [data]="rooms" [formClass]="roomFormComponent" [headers]="headers"
                   [properties]="properties" [type]="DataType.Room"
                   [filterPredicate]="roomFilter" search>
      <ng-container selectionActions>
         <button (click)="blockSelected()" mat-raised-button color="accent">
            <mat-icon>no_meeting_room</mat-icon>
            Блокирай
         </button>
         <button (click)="unblockSelected()" mat-raised-button color="success">
            <mat-icon>meeting_room</mat-icon>
            Отблокирай
         </button>
      </ng-container>
   </app-data-table>
}

<div class="settings-header">
   <h1>Типове стаи</h1>

   <button (click)="saveRoomTypes()" [disabled]="!dirtyRoomTypesValid" color="primary"
           mat-raised-button>
      <mat-icon>save</mat-icon>
      Запази промените
   </button>
</div>
<div>
   @for (roomType of roomTypes; track roomType) {
      <div class="flex-row" [formGroup]="roomType">
         <mat-form-field>
            <input autocomplete="off" formControlName="name" matInput type="text">
         </mat-form-field>
         <button (click)="deleteRoomType(roomType.value)" mat-icon-button>
            <mat-icon>delete</mat-icon>
         </button>
      </div>
   }
   <div class="flex-row">
      <mat-form-field>
         <input [formControl]="newRoomTypeInput" autocomplete="off" matInput
                placeholder="Нов тип стая" type="text">
      </mat-form-field>
      <button (click)="addRoomType()" [disabled]="!newRoomTypeInput.value" color="primary"
              mat-icon-button>
         <mat-icon>add</mat-icon>
      </button>
   </div>
</div>

import {Component, inject} from '@angular/core';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatInputModule} from '@angular/material/input';
import {ReactiveFormsModule, UntypedFormBuilder, Validators} from '@angular/forms';

@Component({
   template: `
      <form [formGroup]="form" class="flex-column">
         <mat-form-field>
            <mat-label>Период</mat-label>
            <mat-date-range-input [rangePicker]="picker">
               <input formControlName="start" matStartDate placeholder="Начало">
               <input formControlName="end" matEndDate placeholder="Край">
            </mat-date-range-input>
            <mat-datepicker-toggle [for]="picker" matSuffix/>
            <mat-date-range-picker #picker/>
         </mat-form-field>
         <mat-form-field>
            <mat-label>Причина</mat-label>
            <input autocomplete="off" formControlName="reason" matInput type="text">
         </mat-form-field>
      </form>
   `,
   imports: [
      MatFormFieldModule,
      MatDatepickerModule,
      MatInputModule,
      ReactiveFormsModule
   ]
})
export class RoomBlockFormComponent {
   form = inject(UntypedFormBuilder).group({
      reason: ['', Validators.required],
      start: ['', Validators.required],
      end: ['', Validators.required],
   });

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): any {
      return this.form.value;
   }
}

import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {RoomService} from '../../../services/room.service';
import {Room, roomFilter} from '../../../data/room';
import {
   canonicalDate,
   cmpName,
   ids,
   nameJoin,
   rangesToString
} from '../../../utility/utility';
import {forkJoin, switchMap} from 'rxjs';
import {NotificationService} from '../../../services/notification.service';
import {Consumable, ConsumableType} from '../../../data/bundles/consumable';
import {ConsumableService} from '../../../services/consumable.service';
import {FormGroup, UntypedFormBuilder, Validators} from '@angular/forms';
import {RoomFormComponent} from '../../../forms/room-form/room-form.component';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {MatDialog} from '@angular/material/dialog';
import {filter} from 'rxjs/operators';
import {DataTableComponent} from '../../data-table/data-table.component';
import {
   DynamicFormDialogComponent
} from '../../../dialogs/dynamic-form-dialog/dynamic-form-dialog.component';
import {RoomBlockFormComponent} from './room-block-form.component';
import {DateTime} from 'luxon';

@Component({
   selector: 'app-rooms-settings',
   templateUrl: './rooms-settings.component.html',
   standalone: false
})
export class RoomsSettingsComponent implements OnInit {
   @ViewChild(DataTableComponent) dataTable!: DataTableComponent;

   rooms: Room[] = [];

   now = DateTime.local({locale: 'bg'});
   headers = ['Име', 'Тип', 'Капацитет', 'Допълнителни легла', 'Блокирана'];
   properties = [
      'name',
      (r: Room) => r.baseConsumable.name,
      'baseCapacity',
      'additionalCapacity',
      (r: Room) => r.blocked && r.blocked.end > this.now ?
         [rangesToString([r.blocked]), `Причина: ${r.blocked.reason}`] : '-'
   ];

   roomTypes: FormGroup[] = [];
   roomFormComponent = RoomFormComponent;

   protected DataType = DataType;
   protected roomFilter = roomFilter;
   private sRoom = inject(RoomService);
   private sNotification = inject(NotificationService);
   private sConsumable = inject(ConsumableService);
   private sDataTable = inject(DataTableService);
   private dialog = inject(MatDialog);
   private fb = inject(UntypedFormBuilder);
   newRoomTypeInput = this.fb.control('');

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => type === DataType.Room &&
            this.sRoom.update(data).subscribe(() => this.renewData()),
         ({type, data}) => type === DataType.Room && this.deleteRooms(data),
      );
   }

   get dirtyRoomTypesValid(): boolean {
      return this.roomTypes.some(fg => fg.dirty && fg.valid);
   }

   ngOnInit(): void {
      this.renewData();
   }

   addRoom(): void {
      this.dialog.open(DynamicFormDialogComponent, {
         data: {form: RoomFormComponent, title: 'Добавяне на стая'}
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(room => this.sRoom.add(room)),
      ).subscribe(() => this.renewData());
   }

   deleteRooms(rooms: Room[]): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на стая',
         description: `Сигурни ли сте, че искате да изтриете стаите: ${nameJoin(rooms)}?`,
         yesText: 'Да, изтрий стаите!',
      }).pipe(switchMap(() => this.sRoom.batchDelete(ids(rooms))))
         .subscribe({
            next: () => {
               this.dataTable.clearSelection();
               this.renewData();
            },
            error: () => this.dataTable.clearSelection(),
         });
   }

   addRoomType(): void {
      const roomType = {
         name: this.newRoomTypeInput.value,
         type: ConsumableType.room
      };

      this.sConsumable.add(roomType).subscribe(() => {
         this.renewData();
         this.newRoomTypeInput.setValue('');
      });
   }

   saveRoomTypes(): void {
      if (this.dirtyRoomTypesValid) {
         forkJoin(this.roomTypes.filter(fg => fg.dirty)
            .map(fg => this.sConsumable.update(fg.value))
         ).subscribe({
            next: () => {
               this.sRoom.invalidateCache();
               this.renewData();
            },
            error: (err) => {
               console.error(err);
               this.sNotification.displayNotification(
                  'Промяната не може да бъде записана!', 'Ок');
            }
         });
      }
   }

   deleteRoomType({id, name}: Consumable): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на тип стая',
         description: `Сигурни ли сте, че искате да изтриете "${name}"`,
      })
         .pipe(switchMap(() => this.sConsumable.delete(id)))
         .subscribe(() => this.renewData());
   }

   blockSelected(): void {
      this.dialog.open(DynamicFormDialogComponent, {
         data: {form: RoomBlockFormComponent, title: 'Блокиране на избраните стаи'}
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(({start, end, reason}) => this.sRoom.blockRooms({
            rooms: this.dataTable.selection.selected.map(r => r.id),
            start: canonicalDate(start).plus({seconds: 1}),
            end: canonicalDate(end),
            reason,
         })),
      ).subscribe({
         next: () => {
            this.dataTable.clearSelection();
            this.renewData();
         },
         error: () => this.dataTable.clearSelection()
      });
   }

   unblockSelected(): void {
      const rooms = this.dataTable.selection.selected;

      this.sNotification.openConfirmationDialog({
         title: 'Отблокиране на стаи',
         description: `Сигурни ли сте, че искате да отблокирате стаи ${nameJoin(rooms)}?`,
      })
         .pipe(switchMap(() => this.sRoom.unblockRooms(rooms.map(r => r.id))))
         .subscribe({
            next: () => {
               this.dataTable.clearSelection();
               this.renewData();
            },
            error: () => this.dataTable.clearSelection()
         });
   }

   private renewData(): void {
      this.sRoom.getAll().subscribe(rs => this.rooms = rs.sort(cmpName));

      this.sConsumable.getAllOfType(ConsumableType.room).subscribe(rts => {
         this.roomTypes = [];
         rts.sort(cmpName).forEach(rt => this.addRoomTypeInput(rt));
      });
   }

   private addRoomTypeInput(roomType: Consumable): void {
      this.roomTypes.push(this.fb.group({
         id: roomType.id,
         name: [roomType.name, Validators.required],
         type: ConsumableType.room
      }));
   }
}

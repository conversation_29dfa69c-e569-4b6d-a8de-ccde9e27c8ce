import {effect, Injectable, signal, Signal, WritableSignal} from '@angular/core';

export enum DataType {
   Room,
   ConsumptionRule,
   Bundle,
   CustomerGroup,
   CustomerDiscount,
   Operator,
   VoucherSource,
   Cleaning,
}

export interface SubmitData {
   data: any;
   type: DataType;
}

export interface DeleteData {
   data: any[];
   type: DataType;
}

export type SubmitSignal = WritableSignal<SubmitData | undefined>;
export type ReadonlySubmitSignal = Signal<SubmitData | undefined>;

export type DeleteSignal = WritableSignal<DeleteData | undefined>;
export type ReadonlyDeleteSignal = Signal<DeleteData | undefined>;

@Injectable({
   providedIn: 'root'
})
export class DataTableService {
   result: any;
   private _submit: SubmitSignal = signal(undefined);
   private _delete: DeleteSignal = signal(undefined);

   private get onSubmit(): ReadonlySubmitSignal {
      return this._submit;
   }

   private get onDelete(): ReadonlyDeleteSignal {
      return this._delete;
   }

   resetResult(): void {
      this.result = undefined;
      this._submit.set(undefined);
   }

   submit(data: any, type: DataType): void {
      this._submit.set({data, type});
   }

   delete(data: any[], type: DataType): void {
      this._delete.set({data, type});
   }

   registerHandlers(submitHandler: (data: SubmitData) => void,
                    deleteHandler: (data: DeleteData) => void): void {
      effect(() => {
         const value = this.onSubmit();
         if (value) {
            submitHandler(value);
            this._submit.set(undefined);
         }
      }, {allowSignalWrites: true});

      effect(() => {
         const toDelete = this.onDelete();
         if (toDelete) {
            deleteHandler(toDelete);
            this._delete.set(undefined);
         }
      }, {allowSignalWrites: true});
   }
}

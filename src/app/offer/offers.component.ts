import {AfterViewInit, Component, inject, ViewChild} from '@angular/core';
import {OfferService} from '../services/offer.service';
import {Offer} from '../data/offer';
import {filter, map, mergeMap, switchMap} from 'rxjs/operators';
import {Observable} from 'rxjs';
import {MatDialog} from '@angular/material/dialog';
import {
   ReservationDialogComponent,
   ReservationDialogData
} from '../dialogs/reservation-dialog/reservation-dialog.component';
import {ReservationService} from '../services/reservation.service';
import {ConsumableType} from '../data/bundles/consumable';
import {
   OfferDialogComponent,
   OfferDialogInput
} from '../dialogs/offer-dialog/offer-dialog.component';
import {NotificationService} from '../services/notification.service';
import {fullScreenDialogOptions} from '../utility/dialog-utility';
import {MatPaginator} from '@angular/material/paginator';
import {MatTableDataSource} from '@angular/material/table';
import {FormatCustomerPipe} from '../pipes/format-customer.pipe';
import {moneyToString, rangesToString} from '../utility/utility';
import {formatGGC} from '../utility/reservation-utility';
import {CustomerGroupService} from '../services/customer-group.service';
import {CustomerGroup} from '../data/customers/customer-group';
import {FormControl} from '@angular/forms';
import {NotesService} from '../services/notes.service';
import {fullName} from '../data/customers/customer';

const offerSort = (a: Offer, b: Offer) => a.start.valueOf() - b.start.valueOf();

@Component({
   selector: 'app-offers',
   templateUrl: './offers.component.html',
   standalone: false
})
export class OffersComponent implements AfterViewInit {
   @ViewChild(MatPaginator) paginator!: MatPaginator;

   search = new FormControl('');
   dataSource = new MatTableDataSource<Offer>();
   columns = ['name', 'price', 'bundle', 'dates', 'guests', 'notes', 'actions'];

   getName = (offer: Offer) => FormatCustomerPipe.toString(offer.titular);
   getPrice = (offer: Offer) => moneyToString(offer.price);
   getBundle = (offer: Offer) => offer.bundle.name;
   getDates = (offer: Offer) => rangesToString([offer]);
   getGuests = (offer: Offer) => formatGGC(offer.guestGroupCount, this.groups);
   getNotes = (offer: Offer) => offer.notes?.map(n => n.content).join(', ') ?? '';

   private groups: CustomerGroup[] = [];
   private sOffer = inject(OfferService);
   private sReservation = inject(ReservationService);
   private sNotification = inject(NotificationService);
   private sNotes = inject(NotesService);
   private dialog = inject(MatDialog);

   constructor() {
      inject(CustomerGroupService).getAll().subscribe(gs => this.groups = gs);

      this.dataSource.filterPredicate = (o, search) => {
         return fullName(o.titular).toLowerCase().includes(search.toLowerCase());
      };
      this.search.valueChanges.subscribe(v => this.dataSource.filter = v ?? '');
   }

   ngAfterViewInit(): void {
      this.dataSource.paginator = this.paginator;

      this.renewData();
   }

   confirmOffer(offer: Offer) {
      const {titular, start, end, bundle, price, guestGroupCount, source} = offer;
      const roomType = bundle.consumableRules.find(
         ca => ca.consumable.type === ConsumableType.room)?.consumable;

      const data: ReservationDialogData = {
         edit: false,
         data: {
            titular,
            start,
            end,
            bundle,
            manualPrice: price,
            guestGroupCount,
            source,
         },
         roomType,
         notesParent: offer.id
      };

      const dialog = this.dialog.open(ReservationDialogComponent, {
         data,
         ...fullScreenDialogOptions,
      });

      dialog.afterClosed().pipe(
         filter(value => !!value),
         switchMap(result => this.sReservation.add(result.addReservation)),
         switchMap(
            reservation => this.sOffer.confirmOffer(offer.id, reservation.id))
      ).subscribe(() => this.renewData());
   }

   createOffer(): void {
      this.openOfferDialog()
         .pipe(
            switchMap(result => this.sOffer.add(result.offer).pipe(
               map(offer => ({offer, draftId: result.draftId}))
            )),
            mergeMap(({offer, draftId}) =>
               this.sNotes.moveToNewParent(draftId, offer.id)
                  .pipe(map(() => draftId))),
            mergeMap(draftId => this.sReservation.deleteDraft(draftId)))
         .subscribe(() => this.renewData());
   }

   editOffer(offer: Offer): void {
      this.openOfferDialog(offer)
         .pipe(switchMap(result => this.sOffer.update(result.offer)))
         .subscribe(() => this.renewData());
   }

   deleteOffer(offer: Offer) {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на оферта',
         description: `Сигурни ли сте, че искате да изтриете офертата за ${fullName(
            offer.titular)}?`,
         yesText: 'Да, изтрий офертата!'
      }).pipe(switchMap(() => this.sOffer.delete(offer.id)))
         .subscribe(() => this.renewData());
   }

   private openOfferDialog(offer?: Offer): Observable<any> {
      const data: OfferDialogInput = {
         edit: !!offer,
         data: offer,
         notesCallback: offer ? this.renewData : undefined
      };

      const dialog = this.dialog.open(OfferDialogComponent, {
         data,
         ...fullScreenDialogOptions,
      });

      return dialog.afterClosed().pipe(filter(result => !!result));
   }

   private renewData(): void {
      this.sOffer.getAll()
         .subscribe(os => this.dataSource.data = os.sort(offerSort));
   }
}

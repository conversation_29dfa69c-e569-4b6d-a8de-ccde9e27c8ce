<div class="main-view-header">
   <div style="width: 144px"></div>
   <mat-form-field class="search">
      <mat-label>Търсене</mat-label>
      <input [formControl]="search" matInput type="text">
      <mat-icon matSuffix>search</mat-icon>
   </mat-form-field>
   <button (click)="createOffer()" color="primary" extended mat-fab>
      <mat-icon>price_check</mat-icon>
      Получи цена
   </button>
</div>
<div class="main-view-table mat-elevation-z5" style="width: 90vw;">
   <table [dataSource]="dataSource" mat-table>
      <mat-text-column [dataAccessor]="getName" headerText="Име" name="name"/>
      <mat-text-column [dataAccessor]="getPrice" headerText="Цена" name="price"/>
      <mat-text-column [dataAccessor]="getBundle" headerText="Пакет" name="bundle"/>
      <mat-text-column [dataAccessor]="getDates" headerText="Период" name="dates"/>
      <mat-text-column [dataAccessor]="getGuests" headerText="Бройки" name="guests"/>
      <mat-text-column [dataAccessor]="getNotes" headerText="Бележки" name="notes"/>

      <ng-container matColumnDef="actions">
         <th *matHeaderCellDef mat-header-cell></th>
         <td *matCellDef="let offer" mat-cell>
            <button (click)="editOffer(offer)" mat-icon-button>
               <mat-icon>edit</mat-icon>
            </button>
            <button [matMenuTriggerData]="{offer}" [matMenuTriggerFor]="menu"
                    mat-icon-button>
               <mat-icon>more_vert</mat-icon>
            </button>
         </td>
      </ng-container>

      <tr *matHeaderRowDef="columns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: columns;" mat-row></tr>

      <tr *matNoDataRow>
         <td [colSpan]="columns.length" class="text-center">Няма направени оферти</td>
      </tr>
   </table>

   <mat-paginator [pageSizeOptions]="[20, 50, 100]" showFirstLastButtons/>
</div>

<mat-menu #menu>
   <ng-template let-offer="offer" matMenuContent>
      <button (click)="confirmOffer(offer)" mat-menu-item>
         <mat-icon>event_available</mat-icon>
         Направи резервация
      </button>
      <button (click)="deleteOffer(offer)" mat-menu-item>
         <mat-icon color="warn">delete</mat-icon>
         Изтрий
      </button>
   </ng-template>
</mat-menu>

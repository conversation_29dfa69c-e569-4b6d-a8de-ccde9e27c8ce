import {Component, inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, Validators} from '@angular/forms';
import {ReceiptReversalReason} from '../../data/fiscal/fiscal-agent';
import {FiscalAgentService} from '../../services/fiscal-agent.service';

export interface PaymentReversalDialogInput {
   paymentAmount: number;
}

@Component({
   selector: 'app-payment-reversal-dialog',
   template: `
      <app-form-dialog (submitForm)="trySubmit()" heading="Причина за сторно"
                       submitText="Сторнирай плащането!">
         <div>
            <mat-form-field class="wide-form-field">
               <mat-label>Причина</mat-label>
               <mat-select [formControl]="reasonCtrl">
                  @for (r of reasons; track r) {
                     <mat-option [value]="r.reason" [disabled]="r.disabled">
                        @if (r.disabled) {
                           <mat-icon>money_off</mat-icon>
                        }
                        {{r.text}}
                     </mat-option>
                  }
               </mat-select>
            </mat-form-field>
         </div>
      </app-form-dialog>
   `,
   standalone: false
})
export class PaymentReversalDialogComponent {
   reasons = [ // keep order
      {
         reason: ReceiptReversalReason.taxBaseReduction,
         disabled: false,
         text: 'Промяна на цена (данъчна основа)'
      },
      {
         reason: ReceiptReversalReason.refund,
         disabled: false,
         text: 'Връщане или рекламация',
      },
      {
         reason: ReceiptReversalReason.operatorError,
         disabled: false,
         text: 'Операторска грешка'
      },
   ];
   reasonCtrl = inject(FormBuilder).nonNullable
      .control(ReceiptReversalReason.taxBaseReduction, Validators.required);

   remaining?: number;

   private input: PaymentReversalDialogInput = inject(MAT_DIALOG_DATA);
   private dialog = inject(MatDialogRef);

   constructor() {
      inject(FiscalAgentService).getCashAmount().subscribe({
         next: response => {
            const cash = response.amount;
            if (cash < this.input.paymentAmount) {
               const r = ReceiptReversalReason;

               this.remaining = this.input.paymentAmount - cash;
               this.reasons[0].disabled = true; // taxBaseReduction
               this.reasons[1].disabled = true; // refund
               this.reasonCtrl.setValue(r.operatorError);
            }
         },
         error: () => this.dialog.close(),
      });
   }

   trySubmit(): void {
      if (this.reasonCtrl.valid) {
         this.dialog.close(this.reasonCtrl.value);
      }
   }
}

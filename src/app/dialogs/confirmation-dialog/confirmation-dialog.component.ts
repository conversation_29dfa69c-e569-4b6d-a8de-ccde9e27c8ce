import {Component, inject, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';

export interface ConfirmationDialogData {
   title: string;
   description: string | string[];
   yesText?: string;
}

@Component({
   selector: 'app-confirmation-dialog',
   templateUrl: './confirmation-dialog.component.html',
   standalone: false
})
export class ConfirmationDialogComponent implements OnInit {
   confirmationDialogDefaults = {
      yesText: 'Да'
   };

   data: ConfirmationDialogData = inject(MAT_DIALOG_DATA);
   multiline = false;

   ngOnInit(): void {
      this.data = {...this.confirmationDialogDefaults, ...this.data};
      this.multiline = Array.isArray(this.data.description);
   }
}

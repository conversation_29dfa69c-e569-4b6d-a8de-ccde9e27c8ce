<app-dialog [heading]="title">
   <ng-container app-dialog-buttons>
      <button (click)="trySubmit()" [disabled]="!offerForm.valid || offerForm.pristine"
              color="primary" mat-raised-button>
         Запазване
      </button>
   </ng-container>
   <div class="two-column-grid">
      <div>
         <app-offer-form #offerForm (titularChange)="updateTitularInfo($event)"
                         [data]="input.data"/>
         <app-notes (addedNote)="handleNewNote($event)" [parent]="notesParent"/>
      </div>
      <div class="card-holder">
         <mat-card>
            <mat-card-header>
               <mat-card-title>
                  @if (availableRoomsCount !== undefined) {
                     @if (availableRoomsCount > 1) {
                        <b>{{availableRoomsCount}}</b> свободни стаи за периода
                     } @else if (availableRoomsCount === 1) {
                        1 свободна стая за периода
                     }
                  } @else {
                     Свободни стаи за периода
                  }
               </mat-card-title>
            </mat-card-header>
            <mat-card-content>
               @if (availableRooms$ | async; as rooms) {
                  @for (r of rooms | keyvalue; track r.key) {
                     <div>
                        <b>{{r.value.length}}</b> {{r.key}} ({{r.value | commaJoin}})
                     </div>
                  }
               }
            </mat-card-content>
         </mat-card>
         <mat-card>
            <mat-card-header>
               <mat-card-title>Цена</mat-card-title>
            </mat-card-header>
            <mat-card-content>
               @if (priceData$ | async; as priceData) {
                  <b>{{priceData.price | money}}</b> за
                  @if (priceData.duration === 1) {
                     <b>1</b> нощувка
                  } @else {
                     <b>{{priceData.duration}}</b> нощувки
                  }
               } @else {
                  Моля въведете бройка гости, дати и пакет, за да получите цена!
               }
            </mat-card-content>
         </mat-card>
         <mat-card>
            <mat-card-header>
               <mat-card-title>Информация за госта</mat-card-title>
            </mat-card-header>
            <mat-card-content>
               @if (titular) {
                  <mat-list>
                     <mat-list-item>
                        <mat-icon matListItemIcon>paid</mat-icon>
                        @if (titularPayments$ | async; as payments) {
                           <b>{{payments | money}}</b> в направени плащания
                        } @else {
                           Няма направени плащания
                        }
                     </mat-list-item>
                     <mat-list-item>
                        <mat-icon matListItemIcon>history</mat-icon>
                        @if (reservationCount) {
                           @if (reservationCount === 1) {
                              <b>1</b> направена резервация
                           } @else {
                              <b>{{reservationCount}}</b> направени резервации
                           }
                        } @else {
                           Няма минали резервации
                        }
                     </mat-list-item>
                     <mat-list-item>
                        <mat-icon matListItemIcon>event_note</mat-icon>
                        @if (titular.contact.notes; as notes) {
                           {{notes}}
                        } @else {
                           Няма запазени бележки
                        }
                     </mat-list-item>
                  </mat-list>
               } @else {
                  Моля изберете гост!
               }
            </mat-card-content>
         </mat-card>
      </div>
   </div>
</app-dialog>

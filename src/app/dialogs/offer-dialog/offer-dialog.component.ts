import {AfterViewInit, Component, inject, OnInit, ViewChild} from '@angular/core';
import {OfferFormComponent} from '../../forms/offer-form/offer-form.component';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Customer} from '../../data/customers/customer';
import {ReservationService} from '../../services/reservation.service';
import {DEBOUNCE_TIME, getDuration} from '../../utility/utility';
import {Money} from '../../data/common';
import {Offer} from '../../data/offer';
import {DialogInput} from '../dialog-input';
import {forkJoin, Observable, of, tap} from 'rxjs';
import {
   debounceTime,
   distinctUntilChanged,
   filter,
   map,
   startWith,
   switchMap
} from 'rxjs/operators';
import {AnalyticsService} from '../../services/analytics.service';
import {OfferService} from '../../services/offer.service';
import {CustomerService} from '../../services/customer.service';
import {ID} from '../../data/identifiable';
import {Note} from '../../data/notes';

export type OfferDialogInput = DialogInput<Offer> & {
   notesCallback?: (note: Note) => void;
}

@Component({
   selector: 'app-offer-dialog',
   templateUrl: './offer-dialog.component.html',
   styles: [`
      .card-holder mat-card {
         margin: 16px 0;
      }
   `],
   standalone: false
})
export class OfferDialogComponent implements OnInit, AfterViewInit {
   @ViewChild(OfferFormComponent) form!: OfferFormComponent;

   titular: Customer | null = null;
   reservationCount = 0;
   priceData$?: Observable<{ price: Money; duration: number }>;
   titularPayments$?: Observable<Money | null>;
   availableRooms$?: Observable<Record<string, string[]>>;
   availableRoomsCount?: number;

   input: OfferDialogInput = inject(MAT_DIALOG_DATA);

   private sReservation = inject(ReservationService);
   private sOffer = inject(OfferService);
   private sAnalytics = inject(AnalyticsService);
   private sCustomer = inject(CustomerService);
   private dialogRef = inject(MatDialogRef);

   private draftId?: ID;

   get title(): string {
      return this.input.edit ? 'Редактиране на оферта' : 'Нова оферта';
   }

   get notesParent() {
      return this.input.edit ? this.input.data!.id : this.draftId;
   }

   ngOnInit(): void {
      if (!this.input.edit) {
         this.dialogRef.afterOpened().pipe(switchMap(() => {
            return this.sReservation.createDraft();
         })).subscribe(draftId => this.draftId = draftId);
         this.dialogRef.beforeClosed().pipe(switchMap(submittedData => {
            if (!submittedData) {
               return this.sReservation.deleteDraft(this.draftId!);
            }
            return of();
         })).subscribe();
      }
   }

   ngAfterViewInit(): void {
      this.priceData$ = this.form.valueChanges.pipe(
         startWith(this.input.data),
         debounceTime(DEBOUNCE_TIME * 2),
         filter(v => v && v.bundle && v.start && v.end),
         distinctUntilChanged((p: Offer, c: Offer) => {
            const {bundle: b1, start: s1, end: e1} = p;
            const {bundle: b2, start: s2, end: e2} = c;

            return b1.id === b2.id && s1.equals(s2) && e1.equals(e2);
         }),
         tap(() => this.sAnalytics.event('leads', 'price')),
         switchMap(o => forkJoin([of(o), this.sReservation.getPrice(o)])),
         map(([offer, price]) => ({price, duration: getDuration(offer.start, offer.end)}))
      );

      this.availableRooms$ = this.form.valueChanges.pipe(
         startWith(this.input.data),
         debounceTime(DEBOUNCE_TIME * 2),
         filter(v => v && v.start && v.end),
         distinctUntilChanged((p, c) => p.start.equals(c.start) && p.end.equals(c.end)),
         tap(() => this.sAnalytics.event('leads', 'available_rooms')),
         switchMap(offer => this.sOffer.getAvailableRooms(offer)),
         map(rooms => {
            const result: Record<string, string[]> = {};
            for (const room of rooms) {
               const roomType = room.baseConsumable.name;
               if (roomType in result) {
                  result[roomType].push(room.name);
               } else {
                  result[roomType] = [room.name];
               }
            }

            this.availableRoomsCount = rooms.length;
            Object.values(result).forEach(rs => rs.sort());
            return result;
         }),
      );
   }

   trySubmit(): void {
      if (this.form.valid) {
         this.dialogRef.close({offer: this.form.value, draftId: this.draftId});
      }
   }

   updateTitularInfo(titular: Customer) {
      this.titular = titular;

      this.sReservation.getCustomerReservations(titular.id)
         .subscribe(count => this.reservationCount = count);

      this.titularPayments$ = this.sCustomer.getTotalPayments(titular.id);

      this.sAnalytics.event('leads', 'titular_info');
   }

   handleNewNote(note: Note) {
      if (this.input.notesCallback) {
         this.input.notesCallback(note);
      }
   }
}

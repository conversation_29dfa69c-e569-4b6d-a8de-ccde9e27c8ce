import {Component, inject} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {Customer} from '../data/customers/customer';
import {IdScannerUseCase} from '../components/id-scanner-icon/id-scanner-icon.component';

export interface IdScannerResultDialogData {
   customer: Customer;
   useCase?: IdScannerUseCase;
}

export type IdScannerResultAction = boolean | 'save-guest' | 'save-guest-titular';

@Component({
   selector: 'app-id-scanner-result-dialog',
   template: `
      <app-dialog [actions]="input.useCase !== uc.reservation"
                  [customActions]="input.useCase === uc.reservation"
                  confirmText="Запази данните"
                  heading="Данни от четеца на лични документи">
         @let c = input.customer;
         <div>{{c.contact.name}} {{c.contact.lastName}}</div>
         @if (c.idNumber) {
            <div>ЕГН: {{c.idNumber}}</div>
         }
         @if (c.document) {
            <div>{{c.document.type | customerDocument}} с №{{c.document.id}}
               издадена на {{c.document.issuedAt}}
            </div>
         }
         <div>Държава: {{c.country}}</div>
         @if (c.individual) {
            <div>Дата на раждане: {{c.individual.birthDate}}</div>
            <div>Пол: {{c.individual.gender | customerGender}}</div>
         }
         @if (input.useCase === uc.reservation) {
            <mat-dialog-actions align="end" app-dialog-actions>
               <button [mat-dialog-close]="'save-guest-titular'" color="accent"
                       mat-raised-button>
                  Запази като гост и титуляр
               </button>
               <button [mat-dialog-close]="'save-guest'" color="primary"
                       mat-raised-button>
                  Запази като гост
               </button>
            </mat-dialog-actions>
         }
      </app-dialog>
   `,
   styles: ``,
   standalone: false
})
export class IdScannerResultDialogComponent {
   input: IdScannerResultDialogData = inject(MAT_DIALOG_DATA);
   uc = IdScannerUseCase;
}

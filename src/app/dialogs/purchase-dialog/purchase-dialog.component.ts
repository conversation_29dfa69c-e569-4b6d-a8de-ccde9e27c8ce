import {Component, inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Purchase} from '../../data/purchase';
import {PurchaseFormComponent} from '../../forms/purchase-form/purchase-form.component';
import {ID} from 'src/app/data/identifiable';
import {PurchaseService} from '../../services/purchase.service';
import {dialogCloseObserver} from '../../utility/dialog-utility';
import {NotificationService} from '../../services/notification.service';

export interface PurchaseDialogInput {
   edit: boolean;
   account: ID;
   data?: Purchase;
}

@Component({
   selector: 'app-purchase-dialog',
   template: `
      <app-form-dialog (submitForm)="trySubmit()" [heading]="title" [loading]="loading">
         <app-purchase-form [account]="input.account" [data]="input.data"/>
      </app-form-dialog>
   `,
   standalone: false
})
export class PurchaseDialogComponent {
   @ViewChild(PurchaseFormComponent) form!: PurchaseFormComponent;

   title: string;
   loading = false;

   input: PurchaseDialogInput = inject(MAT_DIALOG_DATA);

   private sPurchase = inject(PurchaseService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialogRef);

   constructor() {
      this.title = `${this.input.edit ? 'Редактиране' : 'Добавяне'} на начисление`;
   }

   trySubmit() {
      if (this.form.valid) {
         const purchase = this.form.value;
         const observer = dialogCloseObserver(this.dialog, this.sNotification);

         this.loading = true;
         if (this.input.edit) {
            this.sPurchase.update(purchase).subscribe(observer);
         } else {
            this.sPurchase.add(purchase).subscribe(observer);
         }
      }
   }
}

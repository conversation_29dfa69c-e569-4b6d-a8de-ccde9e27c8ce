<app-dialog [actions]="true" [value]="value" heading="Начисляване на почистване">
   <div class="form">
      <div class="flex-row form">
         <mat-form-field>
            <mat-icon matPrefix>cleaning_services</mat-icon>
            <mat-label>Почистване</mat-label>
            <mat-select [formControl]="cleaning">
               @for (cleaning of cleanings$ | async; track cleaning.id) {
                  <mat-option [value]="cleaning">
                     {{cleaning.name}}
                  </mat-option>
               }
            </mat-select>
         </mat-form-field>
         <mat-form-field>
            <mat-icon matPrefix>meeting_room</mat-icon>
            <mat-label>Стаи</mat-label>
            <mat-select [formControl]="rooms" multiple>
               <mat-select-trigger>
                  {{roomNames$ | async}}
               </mat-select-trigger>
               @for (room of rooms$ | async; track room.id) {
                  <mat-option [value]="room">
                     {{room.name}} ({{room.baseConsumable.name}})
                  </mat-option>
               }
            </mat-select>
         </mat-form-field>
      </div>
   </div>

</app-dialog>

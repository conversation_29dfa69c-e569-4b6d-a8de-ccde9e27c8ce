import {Component, inject} from '@angular/core';
import {RoomService} from '../../services/room.service';
import {Room} from '../../data/room';
import {Observable} from 'rxjs';
import {FormControl} from '@angular/forms';
import {cmpName, nameJoin} from '../../utility/utility';
import {map} from 'rxjs/operators';
import {CleaningService} from '../../services/cleaning.service';
import {Cleaning} from '../../data/cleaning';

@Component({
   selector: 'app-assign-cleaning-dialog',
   templateUrl: './assign-cleaning-dialog.component.html',
   standalone: false
})
export class AssignCleaningDialogComponent {
   rooms$: Observable<Room[]>;
   cleanings$: Observable<Cleaning[]>;

   rooms = new FormControl<Room[]>([]);
   cleaning = new FormControl<Cleaning | null>(null);

   roomNames$: Observable<string> = this.rooms.valueChanges.pipe(
      map(rs => rs ? nameJoin(rs) : ''),
   );
   value = () => ({rooms: this.rooms.value, cleaning: this.cleaning.value});

   private sRoom = inject(RoomService);
   private sCleaning = inject(CleaningService);

   constructor() {
      this.rooms$ = this.sRoom.getAll().pipe(
         map(rooms => rooms.filter(r => !r.cleaning).sort(cmpName))
      );

      this.cleanings$ = this.sCleaning.getAll().pipe(
         map(cleanings => cleanings.sort(cmpName))
      );
   }
}

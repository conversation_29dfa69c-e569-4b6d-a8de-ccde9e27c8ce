<app-form-dialog (submitForm)="trySubmit()" heading="Настаняване"
                 submitText="Настаняване!">
   <ng-container app-dialog-heading>
      <app-id-scanner-icon (scannerResult)="handleIdScanned($event)"
                           tooltip="Сканирайте документ, за да запазите данните
                                    в профила на избрания гост."/>
   </ng-container>
   <mat-stepper disableRipple orientation="vertical">
      @for (guest of reservation.guests; track guest.id) {
         <mat-step [stepControl]="customerForm.form">
            <ng-template matStepLabel>
               Данни за {{guest | formatCustomer:false}}
            </ng-template>
            <app-customer-form #customerForm [data]="guest" allRequired="true"
                               edit="true"/>
         </mat-step>
      }
      <mat-step>
         <ng-template matStepLabel>Допълнителни гости в стаята</ng-template>
         <app-customer-list-input [allCustomerFieldsRequired]="true"
                                  [customerArray]="additionalGuests" wide/>
      </mat-step>
      <mat-step>
         <ng-template matStepLabel>
            <p>
               @if (payments.length === 1) {
                  Едно плащане на стойност {{paid | money}}
               } @else if (payments.length > 1) {
                  {{payments.length}} плащания на обща стойност {{paid | money}}
               } @else {
                  Не са регистрирани плащания
               }
            </p>

            @if (remaining?.amount > 0) {
               <button (click)="payRemaining()" [disabled]="!sPayment.enabledNoBank()"
                       color="primary" mat-raised-button>
                  <mat-icon>attach_money</mat-icon>
                  Плати {{remaining | money}}
               </button>
            }
         </ng-template>
         @if (payments.length > 0) {
            <b>Регистрирани плащания</b>
         }
         @for (payment of payments; track payment.id) {
            @if (payment.isDownPayment) {
               <p>
                  {{payment.price | money}} (Капаро {{payment.method | paymentMethod}})
               </p>
            } @else {
               <p>{{payment.price | money}} ({{payment.method | paymentMethod}})</p>
            }
         }
      </mat-step>
   </mat-stepper>
</app-form-dialog>

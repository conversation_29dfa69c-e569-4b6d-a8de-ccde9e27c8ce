import {Component, inject, QueryList, <PERSON>Child, ViewChildren} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {Reservation} from '../../data/reservation';
import {UntypedFormArray, UntypedFormControl} from '@angular/forms';
import {MatStepper} from '@angular/material/stepper';
import {forkJoin} from 'rxjs';
import {defaultIfEmpty, filter, map, tap} from 'rxjs/operators';
import {MatSnackBar} from '@angular/material/snack-bar';
import {CustomerFormComponent} from '../../forms/customer-form/customer-form.component';
import {CustomerService} from '../../services/customer.service';
import {Money} from '../../data/common';
import {addMoney, subMoney} from '../../utility/money-utility';
import {PaymentService} from '../../services/payment.service';
import {PurchaseService} from '../../services/purchase.service';
import {
   PaymentDialogComponent,
   PaymentDialogInput
} from '../payment-dialog/payment-dialog.component';
import {commaJoin, normalizeMoney, nullMoney} from '../../utility/utility';
import {Customer} from '../../data/customers/customer';
import {Payment} from '../../data/payment';
import {
   IdScannerResult
} from '../../components/id-scanner-icon/id-scanner-icon.component';

@Component({
   selector: 'app-check-in-dialog',
   templateUrl: './check-in-dialog.component.html',
   standalone: false
})
export class CheckInDialogComponent {
   @ViewChild(MatStepper) stepper!: MatStepper;
   @ViewChildren(CustomerFormComponent) customerForms!: QueryList<CustomerFormComponent>;

   additionalGuests = new UntypedFormArray([]);
   payments: Payment[] = [];
   paid: Money | undefined;
   remaining: Money | undefined;

   reservation: Reservation = inject(MAT_DIALOG_DATA);

   sPayment = inject(PaymentService);
   private sPurchase = inject(PurchaseService);
   private sCustomer = inject(CustomerService);
   private dialogRef = inject(MatDialogRef);
   private dialog = inject(MatDialog);
   private snackbar = inject(MatSnackBar);

   constructor() {
      this.renewRemaining();
   }

   trySubmit(): void {
      const invalid = this.customerForms.filter(cf => !cf.valid);
      if (invalid.length !== 0) {
         const names = commaJoin(invalid.map(cf => cf.value.contact.name));
         this.displayError(`Трябва да попълните данните за ${names}!`);
         return;
      }

      const duplicate = this.customerForms.filter(cf => cf.duplicates().length > 0);
      if (duplicate.length !== 0) {
         const names = commaJoin(duplicate.map(cf => cf.value.contact.name));
         this.displayError(`Трябва да изберете съществуващ профил за ${names}!`);
         return;
      }

      // Update all the customers with touched data in the database
      const touchedCustomers = this.customerForms.filter(cf => cf.form.dirty);
      forkJoin(touchedCustomers.map(cf => this.sCustomer.update(cf.value)))
         .pipe(defaultIfEmpty(null))
         .subscribe(() => this.dialogRef.close(this.getGuests()));
   }

   payRemaining(): void {
      if (!this.remaining) {
         return;
      }

      const data: PaymentDialogInput = {
         data: {price: this.remaining},
         edit: false,
         account: this.reservation.activeAccount.id
      };

      const dialog = this.dialog.open(PaymentDialogComponent, {data});

      dialog.afterClosed()
         .pipe(filter(result => !!result))
         .subscribe(() => this.renewRemaining());
   }

   handleIdScanned({action, customer}: IdScannerResult): void {
      if (action) {
         const index = this.stepper.selectedIndex;
         if (index < this.customerForms.length) {
            this.customerForms.get(index)!.customer = customer;
         } else if (index === this.customerForms.length) { // additional guest
            this.additionalGuests.push(new UntypedFormControl(customer));
         }
      }
   }

   private displayError(message: string): void {
      this.snackbar.open(message, 'Добре', {horizontalPosition: 'center'});
   }

   private getGuests(): Customer[] {
      return [
         ...this.customerForms.map(form => form.value),
         ...this.additionalGuests.value
      ];
   }

   private renewRemaining(): void {
      forkJoin({
         purchases: this.sPurchase.getByAccount(this.reservation.activeAccount.id),
         payments: this.sPayment.getByAccount(this.reservation.activeAccount.id),
      }).pipe(
         tap(({payments}) => this.payments = payments),
         map(({purchases, payments}) => {
            this.paid = payments.map(p => p.price).reduce(addMoney, nullMoney());
            return subMoney(
               purchases.map(p => p.price).reduce(addMoney, nullMoney()),
               this.paid
            );
         }),
      ).subscribe(remaining => this.remaining = normalizeMoney(remaining));
   }
}

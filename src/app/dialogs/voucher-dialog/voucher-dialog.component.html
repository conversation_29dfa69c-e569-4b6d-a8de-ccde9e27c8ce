<app-form-dialog (submitForm)="trySubmit()" heading="Добавяне на ваучери">
   <form [formGroup]="form" class="column-form">
      <div class="column-elements">
         <mat-form-field>
            <mat-label>Ваучер</mat-label>
            <mat-icon matPrefix>redeem</mat-icon>
            <mat-select [compareWith]="equalVouchers" formControlName="voucher">
               <mat-select-trigger>
                  {{selectedVoucher?.name}}
               </mat-select-trigger>

               @for (voucher of vouchers; track voucher.id) {
                  <mat-option [value]="voucher">{{voucher.name}}</mat-option>
               }
            </mat-select>
         </mat-form-field>
         <mat-form-field>
            <mat-icon matPrefix>fingerprint</mat-icon>
            <mat-label>Код</mat-label>
            <input autocomplete="off" formControlName="identifier" matInput
                   maxlength="64" type="text">
         </mat-form-field>
         <mat-form-field>
            <mat-icon matPrefix>tag</mat-icon>
            <mat-label>Брой</mat-label>
            <input [readonly]="true" [value]="selectedVoucher?.multiplier"
                   autocomplete="off" matInput type="number">
         </mat-form-field>
         <mat-form-field>
            <mat-icon matPrefix>attach_money</mat-icon>
            <mat-label>Единична стойност</mat-label>
            <input [readonly]="true" [value]="selectedVoucher?.price | money"
                   autocomplete="off" matInput type="text">
         </mat-form-field>
      </div>
   </form>
   <div class="final-price">
      <p>Обща стойност</p>
      <p class="amount">{{totalAmount | money}}</p>
   </div>
</app-form-dialog>

import {Component, Inject, OnInit} from '@angular/core';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {touchAllFormFields} from '../../utility/form-utility';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {equalIdentifiables} from '../../utility/utility';
import {Voucher, VoucherDiscount} from '../../data/voucher';
import {Money} from '../../data/common';

export type VoucherDialogInput = {
   voucherDiscount: VoucherDiscount;
   vouchers: Voucher[];
};

@Component({
   selector: 'app-voucher-dialog',
   templateUrl: './voucher-dialog.component.html',
   styleUrls: ['./voucher-dialog.component.scss'],
   standalone: false
})
export class VoucherDialogComponent implements OnInit {

   form = this.fb.group({
      voucher: [null, Validators.required],
      identifier: ['', Validators.required]
   });
   equalVouchers = equalIdentifiables;

   constructor(@Inject(MAT_DIALOG_DATA) public input: VoucherDialogInput,
               private dialogRef: MatDialogRef<VoucherDialogComponent>,
               private fb: UntypedFormBuilder) {

   }

   get selectedVoucher(): Voucher | undefined {
      return this.form.get('voucher')?.value;
   }

   get totalAmount(): Money | undefined {
      const voucher = this.selectedVoucher;
      if (voucher) {
         const total = voucher.price.amount * voucher.multiplier;
         return {amount: total, currency: voucher.price.currency};
      }
   }

   get vouchers(): Voucher[] {
      return this.input.vouchers;
   }

   ngOnInit(): void {
      this.form.patchValue(this.input.voucherDiscount);
   }

   trySubmit(): void {
      if (this.form.valid) {
         this.dialogRef.close(this.form.value);
      } else {
         touchAllFormFields(this.form);
      }
   }

}

import {booleanAttribute, Component, EventEmitter, Input, Output} from '@angular/core';

export type ActionEvent = {
   type: string;
   payload: any;
};

export interface DialogAction {
   name: string;
   color: string;
   event: ActionEvent;
}

@Component({
   selector: 'app-form-dialog',
   templateUrl: './form-dialog.component.html',
   styles: [`
      .dialog-title {
         &:before {
            content: none;
         }

         padding-top: 16px;

         display: flex;
         justify-content: space-between;
      }
   `],
   standalone: false
})
export class FormDialogComponent {
   @Input() heading = '';
   @Input() submitText = 'OK!';
   @Input({transform: booleanAttribute}) actions = true;
   @Input({transform: booleanAttribute}) loading = false;
   @Input({transform: booleanAttribute}) disabled = false;
   @Input() customActions: DialogAction[] = [];

   @Output() submitForm = new EventEmitter<void>();
   @Output() action = new EventEmitter<ActionEvent>();

   submit(): void {
      this.submitForm.emit();
   }

   emitActionEvent(action: DialogAction): void {
      this.action.emit(action.event);
   }
}

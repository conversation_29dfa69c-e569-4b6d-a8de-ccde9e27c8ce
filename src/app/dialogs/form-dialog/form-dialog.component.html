<div class="dialog-title" mat-dialog-title>
   <span>
      {{heading}}
      <ng-content select="[app-dialog-heading]"></ng-content>
   </span>
   <ng-content select="[app-dialog-buttons]"></ng-content>
   <button [mat-dialog-close]="false" color="warn" mat-icon-button>
      <mat-icon>close</mat-icon>
   </button>
</div>
<mat-dialog-content>
   <ng-content></ng-content>
</mat-dialog-content>
@if (actions) {
   <mat-dialog-actions align="end">
      <button [disabled]="loading" [mat-dialog-close]="false" color="warn"
              mat-raised-button>
         Отказ
      </button>
      @for (customAction of customActions; track customAction.name) {
         <button (click)="emitActionEvent(customAction)" mat-raised-button
                 [color]="customAction.color">
            {{customAction.name}}
         </button>
      }
      <button [disabled]="loading || disabled" (click)="submit()" color="primary"
              mat-raised-button>
         @if (loading) {
            <mat-icon>
               <mat-spinner diameter="18" color="primary"/>
            </mat-icon>
         }
         {{submitText}}
      </button>
   </mat-dialog-actions>
}

import {Component, Inject, ViewChild} from '@angular/core';
import {Room} from '../../data/room';
import {DateTime} from 'luxon';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {
   GroupReservationFormComponent
} from '../../forms/group-reservation-form/group-reservation-form.component';

export interface GroupReservationDialogData {
   data: Map<Room, [DateTime, DateTime]>;
}

@Component({
   selector: 'app-group-reservation-dialog',
   templateUrl: './group-reservation-dialog.component.html',
   standalone: false
})
export class GroupReservationDialogComponent {
   @ViewChild(GroupReservationFormComponent) form!: GroupReservationFormComponent;

   constructor(@Inject(MAT_DIALOG_DATA) public input: GroupReservationDialogData,
               private dialogRef: MatDialogRef<GroupReservationDialogComponent>) {
   }

   trySubmit(): void {
      if (this.form.valid) {
         this.dialogRef.close(this.form.value);
      }
   }
}

import {Component, Inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Rate} from '../../data/rate';
import {RateFormComponent} from '../../forms/rate-form/rate-form.component';
import {filterProperties} from '../../utility/utility';

export interface RateDialogInput {
   data?: Rate;
   edit: boolean;
}

@Component({
   selector: 'app-rate-dialog',
   templateUrl: './rate-dialog.component.html',
   standalone: false
})
export class RateDialogComponent {
   @ViewChild(RateFormComponent) form!: RateFormComponent;

   constructor(@Inject(MAT_DIALOG_DATA) public input: RateDialogInput,
      public dialogRef: MatDialogRef<RateDialogComponent>) {
   }

   get windowTitle(): string {
      return this.input.edit ? 'Редактиране на тариф' : 'Създаване на тариф';
   }

   trySubmit(): void {
      if (this.form.valid) {
         const rate = this.form.value;
         // Filter out empty bundle entries
         rate.entries = rate.entries.filter((entry: any) =>
            entry.bundleId && entry.bundleRate.price.amount > 0);
         this.dialogRef.close(rate);
      }
   }
}

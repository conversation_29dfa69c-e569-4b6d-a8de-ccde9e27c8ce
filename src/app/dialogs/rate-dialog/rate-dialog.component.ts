import {Component, Inject, inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Rate} from '../../data/rate';
import {RateFormComponent} from '../../forms/rate-form/rate-form.component';

export interface RateDialogInput {
   data?: Rate;
   edit: boolean;
}

@Component({
   selector: 'app-rate-dialog',
   templateUrl: './rate-dialog.component.html',
   standalone: false
})
export class RateDialogComponent {
   @ViewChild('form') form!: RateFormComponent;

   input: RateDialogInput = inject(MAT_DIALOG_DATA);
   private dialogRef = inject(MatDialogRef<RateDialogComponent>);

   get windowTitle(): string {
      return this.input.edit ? 'Редактиране на тарифа' : 'Създаване на тарифа';
   }

   trySubmit(): void {
      if (this.form.valid) {
         const rate = this.form.value;
         // Filter out empty bundle entries
         rate.entries = rate.entries.filter((entry: any) =>
            entry.bundleId && entry.bundleRate.price.amount > 0);
         this.dialogRef.close(rate);
      }
   }
}

import {Component, inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {CustomerFormComponent} from '../../forms/customer-form/customer-form.component';
import {Customer, fullName} from '../../data/customers/customer';
import {touchAllFormFields} from '../../utility/form-utility';
import {CustomerService} from 'src/app/services/customer.service';
import {switchMap} from 'rxjs';
import {ActionEvent, DialogAction} from '../form-dialog/form-dialog.component';
import {
   ConfirmationDialogData
} from '../confirmation-dialog/confirmation-dialog.component';
import {NotificationService} from 'src/app/services/notification.service';

export type CustomerDialogData = {
   edit: boolean;
   data?: Partial<Customer>;
   allRequired?: boolean;
   allowDelete?: boolean;
};

@Component({
   selector: 'app-customer-dialog',
   templateUrl: './customer-dialog.component.html',
   standalone: false
})
export class CustomerDialogComponent {
   @ViewChild(CustomerFormComponent) form!: CustomerFormComponent;

   dialogRef = inject(MatDialogRef);
   input = inject(MAT_DIALOG_DATA);

   protected requestSent = false;

   private sCustomer = inject(CustomerService);
   private sNotification = inject(NotificationService);

   get windowTitle(): string {
      return this.input.edit ? 'Редактиране на клиент' : 'Създаване на клиент';
   }

   get customActions(): DialogAction[] {
      if (this.input.allowDelete) {
         return [
            {
               name: 'Заличи данните',
               color: 'accent',
               event: {type: 'delete', payload: this.input.data}
            }
         ];
      } else {
         return [];
      }
   }

   trySubmit(): void {
      if (this.form.valid) {
         const customer = this.form.value;
         if (customer.id) {
            this.sCustomer.update(customer).pipe(
               switchMap(() => this.sCustomer.get(customer.id))
            ).subscribe(this.responseHandler());
         } else {
            this.sCustomer.add(customer).subscribe(this.responseHandler());
         }
         this.requestSent = true;
      } else {
         touchAllFormFields(this.form.form);
         touchAllFormFields(this.form.individual);
         touchAllFormFields(this.form.legal);
         touchAllFormFields(this.form.document);
      }
   }

   protected handleAction(actionEvent: ActionEvent) {
      if (actionEvent.type == 'delete') {
         this.deleteCustomer(actionEvent.payload);
      }
   }

   private deleteCustomer(customer: Customer) {
      const data: ConfirmationDialogData = {
         title: 'Изтриване на клиент',
         description: `Сигурни ли сте, че искате да изтриете данните за клиента ${fullName(
            customer)}?`
      };

      this.sNotification.openConfirmationDialog(data).pipe(
         switchMap(() => this.sCustomer.delete(customer.id))
      ).subscribe(this.deleteHandler());

      this.requestSent = true;
   }

   private responseHandler() {
      return {
         next: (customer: Customer) => this.dialogRef.close(customer),
         error: (err: any) => {
            this.requestSent = false;
            console.error(err);
         }
      };
   }

   private deleteHandler() {
      return {
         next: () => this.dialogRef.close(this.input.data),
         error: (err: any) => {
            this.requestSent = false;
            console.error(err);
         }
      };
   }
}

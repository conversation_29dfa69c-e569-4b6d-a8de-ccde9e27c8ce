<app-dialog [actions]="true" [disableConfirmation]="balance?.amount < 0"
            confirmText="Напусни резервация" heading="Напускане на резервация">
   <div app-dialog-buttons class="action-buttons">
      @if (canIssueInvoice) {
         <button (click)="issueInvoice()" [disabled]="balance?.amount < 0"
                 mat-stroked-button>
            Фактура
         </button>
      }
      @if (balance?.amount < 0) {
         <button (click)="delayPayment()" mat-stroked-button>Отложи плащането</button>
      }
      @if (balance?.amount < 0) {
         <button (click)="payRemaining(balance)" [disabled]="!sPayment.enabledNoBank()"
                 color="primary" mat-raised-button>
            Плати остатъка
         </button>
      }
   </div>

   <app-balance-table (balanceChange)="handleBalanceChange($event)"
                      [allowChoice]="false"
                      [payments]="payments"
                      [purchases]="purchases"/>
</app-dialog>

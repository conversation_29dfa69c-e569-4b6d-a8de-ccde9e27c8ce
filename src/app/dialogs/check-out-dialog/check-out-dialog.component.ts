import {ChangeDetectorRef, Component, inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog} from '@angular/material/dialog';
import {Reservation} from '../../data/reservation';
import {PurchaseService} from '../../services/purchase.service';
import {PaymentService} from '../../services/payment.service';
import {Payment} from '../../data/payment';
import {Purchase} from '../../data/purchase';
import {forkJoin, switchMap} from 'rxjs';
import {
   PaymentDialogComponent,
   PaymentDialogInput
} from '../payment-dialog/payment-dialog.component';
import {Money} from '../../data/common';
import {
   BalanceTableComponent
} from '../../components/balance-table/balance-table.component';
import {InvoiceService} from 'src/app/services/invoice.service';
import {AnalyticsService} from '../../services/analytics.service';
import {FinancialAccountService} from 'src/app/services/financial-account.service';
import {ID} from 'src/app/data/identifiable';
import {NotificationService} from 'src/app/services/notification.service';
import {
   ConfirmationDialogData
} from '../confirmation-dialog/confirmation-dialog.component';
import {InvoiceInput, issueInvoice} from 'src/app/utility/invoice-utility';
import {InvoiceCategoryService} from 'src/app/services/invoice-category.service';
import {filter} from 'rxjs/operators';
import {InvoiceCategory, InvoiceCategoryType} from 'src/app/data/invoice';
import {Customer} from 'src/app/data/customers/customer';

@Component({
   selector: 'app-check-out-dialog',
   templateUrl: './check-out-dialog.component.html',
   styles: [`
      .action-buttons > * {
         margin: 0 8px;
      }
   `],
   standalone: false
})
export class CheckOutDialogComponent {
   @ViewChild(BalanceTableComponent) balanceTable!: BalanceTableComponent;

   payments: Payment[] = [];
   purchases: Purchase[] = [];
   balance: Money | undefined;
   issuer: Customer | null = null;
   fiscalCategory: InvoiceCategory | null = null;
   canIssueInvoice = false;

   reservation: Reservation = inject(MAT_DIALOG_DATA);

   sPayment = inject(PaymentService);
   private sPurchase = inject(PurchaseService);
   private sInvoice = inject(InvoiceService);
   private sInvoiceCategory = inject(InvoiceCategoryService);
   private sAnalytics = inject(AnalyticsService);
   private sFinancialAccount = inject(FinancialAccountService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);
   private cd = inject(ChangeDetectorRef);

   constructor() {
      this.renewData();

      this.sInvoice.getIssuer().subscribe(issuer => this.issuer = issuer);

      this.sInvoiceCategory.getAll().subscribe(cs => {
         const category = cs.find(c => c.type === InvoiceCategoryType.tax);
         if (category) {
            this.fiscalCategory = category;
         } else {
            console.error('No fiscal document invoice category found', cs);
         }
      });
   }

   payRemaining(price?: Money): void {
      if (!price) {
         return;
      }

      const data: PaymentDialogInput = {
         data: {price: {...price, amount: Math.abs(price.amount)}},
         edit: false,
         account: this.reservation.activeAccount.id
      };

      const dialog = this.dialog.open(PaymentDialogComponent, {data});

      dialog.afterClosed()
         .pipe(filter(result => !!result))
         .subscribe(() => this.renewData());
   }

   delayPayment(): void {
      const delayPaymentConfirmation: ConfirmationDialogData = {
         title: 'Отлагане на плащането',
         description: 'Желаете ли да преместите всички начисления и плащания в нова сметка?',
         yesText: 'Отложи плащането',
      };

      const titular = this.reservation.titular;
      this.sNotification.openConfirmationDialog(delayPaymentConfirmation).pipe(
         switchMap(() => this.sFinancialAccount.add({titular})),
      ).subscribe(account => this.moveToAccount(account.id));
   }

   handleBalanceChange(balance: Money): void {
      this.balance = balance;
      this.cd.detectChanges();
   }

   issueInvoice(): void {
      if (!this.fiscalCategory || !this.issuer) {
         console.error('Cannot issue an invoice without category and issuer id!');
         return;
      }

      const invoiceInput: InvoiceInput = {
         category: this.fiscalCategory,
         sender: this.issuer,
         accountId: this.reservation.activeAccount.id,
         titular: this.reservation.invoiceReceiver ?? this.reservation.titular,
      };

      issueInvoice(invoiceInput, this.sInvoice, this.dialog)
         .subscribe(() => this.sAnalytics.event('check_out', 'generate_invoice'));
   }

   private renewData(): void {
      forkJoin({
         payments: this.sPayment.getByAccount(this.reservation.activeAccount.id),
         purchases: this.sPurchase.getByAccount(this.reservation.activeAccount.id),
      }).subscribe(({payments, purchases}) => {
         this.payments = payments;
         this.purchases = purchases;
         this.canIssueInvoice = (payments.length + purchases.length) !== 0;
      });
   }

   private moveToAccount(accountId: ID): void {
      forkJoin([
         this.sPayment.moveToStandalone(this.payments.map(p => p.id), accountId),
         this.sPurchase.moveToStandalone(this.purchases.map(p => p.id), accountId),
      ]).subscribe(() => {
         this.renewData();
         this.sNotification.displayNotification('Преместването е успешно', 'Ок!');
      });
   }
}

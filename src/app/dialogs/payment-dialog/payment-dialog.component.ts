import {AfterViewInit, Component, inject, signal, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {PaymentFormComponent} from '../../forms/payment-form/payment-form.component';
import {Payment, PaymentMethod} from '../../data/payment';
import {ID} from 'src/app/data/identifiable';
import {PaymentService} from '../../services/payment.service';
import {NotificationService} from '../../services/notification.service';
import {dialogCloseObserver} from '../../utility/dialog-utility';
import {FiscalAgentService} from '../../services/fiscal-agent.service';
import {forkJoin, of, switchMap} from 'rxjs';
import {FiscalService} from '../../services/fiscal.service';
import {FeatureService} from '../../services/feature.service';

export interface PaymentDialogInput {
   account: ID;
   edit: boolean;
   data?: Partial<Payment>;
   allowDownPayment?: boolean;
   purchases?: ID[];
}

@Component({
   selector: 'app-payment-dialog',
   templateUrl: './payment-dialog.component.html',
   standalone: false
})
export class PaymentDialogComponent implements AfterViewInit {
   @ViewChild(PaymentFormComponent) form!: PaymentFormComponent;

   title = '';
   submitText = signal('');

   input: PaymentDialogInput = inject(MAT_DIALOG_DATA);

   savingPayment = false;

   private sPayment = inject(PaymentService);
   private sFeature = inject(FeatureService);
   private sFiscal = inject(FiscalService);
   private sFiscalAgent = inject(FiscalAgentService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialogRef);

   constructor() {
      if (this.input.edit) {
         this.title = 'Редактиране на плащане';
         this.submitText.set('Запази');
      } else {
         this.title = 'Добавяне на плащане';
         this.updateSubmitText(PaymentMethod.cash);
      }
   }

   ngAfterViewInit(): void {
      this.form.method.valueChanges.subscribe(m => this.updateSubmitText(m));
   }

   trySubmit() {
      if (this.form.valid) {
         const payment = this.form.value;
         const observer = dialogCloseObserver(this.dialog, this.sNotification);

         this.savingPayment = true;
         if (this.input.edit) {
            this.sPayment.update(payment).subscribe(observer);
         } else if (this.sFeature.fiscal() && payment.method != PaymentMethod.bank) {
            this.sFiscal.getNextReceipt(payment, this.input.purchases).pipe(
               switchMap(r => forkJoin([this.sFiscalAgent.receipt(payment, r), of(r)])),
               switchMap(([fpResponse, receipt]) => fpResponse.ok ?
                  this.sFiscal.successReceipt(receipt.id, fpResponse) :
                  this.sFiscal.failReceipt(receipt.id)
               ),
            ).subscribe(observer);
         } else {
            this.sPayment.add(payment).subscribe(observer);
         }
      }
   }

   private updateSubmitText(m?: PaymentMethod): void {
      this.submitText.set(this.sFeature.fiscal() && m && m != PaymentMethod.bank ?
         'Издай касова бележка' : 'Създай плащане');
   }
}

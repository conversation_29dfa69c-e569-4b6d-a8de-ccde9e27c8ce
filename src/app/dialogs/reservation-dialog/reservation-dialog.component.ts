import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {
   ReservationFormComponent
} from '../../forms/reservation-form/reservation-form.component';
import {Reservation} from '../../data/reservation';
import {Consumable} from '../../data/bundles/consumable';
import {NotificationService} from '../../services/notification.service';
import {Note} from '../../data/notes';
import {of, switchMap} from 'rxjs';
import {ID} from '../../data/identifiable';
import {ReservationService} from '../../services/reservation.service';
import {
   IdScannerUseCase
} from '../../components/id-scanner-icon/id-scanner-icon.component';

export interface ReservationDialogData {
   edit: boolean;
   data: Partial<Reservation>;
   roomType?: Consumable;
   readonly?: boolean;
   notesCallback?: (note: Note) => void;
   notesParent?: ID;
}

@Component({
   selector: 'app-reservation-dialog',
   templateUrl: './reservation-dialog.component.html',
   styles: [`
      .action-buttons > * {
         margin-right: 16px;
      }
   `],
   standalone: false
})
export class ReservationDialogComponent implements OnInit {
   @ViewChild(ReservationFormComponent) form!: ReservationFormComponent;

   dialogRef = inject(MatDialogRef);
   input = inject(MAT_DIALOG_DATA);

   isShadow = false;
   isLeisure = false;

   protected idScannerUseCase = IdScannerUseCase;

   private draftId?: ID;
   private sReservation = inject(ReservationService);
   private sNotification = inject(NotificationService);

   get additionalActionsCount(): number | undefined {
      if (this.isShadow && this.isLeisure) {
         return 2;
      } else if (this.isShadow || this.isLeisure) {
         return 1;
      }
   }

   get notesParent() {
      return this.input.edit ? this.input.data.id :
         (this.input.notesParent ? this.input.notesParent : this.draftId);
   }

   ngOnInit(): void {
      this.isShadow = !!this.input.data.isShadow;
      this.isLeisure = !!this.input.data.isLeisure;
      if (!this.input.edit && !this.input.notesParent) {
         this.dialogRef.afterOpened().pipe(
            switchMap(() => this.sReservation.createDraft())
         ).subscribe(draftId => this.draftId = draftId);
         this.dialogRef.beforeClosed().pipe(
            switchMap(submitted => submitted ? of() :
               this.sReservation.deleteDraft(this.draftId!)
            )
         ).subscribe();
      }
   }

   trySubmit(): void {
      if (this.form.valid) {
         this.dialogRef.close({
            addReservation: {
               ...this.form.value,
               isShadow: this.isShadow,
               isLeisure: this.isLeisure
            },
            draftId: this.draftId
         });
      } else {
         this.form.triggerValidation();
      }
   }

   deleteReservation(): void {
      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на резервация',
         description: 'Сигурни ли сте, че искате да изтриете тази резервация?',
         yesText: 'Да, изтрий резервацията'
      }).subscribe(() => this.dialogRef.close({deleteReservation: this.input.data.id}));
   }

   onNewNote(note: Note) {
      if (this.input.notesCallback) {
         this.input.notesCallback(note);
      }
   }
}

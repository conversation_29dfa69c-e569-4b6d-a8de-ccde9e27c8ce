<app-dialog heading="Резервация">
   <ng-container app-dialog-heading>
      <app-id-scanner-icon (scannerResult)="reservationForm.handleIdScanned($event)"
                           [useCase]="idScannerUseCase.reservation"
                           tooltip="Сканирайте документ за самоличност, за да добавите гост."/>
   </ng-container>
   <div app-dialog-buttons class="action-buttons">
      @if (!input.readonly) {
         <button (click)="trySubmit()" [disabled]="!reservationForm.valid"
                 class="action-button align-icons" color="primary" mat-raised-button>
            Запазване
         </button>
      }
      @if (input.edit) {
         <button (click)="deleteReservation()" color="warn" mat-stroked-button
                 class="align-icons">
            Изтриване
         </button>
      }
      @if (!input.readonly) {
         <button [matBadge]="additionalActionsCount"
                 [matMenuTriggerFor]="reservationProperties" mat-icon-button>
            <mat-icon>keyboard_arrow_down</mat-icon>
         </button>
      }
   </div>

   <app-reservation-form #reservationForm
                         [data]="input.data"
                         [disabled]="input.readonly"
                         [edit]="input.edit"
                         [isLeisure]="isLeisure"
                         [roomType]="input.roomType"/>
   <app-notes (addedNote)="onNewNote($event)" [parent]="notesParent"/>
</app-dialog>

<mat-menu #reservationProperties="matMenu">
   <div mat-menu-item>
      <mat-slide-toggle (click)="$event.stopPropagation()" [(ngModel)]="isShadow"
                        color="primary">
         Изключи от ЕСТИ
      </mat-slide-toggle>
   </div>
   <div mat-menu-item>
      <mat-slide-toggle (click)="$event.stopPropagation()" [(ngModel)]="isLeisure"
                        color="primary">
         Стая за почивка
      </mat-slide-toggle>
   </div>
</mat-menu>

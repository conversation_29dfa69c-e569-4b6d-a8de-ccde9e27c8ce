import {Component, inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {moneyFormGroupRequired} from '../utility/form-utility';
import {UntypedFormBuilder} from '@angular/forms';

export interface AmountDialogData {
   heading: string;
   inputLabel: string;
}

@Component({
   template: `
      <app-form-dialog (submitForm)="trySubmit()" [heading]="input.heading">
         <app-money-input [group]="money" [label]="input.inputLabel"
                          class="wide-form-field" wide/>
      </app-form-dialog>
   `,
   standalone: false
})
export class AmountDialogComponent {
   input: AmountDialogData = inject(MAT_DIALOG_DATA);
   dialogRef = inject(MatDialogRef);

   money = moneyFormGroupRequired(inject(UntypedFormBuilder));

   trySubmit(): void {
      if (this.money.valid) {
         this.dialogRef.close(this.money.value.amount);
      }
   }
}

import {Component, Inject, ViewChild} from '@angular/core';
import {
   ReservationSearchComponent
} from '../../forms/inputs/reservation-search/reservation-search.component';
import {ReservationInfo} from '../../data/reservation';
import {FinancialAccount} from '../../data/financial-account';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';

export interface ReservationSelectDialogData {
   title: string;
   inputLabel: string;
   confirmText: string;
   includeFinancialAccounts: boolean;
}

@Component({
   selector: 'app-reservation-select-dialog',
   templateUrl: './reservation-select-dialog.component.html',
   standalone: false
})
export class ReservationSelectDialogComponent {
   @ViewChild(ReservationSearchComponent) reservationSearch!: ReservationSearchComponent;

   constructor(@Inject(MAT_DIALOG_DATA) public input: ReservationSelectDialogData) {
   }

   value(): () => ReservationInfo | FinancialAccount {
      return () => this.reservationSearch.value;
   }
}

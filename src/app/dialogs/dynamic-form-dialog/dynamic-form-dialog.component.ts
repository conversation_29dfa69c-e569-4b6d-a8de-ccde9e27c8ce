import {
   AfterViewInit,
   Component,
   inject,
   InjectionToken,
   Injector,
   OnInit,
   Type,
   ViewChild,
   ViewContainerRef
} from '@angular/core';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MAT_DIALOG_DATA, MatDialogModule, MatDialogRef} from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';

export interface DynamicFormDialogInput {
   form: Type<any>;
   title: string;
   submitText?: string;
   formData?: any;
}

export const FORM_DATA = new InjectionToken<any>('Dynamic form data');

@Component({
   selector: 'app-dynamic-form-dialog',
   template: `
      <div class="dialog-title" mat-dialog-title>
         {{title}}
         <button [mat-dialog-close]="false" color="warn" mat-icon-button>
            <mat-icon>close</mat-icon>
         </button>
      </div>
      <mat-dialog-content>
         <ng-container #formOutlet/>
      </mat-dialog-content>
      <mat-dialog-actions align="end">
         <button [mat-dialog-close]="false" color="warn" mat-raised-button>
            Отказ
         </button>
         <button (click)="trySubmit()" color="primary" mat-raised-button>
            {{submitText}}
         </button>
      </mat-dialog-actions>
   `,
   imports: [
      MatFormFieldModule,
      MatDialogModule,
      MatButtonModule,
      MatIconModule
   ],
   styles: [`
      .dialog-title {
         &:before {
            content: none;
         }

         padding-top: 16px;

         display: flex;
         justify-content: space-between;
      }
   `]
})
export class DynamicFormDialogComponent implements OnInit, AfterViewInit {
   @ViewChild('formOutlet', {read: ViewContainerRef}) vcr!: ViewContainerRef;

   title = '';
   submitText = 'OK!';
   form?: any;

   input: DynamicFormDialogInput = inject(MAT_DIALOG_DATA);
   private dialogRef = inject(MatDialogRef);

   ngOnInit(): void {
      this.title = this.input.title;
      if (this.input.submitText) {
         this.submitText = this.input.submitText;
      }
   }

   ngAfterViewInit(): void {
      setTimeout(() => this.createForm(), 0);
   }

   trySubmit(): void {
      if (this.form && this.form.valid) {
         this.dialogRef.close(this.form.value);
      }
   }

   private createForm(): void {
      const injector = Injector.create({
         providers: [{provide: FORM_DATA, useValue: this.input.formData}]
      });

      const dynamicInstance = this.vcr.createComponent(this.input.form, {injector});
      this.form = dynamicInstance.instance;
   }
}

import {Component, Inject, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Pricing} from '../../data/pricing';
import {PricingFormComponent} from '../../forms/pricing-form/pricing-form.component';
import {filterProperties} from '../../utility/utility';

export interface PricingDialogInput {
   data?: Pricing;
   edit: boolean;
}

@Component({
   selector: 'app-pricing-dialog',
   templateUrl: './pricing-dialog.component.html',
   standalone: false
})
export class PricingDialogComponent {
   @ViewChild(PricingFormComponent) form!: PricingFormComponent;

   constructor(@Inject(MAT_DIALOG_DATA) public input: PricingDialogInput,
               private dialogRef: MatDialogRef<PricingDialogComponent>) {
   }

   get windowTitle(): string {
      return this.input.edit ? 'Редактиране на ценоразпис' : 'Създаване на ценоразпис';
   }

   trySubmit(): void {
      if (this.form.valid) {
         const pricing = this.form.value;
         pricing.bundlePrices =
            filterProperties(pricing.bundlePrices, ([_, price]) => price.amount !== '');
         this.dialogRef.close(pricing);
      }
   }
}

import {booleanAttribute, Component, inject, Input} from '@angular/core';
import {MatDialogRef} from '@angular/material/dialog';

@Component({
   selector: 'app-dialog',
   templateUrl: './dialog.component.html',
   styles: [`
      .dialog-title {
         &:before {
            content: none;
         }

         padding-top: 16px;

         display: flex;
         justify-content: space-between;
      }
   `],
   standalone: false
})
export class DialogComponent {
   @Input() heading = '';
   @Input() subtitle = '';
   @Input() confirmText = 'ОК!';
   @Input() denyText = 'Отказ';
   @Input() titleTooltip = '';
   @Input() alignActions: 'start' | 'center' | 'end' = 'end';
   @Input({transform: booleanAttribute}) actions = false;
   @Input({transform: booleanAttribute}) customActions = false;
   @Input({transform: booleanAttribute}) disableConfirmation = false;
   @Input() valid: () => boolean = () => true;
   @Input() value: () => any = () => true;

   private dialogRef = inject(MatDialogRef<DialogComponent>);

   trySubmit(): void {
      if (this.valid()) {
         this.dialogRef.close(this.value());
      }
   }
}

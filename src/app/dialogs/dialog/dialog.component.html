<div class="dialog-title" mat-dialog-title>
   <div>
      <span>
         {{heading}}
         <ng-content select="[app-dialog-heading]"/>
      </span>
      @if (titleTooltip) {
         <app-info-icon [tooltip]="titleTooltip"/>
      }
      <h3><i>{{subtitle}}</i></h3>
   </div>
   <ng-content select="[app-dialog-buttons]"/>
   <button [mat-dialog-close]="false" color="warn" mat-icon-button>
      <mat-icon>close</mat-icon>
   </button>
</div>
<mat-dialog-content>
   <ng-content/>
</mat-dialog-content>
@if (actions) {
   <mat-dialog-actions [align]="alignActions">
      <button [mat-dialog-close]="false" color="warn" mat-raised-button>
         {{denyText}}
      </button>
      <button (click)="trySubmit()" [disabled]="disableConfirmation"
              color="primary" mat-raised-button>
         {{confirmText}}
      </button>
   </mat-dialog-actions>
} @else if (customActions) {
   <ng-content select="[app-dialog-actions]"/>
}

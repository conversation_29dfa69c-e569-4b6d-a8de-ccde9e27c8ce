<app-dialog [subtitle]="input.subtitle" heading="Начисления и плащания">
      <mat-accordion>
         @if (closedAccountsCount) {
            <mat-expansion-panel (afterExpand)="populateClosedAccounts()">
               <mat-expansion-panel-header>
                  <mat-panel-title>
                     Фискализирани сметки ({{closedAccountsCount}})
                  </mat-panel-title>
               </mat-expansion-panel-header>
               @for (account of closedAccounts; track account.id; let i = $index) {
                  <mat-divider></mat-divider>
                  <h3>
                     {{account.deactivatedAt | dateTime:false}}
                     <app-invoice-button [account]="account" [titular]="invoiceReceiver"
                                       (invoice)="issueInvoice($event)"></app-invoice-button>
                     <button (click)="restoreAccount(i)"
                              [disabled]="sFeature.fiscal() &&  sFiscalAgent.state() !== fs.ready" color="warn"
                              mat-icon-button matTooltip="Анулиране"
                              matTooltipPosition="above"
                              matTooltipShowDelay="0">
                        <mat-icon>restore</mat-icon>
                     </button>
                  </h3>
                  @for (purchase of closedAccountsPurchases[i]; track purchase.id) {
                     <p class="red-text">
                        {{purchase.bundle.name}}: {{purchase.price | money}}
                     </p>
                  }
                  @for (payment of closedAccountsPayments[i]; track payment.id) {
                     <p class="green-text">
                        {{payment.method | paymentMethod}}: {{payment.price | money}}
                     </p>
                  }
                  @if (closedAccountInvoices[i] && closedAccountInvoices[i].length > 0) {
                     <mat-expansion-panel>
                        <mat-expansion-panel-header>Фактури
                           ({{closedAccountInvoices[i].length}})
                        </mat-expansion-panel-header>
                        @for (invoice of closedAccountInvoices[i]; track invoice.id) {
                           <p>
                              {{invoice.category.name}} No. {{invoice.invoiceNumber}}
                              от {{invoice.timestamp | dateTime}}

                              <button (click)="showInvoice(invoice, false)" mat-icon-button
                                    matTooltip="Оригинал" matTooltipPosition="above"
                                    matTooltipShowDelay="0">
                                 <mat-icon>insert_drive_file</mat-icon>
                              </button>
                              @if (invoice.category.isFiscalDocument) {
                                 <button (click)="showInvoice(invoice, true)" mat-icon-button
                                       matTooltip="Копие" matTooltipPosition="above"
                                       matTooltipShowDelay="0">
                                    <mat-icon>file_copy</mat-icon>
                                 </button>
                              }
                           </p>
                        }
                     </mat-expansion-panel>
                  }
               }
            </mat-expansion-panel>
         }
         @if (invoices.length > 0) {
            <mat-expansion-panel>
               <mat-expansion-panel-header>Фактури
                  ({{invoices.length}})
               </mat-expansion-panel-header>
               @for (invoice of invoices; track invoice.id) {
                  <p>
                     {{invoice.category.name}} No. {{invoice.invoiceNumber}}
                     от {{invoice.timestamp | dateTime}}

                     <button (click)="showInvoice(invoice, false)" mat-icon-button
                              matTooltip="Оригинал" matTooltipPosition="above"
                              matTooltipShowDelay="0">
                        <mat-icon>insert_drive_file</mat-icon>
                     </button>
                     @if (invoice.category.isFiscalDocument) {
                        <button (click)="showInvoice(invoice, true)" mat-icon-button
                                 matTooltip="Копие" matTooltipPosition="above"
                                 matTooltipShowDelay="0">
                           <mat-icon>file_copy</mat-icon>
                        </button>
                     }
                  </p>
               }
            </mat-expansion-panel>
         }
      </mat-accordion>

   <app-balance-table (invoice)="issueInvoice($event)" (moveSelected)="moveSelected()"
                      (payment)="onPayment($event)" (purchase)="onPurchase($event)"
                      (renew)="onRenew()" [account]="accountChain.activeAccount"
                      [edit]="isActive" [invoiceReceiver]="invoiceReceiver"
                      [payments]="payments"
                      [purchases]="purchases"/>
</app-dialog>

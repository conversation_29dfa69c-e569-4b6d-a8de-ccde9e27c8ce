import {Component, inject, OnInit, ViewChild} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {
   PurchaseDialogComponent,
   PurchaseDialogInput
} from '../purchase-dialog/purchase-dialog.component';
import {
   PaymentDialogComponent,
   PaymentDialogInput
} from '../payment-dialog/payment-dialog.component';
import {PurchaseService} from '../../services/purchase.service';
import {forkJoin, iif, Observable, of, switchMap} from 'rxjs';
import {PaymentService} from '../../services/payment.service';
import {filter} from 'rxjs/operators';
import {isPayment, Payment} from '../../data/payment';
import {isPurchase, Purchase} from '../../data/purchase';
import {
   ReservationSelectDialogComponent,
   ReservationSelectDialogData
} from '../reservation-select-dialog/reservation-select-dialog.component';
import {
   BalanceTableComponent
} from '../../components/balance-table/balance-table.component';
import {Invoice, InvoiceCategory} from 'src/app/data/invoice';
import {InvoiceService} from 'src/app/services/invoice.service';
import {FinancialAccount, FinancialAccountChain} from 'src/app/data/financial-account';
import {generateInvoiceDocument, InvoiceData} from 'src/app/utility/invoice-utility';
import {FinancialAccountService} from 'src/app/services/financial-account.service';
import {NotificationService} from 'src/app/services/notification.service';
import {FiscalAgentService, FiscalState} from '../../services/fiscal-agent.service';
import {FeatureService} from '../../services/feature.service';
import {
   PaymentReversalDialogComponent,
   PaymentReversalDialogInput
} from '../payment-reversal-dialog/payment-reversal-dialog.component';
import {FiscalService} from '../../services/fiscal.service';
import {sumMoney} from '../../utility/money-utility';
import {Customer} from 'src/app/data/customers/customer';
import {ID} from 'src/app/data/identifiable';

export enum FinancialContext {
   reservation,
   cancellation,
   customer
}

export interface BalanceDialogData {
   accountChain: FinancialAccountChain;
   movementAllowed: boolean;
   context: FinancialContext;
   subtitle: string;
}

@Component({
   selector: 'app-balance-dialog',
   templateUrl: './balance-dialog.component.html',
   styles: `
      .action-buttons {
         display: flex;
         flex-direction: row;
         align-items: flex-start;
         justify-content: space-between;

         & > * {
            margin: 0 32px;
         }
      }
   `,
   standalone: false
})
export class BalanceDialogComponent implements OnInit {
   @ViewChild(BalanceTableComponent) balanceTable!: BalanceTableComponent;

   input: BalanceDialogData = inject(MAT_DIALOG_DATA);

   accountChain = this.input.accountChain;

   payments: Payment[] = [];
   purchases: Purchase[] = [];
   invoices: Invoice[] = [];
   invoiceCategories: InvoiceCategory[] = [];

   closedAccountsCount = 0;
   closedAccounts: FinancialAccount[] = [];
   closedAccountsPurchases: Purchase[][] = [];
   closedAccountsPayments: Payment[][] = [];
   closedAccountInvoices: Invoice[][] = [];
   closedAccountsPopulated = false;

   fs = FiscalState;

   sFeature = inject(FeatureService);
   sFiscalAgent = inject(FiscalAgentService);
   private sPurchase = inject(PurchaseService);
   private sPayment = inject(PaymentService);
   private sInvoice = inject(InvoiceService);
   private sFinancialAccount = inject(FinancialAccountService);
   private sFiscal = inject(FiscalService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);
   private dialogRef = inject(MatDialogRef<BalanceDialogComponent>);

   get isActive(): boolean {
      return !this.accountChain.activeAccount.deactivatedAt;
   }

   get invoiceReceiver(): Customer | undefined {
      return this.accountChain?.invoiceReceiver;
   }

   ngOnInit(): void {
      this.renewData();
   }

   onPurchase(purchase?: Purchase) {
      const data: PurchaseDialogInput = {
         edit: !!purchase,
         account: this.accountChain.activeAccount.id,
         data: purchase,
      };

      const dialog = this.dialog.open(PurchaseDialogComponent, {data});

      dialog.afterClosed()
         .pipe(filter(result => !!result))
         .subscribe(() => this.renewData());
   }

   onPayment(payment?: Payment) {
      this.openPaymentDialog(payment).subscribe(() => this.onRenew());
   }

   onRenew(): void {
      if (this.sFeature.fiscal()) {
         this.renewDataFull();
      } else {
         this.renewData();
      }
   }

   moveSelected() {
      const data: ReservationSelectDialogData = {
         title: 'Местене на начисления и плащания',
         inputLabel: 'Резервация или сметка',
         confirmText: 'Премести начисленията',
         includeFinancialAccounts: true,
      };
      const dialog = this.dialog.open(ReservationSelectDialogComponent, {data});

      dialog.afterClosed().pipe(
         filter(result => !!result),
         switchMap(destination => {
            const selection = this.balanceTable.selection.selected;
            const payments = selection.filter(isPayment).map(p => p.id);
            const purchases = selection.filter(isPurchase).map(p => p.id);

            if ('start' in destination) {
               return forkJoin([
                  this.sPayment.moveToReservation(payments, destination.id),
                  this.sPurchase.moveToReservation(purchases, destination.id),
               ]);
            } else {
               return forkJoin([
                  this.sPayment.moveToStandalone(payments, destination.id),
                  this.sPurchase.moveToStandalone(purchases, destination.id),
               ]);
            }
         }),
      ).subscribe(() => this.renewData());
   }

   issueInvoice(invoice: InvoiceData) {
      this.handleInvoice(invoice, true);
   }

   showInvoice(invoice: Invoice, isCopy: boolean) {
      return generateInvoiceDocument(invoice, this.sInvoice, isCopy)
         .subscribe(invoiceData => this.handleInvoice(invoiceData, false));
   }

   populateClosedAccounts(): void {
      if (this.closedAccountsPopulated) {
         return;
      }

      this.closedAccounts = this.accountChain.accounts.filter(fa => !!fa.deactivatedAt)
         .sort((lhs, rhs) => {
            return rhs.deactivatedAt!.toMillis() - lhs.deactivatedAt!.toMillis();
         });

      forkJoin(this.closedAccounts.map(a => this.sPurchase.getByAccount(a.id))).subscribe(
         purchases => this.closedAccountsPurchases = purchases
      );
      forkJoin(this.closedAccounts.map(a => this.sPayment.getByAccount(a.id))).subscribe(
         payments => this.closedAccountsPayments = payments
      );
      forkJoin(this.closedAccounts.map(a => this.sInvoice.search(a.id))).subscribe(
         invoices => this.closedAccountInvoices = invoices.map(
            accountInvoices => accountInvoices.sort(
               (a, b) => b.timestamp!.toMillis() - a.timestamp!.toMillis()))
      );

      this.closedAccountsPopulated = true;
   }

   restoreAccount(accountIndex: number) {
      const payment = this.closedAccountsPayments[accountIndex]?.find(p => p.receiptId);
      const receiptId = payment?.receiptId;

      let receiptReversal;
      if (this.sFeature.fiscal() && payment && receiptId) {
         const data: PaymentReversalDialogInput = {paymentAmount: payment.price.amount};
         receiptReversal = this.dialog.open(PaymentReversalDialogComponent, {data}).afterClosed().pipe(
            filter(result => !!result),
            switchMap(reason => forkJoin([this.sFiscal.getReceipt(receiptId), of(reason)])),
            switchMap(([receipt, reason]) => this.sFiscalAgent.reverse(payment, receipt, reason)),
            filter(fp => fp.ok)
         );
      }

      const accountId = this.closedAccounts[accountIndex].id;
      let accountRestoration;
      if (receiptReversal) {
         accountRestoration = receiptReversal.pipe(switchMap(() => this.sFinancialAccount.restore(accountId)))
      } else {
         accountRestoration = this.sNotification.openConfirmationDialog({
            title: 'Анулиране на сметка',
            description: `Сигурни ли сте, че искате да анулирате сметката?`,
         }).pipe(switchMap(() => this.sFinancialAccount.restore(accountId)));
      }

      accountRestoration.pipe(
         switchMap(cancellationId => iif(
            () => !!cancellationId,
            this.sInvoice.get(cancellationId!!).pipe(switchMap(cancellationInvoice => generateInvoiceDocument(cancellationInvoice, this.sInvoice, false))),
            of(null)
         ))
      ).subscribe({
         next: () => {
            switch (this.input.context) {
               case FinancialContext.reservation:
               case FinancialContext.cancellation: {
                  this.renewDataFull();
               } break;
               case FinancialContext.customer: {
                  this.sFinancialAccount.ofTitular(this.input.accountChain.id)
                     .subscribe(accounts => {
                        const restoredAccount = accounts.find(account => account.id == accountId);
                        if (restoredAccount) {
                           this.accountChain =
                              {id: this.input.accountChain.id, activeAccount: restoredAccount, accounts};
                           this.renewData();
                           this.closedAccountsPopulated = false;
                           this.populateClosedAccounts();
                        } else {
                           console.error(`Could not find restored account with id: ${accountId}`);
                           this.dialogRef.close();
                        }
                     });
               } break;
            }
         },
         error: err => this.sNotification.displayError(err)
      });
   }

   private openPaymentDialog(payment?: Payment): Observable<void> {
      const {balance, selection} = this.balanceTable;
      const fiscal = this.sFeature.fiscal();

      const purchases = fiscal ? selection.selected.filter(isPurchase) : [];
      const balanceAmount = -balance.amount;
      const amount = purchases.length === 0 ?
         Math.max(0, balanceAmount) :
         Math.min(balanceAmount, sumMoney(purchases.map(p => p.price)).amount);

      const data: PaymentDialogInput = {
         data: payment ?? {
            price: {
               amount,
               currency: balance.currency
            }
         },
         edit: !!payment,
         allowDownPayment: true,
         account: this.accountChain.activeAccount.id,
         purchases: purchases.map(p => p.id),
      };

      const dialog = this.dialog.open(PaymentDialogComponent, {data});
      return dialog.afterClosed().pipe(filter(result => !!result));
   }

   private renewData(): void {
      this.closedAccountsCount =
         this.accountChain.accounts.filter(fa => !!fa.deactivatedAt).length;

      forkJoin({
         purchases: this.sPurchase.getByAccount(this.accountChain.activeAccount.id),
         payments: this.sPayment.getByAccount(this.accountChain.activeAccount.id),
         invoices: this.sInvoice.search(this.accountChain.activeAccount.id)
      }).subscribe(({purchases, payments, invoices}) => {
         this.purchases = purchases;
         this.payments = payments;
         this.invoices = invoices.sort(
            (a, b) => b.timestamp!.toMillis() - a.timestamp!.toMillis());
      });
   }

   private renewDataFull(): void {
      switch (this.input.context) {
         case FinancialContext.reservation:
         case FinancialContext.cancellation: {
            this.sFinancialAccount.getChain(this.input.accountChain.id)
               .subscribe(accountChain => {
                  this.accountChain = accountChain;

                  this.renewData();

                  this.closedAccountsPopulated = false;
                  this.populateClosedAccounts();
               });
         }
            break;

         case FinancialContext.customer: {
            this.sFinancialAccount.ofTitular(this.input.accountChain.id)
               .subscribe(accounts => {
                  const activeAccounts = accounts.filter(account => !account.deactivatedAt);
                  if (activeAccounts.length === 0) {
                     this.dialogRef.close();
                  } else {
                     this.accountChain =
                        {id: this.input.accountChain.id, activeAccount: activeAccounts[0], accounts};
                     this.renewData();

                     this.closedAccountsPopulated = false;
                     this.populateClosedAccounts();
                  }
               });
         }
            break;
      }
   }

   private handleInvoice(data: InvoiceData, fullRefresh: boolean): void {
      if (fullRefresh && !data.isDocumentCopy &&
         data.invoice.category?.isFiscalDocument) {
         this.renewDataFull();
      } else {
         this.closedAccountsPopulated = false;
         this.populateClosedAccounts();
         this.renewData();
      }
   }
}

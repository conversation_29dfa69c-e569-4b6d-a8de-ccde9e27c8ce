import {Component, inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {Customer, fullName} from 'src/app/data/customers/customer';
import {FinancialAccount} from 'src/app/data/financial-account';

export interface CustomerAccountsDialogData {
   customer: Customer,
   accounts: FinancialAccount[]
}

@Component({
   selector: 'app-customer-accounts-dialog',
   standalone: false,

   templateUrl: './customer-accounts-dialog.component.html'
})
export class CustomerAccountsDialogComponent {
   input: CustomerAccountsDialogData = inject(MAT_DIALOG_DATA);
   private dialogRef = inject(MatDialogRef);

   get customerName() {
      return fullName(this.input.customer);
   }

   get accounts() {
      return this.input.accounts;
   }

   protected selectAccount(account: FinancialAccount) {
      this.dialogRef.close(account);
   }
}

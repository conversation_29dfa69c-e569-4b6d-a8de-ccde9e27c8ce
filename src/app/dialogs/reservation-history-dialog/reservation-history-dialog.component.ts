import {Component, inject} from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import {ReservationService} from '../../services/reservation.service';
import {Reservation, ReservationHistory} from '../../data/reservation';
import {ReservationPipe} from '../../pipes/reservation.pipe';
import {getResStatusName} from '../../utility/reservation-utility';
import {DateTimePipe} from '../../pipes/date-time.pipe';
import {moneyToString} from '../../utility/utility';
import {MatTableDataSource} from '@angular/material/table';

export interface ReservationHistoryDialogInput {
   reservation: Reservation;
}

const historySort = (rh1: ReservationHistory, rh2: ReservationHistory) =>
   rh1.timestamp.valueOf() - rh2.timestamp.valueOf();

@Component({
   selector: 'app-reservation-history-dialog',
   templateUrl: './reservation-history-dialog.component.html',
   standalone: false
})
export class ReservationHistoryDialogComponent {
   source = new MatTableDataSource();
   subtitle: string;

   columns = ['timestamp', 'operator', 'titular', 'room', 'start', 'end', 'bundle',
      'status', 'manualPrice'];

   input: ReservationHistoryDialogInput = inject(MAT_DIALOG_DATA);

   constructor() {
      inject(ReservationService).getHistory(this.input.reservation.id)
         .subscribe(hs => this.renewData(hs));

      this.subtitle = ReservationPipe.toString(this.input.reservation);
   }

   private renewData(history: ReservationHistory[]): void {
      const sorted = history.sort(historySort);
      const result = sorted.map(h => ({
         timestamp: DateTimePipe.toString(h.timestamp, false),
         operator: h.operator,
         titular: {data: h.titular, changed: false},
         room: {data: h.room, changed: false},
         start: {data: DateTimePipe.toString(h.start), changed: false},
         end: {data: DateTimePipe.toString(h.end), changed: false},
         bundle: {data: h.bundle, changed: false},
         status: {data: getResStatusName(h.status), changed: false},
         manualPrice: {data: moneyToString(h.manualPrice, 'няма'), changed: false},
      }));

      for (let i = 1; i < result.length; ++i) {
         for (const prop of this.columns.slice(2)) {
            // @ts-expect-error accessing properties by string
            if (result[i][prop].data !== result[i - 1][prop].data) {
               // @ts-expect-error accessing properties by string
               result[i][prop].changed = true;
            }
         }
      }

      this.source.data = result;
   }
}

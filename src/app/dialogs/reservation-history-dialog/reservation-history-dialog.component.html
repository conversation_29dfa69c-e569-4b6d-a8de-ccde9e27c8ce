<app-dialog [subtitle]="subtitle" heading="История">
   <table [dataSource]="source" mat-table style="width: 100%">
      <mat-text-column headerText="Дата" name="timestamp"/>
      <mat-text-column headerText="Оператор" name="operator"/>

      <ng-container matColumnDef="titular">
         <th *matHeaderCellDef mat-header-cell>Титуляр</th>
         <td *matCellDef="let h" [class.red-text]="h.titular.changed" mat-cell>
            {{h.titular.data}}
         </td>
      </ng-container>

      <ng-container matColumnDef="room">
         <th *matHeaderCellDef mat-header-cell>Стая</th>
         <td *matCellDef="let h" [class.red-text]="h.room.changed" mat-cell>
            {{h.room.data}}
         </td>
      </ng-container>

      <ng-container matColumnDef="start">
         <th *matHeaderCellDef mat-header-cell>Начало</th>
         <td *matCellDef="let h" [class.red-text]="h.start.changed" mat-cell>
            {{h.start.data}}
         </td>
      </ng-container>

      <ng-container matColumnDef="end">
         <th *matHeaderCellDef mat-header-cell>Край</th>
         <td *matCellDef="let h" [class.red-text]="h.end.changed" mat-cell>
            {{h.end.data}}
         </td>
      </ng-container>

      <ng-container matColumnDef="bundle">
         <th *matHeaderCellDef mat-header-cell>Пакет</th>
         <td *matCellDef="let h" [class.red-text]="h.bundle.changed" mat-cell>
            {{h.bundle.data}}
         </td>
      </ng-container>

      <ng-container matColumnDef="status">
         <th *matHeaderCellDef mat-header-cell>Статус</th>
         <td *matCellDef="let h" [class.red-text]="h.status.changed" mat-cell>
            {{h.status.data}}
         </td>
      </ng-container>

      <ng-container matColumnDef="manualPrice">
         <th *matHeaderCellDef mat-header-cell>Ръчна цена</th>
         <td *matCellDef="let h" [class.red-text]="h.manualPrice.changed" mat-cell>
            {{h.manualPrice.data}}
         </td>
      </ng-container>

      <tr *matHeaderRowDef="columns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: columns" mat-row></tr>

      <tr *matNoDataRow class="mat-row">
         <td [colSpan]="columns.length" class="mat-cell" style="text-align: center">
            Няма история за тази резервация.
         </td>
      </tr>
   </table>
</app-dialog>

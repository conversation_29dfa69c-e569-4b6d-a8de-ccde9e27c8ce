import {Component, inject, ViewChild} from '@angular/core';
import {UntypedFormControl, Validators} from '@angular/forms';
import {Customer} from '../../data/customers/customer';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {
   CustomerSelectComponent
} from '../../forms/inputs/customer-select/customer-select.component';

export interface CustomerPickerDialogData {
   customer?: Customer;
   useCase: string;
}

@Component({
   selector: 'app-customer-picker-dialog',
   template: `
      <app-form-dialog (submitForm)="trySubmit()" [heading]="title">
         <app-customer-select [allRequired]="true" [autoOpen]="true" [control]="control"/>
      </app-form-dialog>
   `,
   standalone: false
})
export class CustomerPickerDialogComponent {
   @ViewChild(CustomerSelectComponent) customerSelect!: CustomerSelectComponent;

   data: CustomerPickerDialogData = inject(MAT_DIALOG_DATA);

   control = new UntypedFormControl(this.data.customer, Validators.required);
   title = `Лице за ${this.data.useCase}`;

   private dialogRef = inject(MatDialogRef);

   trySubmit(): void {
      if (this.control.valid) {
         this.dialogRef.close(this.control.value);
      }
   }
}

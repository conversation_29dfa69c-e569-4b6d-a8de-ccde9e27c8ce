import {
   AfterViewInit,
   Directive,
   ElementRef,
   HostListener,
   inject,
   Input
} from '@angular/core';
import {AnalyticsService} from '../services/analytics.service';

@Directive({
   selector: '[appEvent]',
   standalone: false
})
export class EventDirective implements AfterViewInit {
   @Input() appEvent = 'unknown_event';
   @Input() appLabel?: string;
   category = 'unknown_category';

   private sAnalytics = inject(AnalyticsService);
   private element = inject(ElementRef);

   @HostListener('click', ['$event'])
   sendEvent(event: PointerEvent): void {
      this.sAnalytics.event(this.category, this.appEvent, this.appLabel);
      event.stopPropagation();
   }

   ngAfterViewInit() {
      let node = this.element.nativeElement;
      while (node && !node.dataset.eventCategory) {
         node = node.parentElement;
      }

      if (node) {
         this.category = node.dataset.eventCategory;
      }
   }
}

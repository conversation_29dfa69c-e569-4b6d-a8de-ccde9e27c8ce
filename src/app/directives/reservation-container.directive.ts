import {Directive, Input, OnInit, ViewContainerRef} from '@angular/core';
import {assert} from '../utility/utility';

@Directive({
   selector: '[appReservationContainer]',
   standalone: false
})
export class ReservationContainerDirective implements OnInit {
   @Input() roomId = '';

   constructor(public viewContainerRef: ViewContainerRef) {
   }

   ngOnInit(): void {
      assert(this.roomId.length > 0, 'roomId is not set on appReservationContainer');
   }
}

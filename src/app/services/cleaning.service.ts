import {inject, Injectable} from '@angular/core';
import {Cleaning, CleaningPriority} from '../data/cleaning';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {Consumable} from '../data/bundles/consumable';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {ConsumableService} from './consumable.service';

interface CleaningDTO extends Identifiable {
   name: string;
   periodDays: Record<ID, number>;
   applyOnCompleted: boolean;
   priority: CleaningPriority;
}

@Injectable({
   providedIn: 'root'
})
export class CleaningService extends DtoCache<Cleaning, CleaningDTO> {
   private sConsumable = inject(ConsumableService);

   constructor(http: HttpClient) {
      super('cleaning', http);
   }

   protected mapToLocal({periodDays, ...rest}: CleaningDTO): Observable<Cleaning> {
      return this.sConsumable.getAll().pipe(
         map(consumables => ({
            periodDays: Object.entries(periodDays).map(([id, days]) => ({
               roomType: consumables.find(c => c.id === id) as Consumable,
               days
            })),
            ...rest
         }))
      );
   }

   protected mapToRemote({periodDays, ...rest}: MaybeID<Cleaning>): MaybeID<CleaningDTO> {
      return {
         periodDays: periodDays.reduce((result, {roomType, days}) => {
            result[roomType.id] = days;
            return result;
         }, {} as Record<ID, number>),
         ...rest,
      };
   }
}

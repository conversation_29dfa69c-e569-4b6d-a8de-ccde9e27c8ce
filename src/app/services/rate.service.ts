import {Injectable, inject} from '@angular/core';
import {Rate} from '../data/rate';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {Observable, of, forkJoin} from 'rxjs';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {CalendarRange, Money, DayOfWeek} from '../data/common';
import {DateTime} from 'luxon';
import {ReservationSourceService} from './reservation-source.service';
import {map, defaultIfEmpty} from 'rxjs/operators';

export interface BundleEntryDTO {
   bundleId: ID;
   bundleRate: {
      price: Money;
   };
}

export interface CalendarRangeDTO {
   dateRange: {
      dateStart: string;
      dateEnd: string;
   };
   weekDays?: string[];
}

export interface RateDTO extends Identifiable {
   name: string;
   activeRanges: CalendarRangeDTO[];
   reservationSources?: ID[];
   entries: BundleEntryDTO[];
}

@Injectable({
   providedIn: 'root'
})
export class RateService extends DtoCache<Rate, RateDTO> {
   private sReservationSource = inject(ReservationSourceService);

   constructor(http: HttpClient) {
      super('rate', http);
   }

   protected mapToLocal({activeRanges, reservationSources, ...rest}: RateDTO): Observable<Rate> {
      const mappedRanges = activeRanges.map(range => ({
         dateRange: {
            start: DateTime.fromMillis(parseInt(range.dateRange.dateStart, 10)),
            end: DateTime.fromMillis(parseInt(range.dateRange.dateEnd, 10))
         },
         weekDays: range.weekDays?.map(day => day as DayOfWeek)
      } as CalendarRange));

      if (!reservationSources || reservationSources.length === 0) {
         return of({
            activeRanges: mappedRanges,
            reservationSources: undefined,
            ...rest,
         });
      }

      return forkJoin(reservationSources.map(id => this.sReservationSource.get(id))).pipe(
         map(sources => ({
            activeRanges: mappedRanges,
            reservationSources: sources,
            ...rest,
         })),
         defaultIfEmpty({
            activeRanges: mappedRanges,
            reservationSources: undefined,
            ...rest,
         })
      );
   }

   protected mapToRemote({activeRanges, reservationSources, ...rest}: MaybeID<Rate>): MaybeID<RateDTO> {
      return {
         activeRanges: activeRanges.map(range => ({
            dateRange: {
               dateStart: range.dateRange.start.toMillis().toString(),
               dateEnd: range.dateRange.end.toMillis().toString()
            },
            weekDays: range.weekDays?.map(day => day.toString())
         } as CalendarRangeDTO)),
         reservationSources: reservationSources?.map(source => source.id),
         ...rest,
      };
   }
}

import {Injectable} from '@angular/core';
import {Rate} from '../data/rate';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {Observable, of} from 'rxjs';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {CalendarRange, Money, DayOfWeek} from '../data/common';
import {DateTime} from 'luxon';

export interface BundleEntryDTO {
   bundleId: ID;
   bundleRate: {
      price: Money;
   };
}

export interface CalendarRangeDTO {
   dateRange: {
      dateStart: string;
      dateEnd: string;
   };
   weekDays?: string[];
}

export interface RateDTO extends Identifiable {
   name: string;
   activeRanges: CalendarRangeDTO[];
   entries: BundleEntryDTO[];
}

@Injectable({
   providedIn: 'root'
})
export class RateService extends DtoCache<Rate, RateDTO> {
   constructor(http: HttpClient) {
      super('rate', http);
   }

   protected mapToLocal({activeRanges, ...rest}: RateDTO): Observable<Rate> {
      return of({
         activeRanges: activeRanges.map(range => ({
            dateRange: {
               start: DateTime.fromMillis(parseInt(range.dateRange.dateStart, 10)),
               end: DateTime.fromMillis(parseInt(range.dateRange.dateEnd, 10))
            },
            weekDays: range.weekDays?.map(day => day as DayOfWeek)
         } as CalendarRange)),
         ...rest,
      });
   }

   protected mapToRemote({activeRanges, ...rest}: MaybeID<Rate>): MaybeID<RateDTO> {
      return {
         activeRanges: activeRanges.map(range => ({
            dateRange: {
               dateStart: range.dateRange.start.toMillis().toString(),
               dateEnd: range.dateRange.end.toMillis().toString()
            },
            weekDays: range.weekDays?.map(day => day.toString())
         } as CalendarRangeDTO)),
         ...rest,
      };
   }
}

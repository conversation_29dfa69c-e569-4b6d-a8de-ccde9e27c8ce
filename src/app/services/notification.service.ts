import {Injectable} from '@angular/core';
import {MatSnackBar, MatSnackBarDismiss} from '@angular/material/snack-bar';
import {MatDialog} from '@angular/material/dialog';
import {
   ConfirmationDialogComponent,
   ConfirmationDialogData
} from '../dialogs/confirmation-dialog/confirmation-dialog.component';
import {Observable} from 'rxjs';
import {filter} from 'rxjs/operators';
import {FPMessage} from '../data/fiscal/fiscal-agent';
import {FiscalNotificationComponent} from '../components/fiscal-notification.component';

@Injectable({
   providedIn: 'root'
})
export class NotificationService {
   constructor(private snackbar: MatSnackBar,
               private dialog: MatDialog) {
   }

   displayNotification(msg: string,
                       action?: string,
                       actionFn?: () => void): void {
      this.snackbar.open(msg, action, {
         duration: 5000,
         horizontalPosition: 'right',
         verticalPosition: 'top',
      }).afterDismissed()
         .subscribe(result => result.dismissedByAction && actionFn && actionFn());
   }

   displayReloadNotification(seconds: number): Observable<MatSnackBarDismiss> {
      const msg = `Налична е нова версия. Страницата ще се презареди след ${seconds} секунди!`;
      return this.snackbar.open(msg, 'Презареди сега', {
         duration: seconds * 1000,
         horizontalPosition: 'left',
         verticalPosition: 'bottom'
      }).afterDismissed();
   }

   displayFiscalErrors(messages?: FPMessage[]): void {
      this.snackbar.openFromComponent(FiscalNotificationComponent, {
         data: messages,
         duration: 30000,
         horizontalPosition: 'right',
         verticalPosition: 'top',
      });
   }

   openConfirmationDialog(data: ConfirmationDialogData,
                          maxWidth?: string): Observable<true> {
      const dialog = this.dialog.open(ConfirmationDialogComponent, {data, maxWidth});
      return dialog.afterClosed().pipe(filter(result => !!result));
   }

   displayError(err: any, pre?: string) {
      const msg = err?.error?.message ?? err?.message ?? JSON.stringify(err);
      const explanation = pre ?? 'Възникна неочаквана грешка';
      this.displayNotification(`${explanation}: ${msg}`, 'Затвори');
   }
}

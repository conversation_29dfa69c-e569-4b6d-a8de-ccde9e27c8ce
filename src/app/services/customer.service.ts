import {ID, MaybeID, NoID} from '../data/identifiable';
import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable, of, tap} from 'rxjs';
import {defaultIfEmpty, switchMap} from 'rxjs/operators';
import {Customer} from '../data/customers/customer';
import {DtoCache} from '../utility/dto-cache';
import {Money} from '../data/common';
import {DateTime} from 'luxon';
import {entity} from '../utility/http-utility';

@Injectable({
   providedIn: 'root'
})
export class CustomerService extends DtoCache<Customer, Customer> {
   constructor(http: HttpClient) {
      super('customer', http);
   }

   find(query: string, legalOnly: boolean): Observable<Customer[]> {
      const params = {query, legalOnly};

      return this.http.get<Customer[]>(`${this.baseUrl}/search`, {params}).pipe(
         tap(cs => this.setCacheEntries(cs)),
         defaultIfEmpty([]),
      );
   }

   loadGuestsInRange(start: DateTime, end: DateTime): Observable<Customer[]> {
      const params = {from: start.toMillis(), to: end.toMillis()};
      return this.http.get<Customer[]>(`${this.baseUrl}/guests`, {params}).pipe(
         tap(cs => this.setCacheEntries(cs)),
         defaultIfEmpty([]),
      );
   }

   loadAccountTitulars(): Observable<Customer[]> {
      return this.http.get<Customer[]>(`${this.baseUrl}/account-titulars`).pipe(
         tap(cs => this.setCacheEntries(cs)),
         defaultIfEmpty([]),
      );
   }

   getTotalPayments(id: ID): Observable<Money | null> {
      return this.http.get<Money>(`${this.idUrl(id)}/total-payments`);
   }

   saveScanned(input: NoID<Customer>): Observable<Customer> {
      return this.http.post<Customer>(`${this.baseUrl}/id-scanner-input`, input).pipe(
         tap(customer => this.setCacheEntries([customer]))
      );
   }

   mergeDuplicateToOriginal(duplicateId: ID, originalId: ID): Observable<Customer> {
      return this.http.post<void>(`${this.idUrl(originalId)}/duplicate`,
         entity(duplicateId)).pipe(
         switchMap(() => this.get(originalId)),
         tap(() => this.deleteCacheEntries([duplicateId]))
      );
   }

   protected mapToLocal(dto: Customer): Observable<Customer> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<Customer>): MaybeID<Customer> {
      return entry;
   }
}

import {TestBed} from '@angular/core/testing';
import {HttpClientTestingModule, HttpTestingController} from '@angular/common/http/testing';
import {RateService, RateDTO} from './rate.service';
import {Rate} from '../data/rate';
import {DateTime} from 'luxon';
import {Currency, DayOfWeek} from '../data/common';

describe('RateService', () => {
   let service: RateService;
   let httpMock: HttpTestingController;

   beforeEach(() => {
      TestBed.configureTestingModule({
         imports: [HttpClientTestingModule],
         providers: [RateService]
      });
      service = TestBed.inject(RateService);
      httpMock = TestBed.inject(HttpTestingController);
   });

   afterEach(() => {
      httpMock.verify();
   });

   it('should be created', () => {
      expect(service).toBeTruthy();
   });

   it('should map DTO to local Rate correctly', () => {
      const mockRateDTO: RateDTO = {
         id: '1',
         name: 'Test Rate',
         activeRanges: [
            {
               dateRange: {
                  dateStart: '1640995200000', // 2022-01-01
                  dateEnd: '1672531200000'    // 2023-01-01
               },
               weekDays: [DayOfWeek.monday, DayOfWeek.tuesday]
            }
         ],
         reservationSources: [],
         entries: [
            {
               bundleId: 'bundle1',
               bundleRate: {
                  price: {
                     amount: 100,
                     currency: Currency.eur
                  }
               }
            }
         ]
      };

      service.get('1').subscribe(rate => {
         expect(rate.id).toBe('1');
         expect(rate.name).toBe('Test Rate');
         expect(rate.activeRanges).toHaveSize(1);
         expect(rate.activeRanges[0].dateRange.start).toBeInstanceOf(DateTime);
         expect(rate.activeRanges[0].dateRange.end).toBeInstanceOf(DateTime);
         expect(rate.activeRanges[0].weekDays).toEqual([DayOfWeek.monday, DayOfWeek.tuesday]);
         expect(rate.entries).toHaveSize(1);
         expect(rate.entries[0].bundleId).toBe('bundle1');
         expect(rate.entries[0].bundleRate.price.amount).toBe(100);
      });

      const req = httpMock.expectOne('http://localhost:8080/api/rate/1');
      expect(req.request.method).toBe('GET');
      req.flush(mockRateDTO);
   });

   it('should map local Rate to DTO correctly', () => {
      const mockRate: Rate = {
         id: '1',
         name: 'Test Rate',
         activeRanges: [
            {
               dateRange: {
                  start: DateTime.fromMillis(1640995200000), // 2022-01-01
                  end: DateTime.fromMillis(1672531200000)    // 2023-01-01
               },
               weekDays: [DayOfWeek.monday, DayOfWeek.tuesday]
            }
         ],
         reservationSources: [],
         entries: [
            {
               bundleId: 'bundle1',
               bundleRate: {
                  price: {
                     amount: 100,
                     currency: Currency.eur
                  }
               }
            }
         ]
      };

      service.update(mockRate).subscribe();

      const req = httpMock.expectOne('http://localhost:8080/api/rate/1');
      expect(req.request.method).toBe('PUT');

      const sentData = req.request.body;
      expect(sentData.activeRanges[0].dateRange.dateStart).toBe('1640995200000');
      expect(sentData.activeRanges[0].dateRange.dateEnd).toBe('1672531200000');
      expect(sentData.activeRanges[0].weekDays).toEqual([DayOfWeek.monday, DayOfWeek.tuesday]);

      req.flush(null);
   });

   it('should get all rates', () => {
      const mockRatesDTO: RateDTO[] = [
         {
            id: '1',
            name: 'Rate 1',
            activeRanges: [],
            entries: []
         },
         {
            id: '2',
            name: 'Rate 2',
            activeRanges: [],
            entries: []
         }
      ];

      service.getAll().subscribe(rates => {
         expect(rates).toHaveSize(2);
         expect(rates[0].name).toBe('Rate 1');
         expect(rates[1].name).toBe('Rate 2');
      });

      const req = httpMock.expectOne('http://localhost:8080/api/rate');
      expect(req.request.method).toBe('GET');
      req.flush(mockRatesDTO);
   });
});

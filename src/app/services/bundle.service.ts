import {inject, Injectable} from '@angular/core';
import {Bundle, ConsumptionRule} from '../data/bundles/bundle';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {forkJoin, Observable} from 'rxjs';
import {AccommodationCondition} from '../data/customers/customer-discount';
import {DtoCache} from '../utility/dto-cache';
import {map} from 'rxjs/operators';
import {Consumable} from '../data/bundles/consumable';
import {HttpClient} from '@angular/common/http';
import {ConsumableService} from './consumable.service';
import {VATGroupService} from './vat-group.service';
import {ConsumptionRuleService} from './consumption-rule.service';
import {PricingService} from './pricing.service';

interface BundleDTO extends Identifiable {
   name: string;
   consumableRules: Record<ID, ID>;
   vatId: ID;
   fiscalName: string;
   activePricings: ID[];
   accommodationConditions: AccommodationCondition[];
}

@Injectable({
   providedIn: 'root'
})
export class BundleService extends DtoCache<Bundle, BundleDTO> {
   private sConsumable = inject(ConsumableService);
   private sVat = inject(VATGroupService);
   private sConsumptionRule = inject(ConsumptionRuleService);
   private sPricing = inject(PricingService);

   constructor(http: HttpClient) {
      super('bundle', http);
   }

   protected mapToLocal({
                           consumableRules,
                           vatId,
                           activePricings,
                           ...rest
                        }: BundleDTO): Observable<Bundle> {
      return forkJoin({
         consumables: this.sConsumable.getAll(),
         rules: this.sConsumptionRule.getAll(),
         vatGroup: this.sVat.get(vatId),
         allPricings: this.sPricing.getAll()
      }).pipe(map(result => ({
         ...rest,
         vatGroup: result.vatGroup,
         consumableRules: Object.entries(consumableRules)
            .map(([consumableId, ruleId]) => ({
               consumable: result.consumables.find(
                  c => c.id === consumableId) as Consumable,
               rule: result.rules.find(r => r.id === ruleId) as ConsumptionRule
            })),
         activePricings: result.allPricings.filter(p => activePricings.includes(p.id))
      })));
   }

   protected mapToRemote(bundle: MaybeID<Bundle>): MaybeID<BundleDTO> {
      const {consumableRules, vatGroup, activePricings, ...rest} = bundle;

      const consumableAmountsDTO: Record<ID, ID> = {};
      for (const {consumable, rule} of consumableRules) {
         consumableAmountsDTO[consumable.id] = rule.id;
      }

      return {
         ...rest,
         consumableRules: consumableAmountsDTO,
         vatId: vatGroup.id,
         activePricings: activePricings.map(p => p.id),
      };
   }
}

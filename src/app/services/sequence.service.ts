import {inject, Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {serverUrl} from '../utility/http-utility';
import {ID} from '../data/identifiable';
import {Observable} from 'rxjs';

@Injectable({providedIn: 'root'})
export class SequenceService {
   private url = serverUrl('sequence');
   private http = inject(HttpClient);

   increase(sequenceId: ID, value: number): Observable<void> {
      return this.http.post<void>(`${this.url}/${sequenceId}/increase`, {value});
   }
}

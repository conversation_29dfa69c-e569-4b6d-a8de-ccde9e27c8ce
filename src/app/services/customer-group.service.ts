import {CustomerGroup} from '../data/customers/customer-group';
import {Injectable} from '@angular/core';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {Observable, of} from 'rxjs';
import {MaybeID} from '../data/identifiable';

@Injectable({
   providedIn: 'root'
})
export class CustomerGroupService extends DtoCache<CustomerGroup> {
   constructor(http: HttpClient) {
      super('customer-group', http);
   }

   protected mapToLocal(dto: CustomerGroup): Observable<CustomerGroup> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<CustomerGroup>): MaybeID<CustomerGroup> {
      return entry;
   }
}

import {Injectable} from '@angular/core';
import {Consumable, ConsumableType} from '../data/bundles/consumable';
import {Observable, of} from 'rxjs';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {map} from 'rxjs/operators';
import {MaybeID} from '../data/identifiable';

@Injectable({
   providedIn: 'root'
})
export class ConsumableService extends DtoCache<Consumable> {
   constructor(http: HttpClient) {
      super('consumable', http);
   }

   getAllOfType(type: ConsumableType): Observable<Consumable[]> {
      return this.getAll().pipe(
         map(cs => cs.filter(c => c.type === type)),
      );
   }

   protected mapToLocal(dto: Consumable): Observable<Consumable> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<Consumable>): MaybeID<Consumable> {
      return entry;
   }
}

import {inject, Injectable} from '@angular/core';
import {MessageContent, Offer} from '../data/offer';
import {forkJoin, Observable, switchMap} from 'rxjs';
import {DataService} from './data-service';
import {ID, Identifiable, MaybeID, NoID} from '../data/identifiable';
import {Bundle} from '../data/bundles/bundle';
import {Room} from '../data/room';
import {addId, serverUrl} from '../utility/http-utility';
import {CustomerService} from './customer.service';
import {BundleService} from './bundle.service';
import {RoomService} from './room.service';
import {ReservationSourceService} from './reservation-source.service';
import {HttpClient} from '@angular/common/http';
import {defaultIfEmpty, map, mergeMap} from 'rxjs/operators';
import {DateTime} from 'luxon';
import {filterProperties} from '../utility/utility';
import {GuestGroupCount} from '../data/reservation';
import {Money} from '../data/common';
import {NotesService} from './notes.service';
import {Customer} from '../data/customers/customer';

interface OfferDTO extends Identifiable {
   titular: Customer;
   guestGroupCount: GuestGroupCount;
   bundle: ID;
   start: string;
   end: string;
   price: Money;
   isConfirmed: boolean;
   source: ID;
   manualPrice?: Money;
}

@Injectable({
   providedIn: 'root'
})
export class OfferService extends DataService<Offer> {
   private baseUrl = serverUrl('offer');
   private idUrl = addId.bind(null, this.baseUrl);

   private sCustomer = inject(CustomerService);
   private sBundle = inject(BundleService);
   private sRoom = inject(RoomService);
   private sReservationSource = inject(ReservationSourceService);
   private sNotes = inject(NotesService);
   private http = inject(HttpClient);

   override getAll(): Observable<Offer[]> {
      return this.http.get<OfferDTO[]>(`${this.baseUrl}`).pipe(
         mergeMap(offers => forkJoin(offers.map(offer => this.mapToLocal(offer)))),
         defaultIfEmpty([]),
      );
   }

   override get(id: ID): Observable<Offer> {
      return this.http.get<OfferDTO>(this.idUrl(id)).pipe(
         mergeMap(r => this.mapToLocal(r)),
      );
   }

   override add(newItem: NoID<Offer>): Observable<Offer> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newItem)).pipe(
         map(id => ({...newItem, id})),
      );
   }

   override update(newItem: Offer): Observable<void> {
      return this.http.put<void>(this.idUrl(newItem.id), this.mapToRemote(newItem));
   }

   override delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id));
   }

   confirmOffer(id: ID, reservationId: ID): Observable<void> {
      return this.http.post<void>(`${this.idUrl(id)}/confirm`, {reservationId});
   }

   getMessage(id: ID): Observable<MessageContent> {
      return this.http.get<MessageContent>(`${this.idUrl(id)}/message`);
   }

   getAvailableBundles(offer: Partial<Offer>): Observable<Bundle[]> {
      const url = `${this.baseUrl}/available-bundles`;
      return this.http.post<ID[]>(url, this.mapToRemote(offer)).pipe(
         mergeMap(ids => forkJoin(ids.map(id => this.sBundle.get(id)))),
         defaultIfEmpty([]),
      );
   }

   search(query: string): Observable<Offer[]> {
      const params = {query};
      return this.http.get<OfferDTO[]>(`${this.baseUrl}/search`, {params}).pipe(
         switchMap(os => forkJoin(os.map(o => this.mapToLocal(o)))),
         defaultIfEmpty([]),
      );
   }

   getAvailableRooms(offer: Pick<Offer, 'start' | 'end'>): Observable<Room[]> {
      const url = `${this.baseUrl}/available-rooms`;
      return this.http.post<ID[]>(url, offer).pipe(
         mergeMap(ids => forkJoin(ids.map(id => this.sRoom.get(id)))),
         defaultIfEmpty([]),
      );
   }

   private mapToLocal(offer: OfferDTO): Observable<Offer> {
      const {bundle, start, end, source, ...rest} = offer;

      return forkJoin({
         bundle: this.sBundle.get(bundle),
         source: this.sReservationSource.get(source),
         notes: this.sNotes.getByParent(offer.id)
      }).pipe(
         map(({bundle: b, source: s, notes}) => ({
            bundle: b,
            start: DateTime.fromMillis(parseInt(start, 10)).setLocale('bg'),
            end: DateTime.fromMillis(parseInt(end, 10)).setLocale('bg'),
            source: s,
            notes,
            ...rest
         })),
      );
   }

   private mapToRemote(offer: MaybeID<Partial<Offer>>): MaybeID<Partial<OfferDTO>> {
      const {guestGroupCount, bundle, start, end, source, ...rest} = offer;

      const ggc: any = guestGroupCount ?
         filterProperties(guestGroupCount, ([_, count]) => count > 0) : undefined;

      return {
         guestGroupCount: ggc,
         bundle: bundle?.id,
         start: start?.toMillis().toString(),
         end: end?.toMillis().toString(),
         source: source?.id,
         ...rest
      };
   }
}

import {inject, Injectable} from '@angular/core';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ID, NoID} from '../data/identifiable';
import {Payment} from '../data/payment';
import {FiscalPrinter, FiscalPrinterCreateRequest, Receipt} from '../data/fiscal/fiscal';
import {defaultIfEmpty} from 'rxjs/operators';
import {FPAReceiptResponse} from '../data/fiscal/fiscal-agent';

@Injectable({
   providedIn: 'root'
})
export class FiscalService {
   currentDeviceId: ID = '';

   private printerUrl = serverUrl('fiscal-printer');
   private receiptUrl = `${this.printerUrl}/receipt`;

   private idPrinterUrl = addId.bind(null, this.printerUrl);
   private idReceiptUrl = addId.bind(null, this.receiptUrl);

   private http = inject(HttpClient);

   getAll(): Observable<FiscalPrinter[]> {
      return this.http.get<FiscalPrinter[]>(this.printerUrl).pipe(defaultIfEmpty([]));
   }

   create(printer: NoID<FiscalPrinterCreateRequest>): Observable<ID> {
      return this.http.post<ID>(this.printerUrl, printer);
   }

   update(printer: FiscalPrinterCreateRequest): Observable<void> {
      return this.http.put<void>(`${this.printerUrl}/${printer.id}`, printer);
   }

   getNextReceipt(payment: NoID<Payment>, purchases?: ID[]): Observable<Receipt> {
      const url = `${this.idPrinterUrl(this.currentDeviceId)}/next-receipt`;
      const body = {payment, purchases};

      return this.http.post<Receipt>(url, body);
   }

   getReceipt(id: ID): Observable<Receipt> {
      return this.http.get<Receipt>(this.idReceiptUrl(id));
   }

   successReceipt(id: ID,
      fpResponse: FPAReceiptResponse): Observable<void> {
      const url = `${this.idReceiptUrl(id)}/success`;
      return this.http.post<void>(url, fpResponse);
   }

   failReceipt(id: ID): Observable<void> {
      const url = `${this.idReceiptUrl(id)}/fail`;
      return this.http.post<void>(url, null);
   }
}

import {inject, Injectable} from '@angular/core';
import {Purchase} from '../data/purchase';
import {forkJoin, Observable, of} from 'rxjs';
import {ID, Identifiable, MaybeID, NoID} from '../data/identifiable';
import {Money} from '../data/common';
import {addId, serverUrl} from '../utility/http-utility';
import {BundleService} from './bundle.service';
import {ReservationService} from './reservation.service';
import {HttpClient} from '@angular/common/http';
import {defaultIfEmpty, map, mergeMap} from 'rxjs/operators';

interface PurchaseDTO extends Identifiable {
   account: ID;
   bundle: ID;
   quantity: number;
   price: Money;
}

interface IncomingPurchaseDTO extends PurchaseDTO {
   isManual: boolean;
   originalReservation?: ID;
}

@Injectable({
   providedIn: 'root'
})
export class PurchaseService {
   private baseUrl = serverUrl('purchase');
   private idUrl = addId.bind(null, this.baseUrl);

   private sBundle = inject(BundleService);
   private sReservation = inject(ReservationService);
   private http = inject(HttpClient);

   get(id: ID): Observable<Purchase> {
      return this.http.get<IncomingPurchaseDTO>(this.idUrl(id)).pipe(
         mergeMap(p => this.mapToLocal(p)),
      );
   }

   add(newItem: NoID<Purchase>): Observable<Purchase> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newItem)).pipe(
         map(id => ({...newItem, id})),
      );
   }

   update(purchase: Purchase): Observable<void> {
      return this.http.put<void>(this.idUrl(purchase.id), this.mapToRemote(purchase));
   }

   delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id));
   }

   getByAccount(accountId: ID): Observable<Purchase[]> {
      const params = {accountId};
      return this.http.get<IncomingPurchaseDTO[]>(this.baseUrl, {params}).pipe(
         mergeMap(ps => forkJoin(ps.map(p => this.mapToLocal(p)))),
         defaultIfEmpty([]),
      );
   }

   moveToStandalone(purchases: ID[], accountId: ID): Observable<void> {
      if (purchases.length === 0) {
         return of(undefined);
      }

      const url = `${this.baseUrl}/move-to-standalone`;
      const params = {accountId};
      return this.http.post<void>(url, purchases, {params});
   }

   moveToReservation(purchases: ID[], reservationId: ID): Observable<void> {
      if (purchases.length === 0) {
         return of(undefined);
      }

      const url = `${this.baseUrl}/move-to-reservation`;
      const params = {reservationId};
      return this.http.post<void>(url, purchases, {params});
   }

   getPrice(bundle: ID, quantity: number): Observable<Money> {
      const params = {bundleId: bundle, quantity: quantity.toString()};
      return this.http.get<Money>(`${this.baseUrl}/price`, {params});
   }

   private mapToLocal({
                         bundle: b,
                         originalReservation: or,
                         ...rest
                      }: IncomingPurchaseDTO): Observable<Purchase> {
      return forkJoin([
         this.sBundle.get(b),
         or ? this.sReservation.getInfo(or) : of(undefined)
      ]).pipe(map(([bb, oor]) => ({bundle: bb, originalReservation: oor, ...rest})));
   }

   private mapToRemote(purchase: MaybeID<Purchase>): MaybeID<PurchaseDTO> {
      const {bundle, ...rest} = purchase;
      return {bundle: bundle.id, ...rest};
   }
}

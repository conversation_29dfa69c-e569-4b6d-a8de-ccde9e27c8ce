import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable, of} from 'rxjs';
import {Identifiable, MaybeID} from '../data/identifiable';
import {AppLocalizedString} from '../data/common';
import {DtoCache} from '../utility/dto-cache';
import {SurveyQuestion} from '../data/survey';

export interface SurveyQuestionDTO extends Identifiable {
   index: number;
   question: AppLocalizedString;
   icon: string;
}

@Injectable({
   providedIn: 'root'
})
export class SurveyQuestionService extends DtoCache<SurveyQuestion, SurveyQuestionDTO> {
   constructor(protected http: HttpClient) {
      super('survey/question', http);
   }

   protected mapToLocal(dto: SurveyQuestionDTO): Observable<SurveyQuestion> {
      const {question, ...rest} = dto;
      return of({question: question['bg'], ...rest});
   }

   protected mapToRemote(_: MaybeID<SurveyQuestion>): MaybeID<SurveyQuestionDTO> {
      throw new Error('Not implemented');
   }
}

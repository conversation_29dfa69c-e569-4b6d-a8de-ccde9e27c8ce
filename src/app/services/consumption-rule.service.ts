import {inject, Injectable} from '@angular/core';
import {ConsumptionRule} from '../data/bundles/bundle';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {CustomerGroupService} from './customer-group.service';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {CustomerGroup} from '../data/customers/customer-group';

export interface ConsumptionRuleDTO extends Identifiable {
   name: string;
   appliesPerCustomer: boolean;
   appliesForRegularBed: boolean;
   appliesForAdditionalBed: boolean;
   applicableCustomerGroups: ID[];
}

@Injectable({
   providedIn: 'root'
})
export class ConsumptionRuleService
   extends DtoCache<ConsumptionRule, ConsumptionRuleDTO> {

   private sCustomerGroup = inject(CustomerGroupService);

   constructor() {
      super('consumption-rule', inject(HttpClient));
   }

   protected mapToLocal(rule: ConsumptionRuleDTO): Observable<ConsumptionRule> {
      const {applicableCustomerGroups, ...rest} = rule;

      return this.sCustomerGroup.getAll().pipe(
         map(groups => ({
            applicableCustomerGroups: groups.filter(
               group => applicableCustomerGroups.includes(group.id)),
            ...rest
         }))
      );
   }

   protected mapToRemote(rule: any): MaybeID<ConsumptionRuleDTO> {
      const {
         applicableCustomerGroups,
         ...rest
      } = rule;

      const groupIds = applicableCustomerGroups.map((g: CustomerGroup) => g.id);
      return {applicableCustomerGroups: groupIds, ...rest};
   }
}

import {inject, Injectable} from '@angular/core';
import {
   GuestGroupCount,
   Reservation,
   ReservationHistory,
   ReservationInfo,
   ReservationStatus
} from '../data/reservation';
import {ID, Identifiable, NoID} from '../data/identifiable';
import {forkJoin, Observable, of, switchMap, throwError} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {Money} from '../data/common';
import {addId, serverUrl} from '../utility/http-utility';
import {RoomService} from './room.service';
import {CustomerService} from './customer.service';
import {BundleService} from './bundle.service';
import {defaultIfEmpty, map, mergeMap} from 'rxjs/operators';
import {DateTime} from 'luxon';
import {Bundle} from '../data/bundles/bundle';
import {filterProperties} from '../utility/utility';
import {Room} from '../data/room';
import {ReservationSourceService} from './reservation-source.service';
import {FinancialAccountService} from './financial-account.service';
import {Voucher} from '../data/voucher';
import {DataService} from './data-service';
import {NotesService} from './notes.service';
import {Note} from '../data/notes';
import {SurveyQuestionService} from './survey-question.service';
import {Customer} from '../data/customers/customer';

export interface CreateGroupReservation {
   reservations: NoID<Reservation>[];
   titular: ID;
   sendInvitation: boolean;
   purchasesRoom?: ID;
   notes?: string[];
}

interface VoucherDiscountDTO {
   voucher: ID;
   identifier: string;
}

interface ReservationDTORemote extends Identifiable {
   room: ID;
   titular: Customer;
   guests: ID[];
   guestGroupCount: GuestGroupCount;
   start: string;
   end: string;
   bundle: ID;
   manualPrice?: Money;
   status: ReservationStatus;
   source: ID;
   color?: string;
   isShadow: boolean;
   invoiceReceiver?: ID;
   isLeisure: boolean;
   voucherDiscount?: VoucherDiscountDTO;
   surveyAnswers?: SurveyAnswersDTO;
   otaIdentifier?: string;
}

interface ReservationDTO extends ReservationDTORemote {
   serialNumber: number;
   accounts: ID[];
   activeAccount: ID;
   balance: Money;
}

interface ReservationInfoDTO extends Identifiable {
   serialNumber: number;
   room: ID;
   titular: ID;
   start: string;
   end: string;
   status: ReservationStatus;
   color?: string;
   isLeisure?: boolean;
}

interface ReservationHistoryDTO extends Identifiable {
   operator: string;
   titular: string;
   room: string;
   bundle: string;
   start: string;
   end: string;
   status: ReservationStatus;
   timestamp: string;
   manualPrice?: Money;
}

export interface SurveyAnswersDTO {
   answers: Record<ID, number>;
   average: number;
   comments?: string;
}

@Injectable({
   providedIn: 'root'
})
export class ReservationService extends DataService<Reservation> {
   baseUrl = serverUrl('reservation');
   groupUrl = serverUrl('group-reservation');
   idUrl = addId.bind(null, this.baseUrl);

   private sRoom = inject(RoomService);
   private sCustomer = inject(CustomerService);
   private sBundle = inject(BundleService);
   private sReservationSource = inject(ReservationSourceService);
   private sFinancialAccount = inject(FinancialAccountService);
   private sNotes = inject(NotesService);
   private sSurveyQuestion = inject(SurveyQuestionService);
   private http = inject(HttpClient);

   override getAll(): Observable<Reservation[]> {
      return throwError(() => new Error('Not supported'));
   }

   override get(id: ID): Observable<Reservation> {
      return this.http.get<ReservationDTO>(this.idUrl(id)).pipe(
         mergeMap(r => this.mapToLocal(r)),
      );
   }

   override add(newItem: NoID<Reservation>): Observable<Reservation> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newItem)).pipe(
         map(id => ({...newItem, id})),
      );
   }

   override update(newItem: Reservation): Observable<void> {
      return this.http.put<void>(this.idUrl(newItem.id), this.mapToRemote(newItem));
   }

   override delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id));
   }

   getInRange(start: DateTime, end: DateTime): Observable<ReservationInfo[]> {
      const params = {from: start.toMillis(), to: end.toMillis()};
      return this.http.get<ReservationInfoDTO[]>(this.baseUrl, {params}).pipe(
         mergeMap(ris => forkJoin(ris.map(ri => this.mapInfoToLocal(ri)))),
         defaultIfEmpty([]),
      );
   }

   getInfo(id: ID): Observable<ReservationInfo> {
      return this.http.get<ReservationInfoDTO>(`${this.idUrl(id)}/info`).pipe(
         mergeMap(ri => this.mapInfoToLocal(ri))
      );
   }

   getCustomerReservations(customerId: ID): Observable<number> {
      const params = {customerId};
      const url = `${this.baseUrl}/reservation-count`;
      return this.http.get<number>(url, {params});
   }

   getPrice(reservation: Partial<Reservation>): Observable<Money> {
      const url = `${this.baseUrl}/price?currency=BGN`;

      return this.http.post<Money>(url, this.mapToRemote(reservation));
   }

   getAvailableBundles(reservation: Partial<Reservation>): Observable<Bundle[]> {
      const url = `${this.baseUrl}/available-bundles`;

      // TODO(vlado): Fix the logic in the reservation form sending wrong dates for
      //              leisure reservations
      if (reservation.start && reservation.end && reservation.start > reservation.end) {
         const start = reservation.start;
         reservation.start = reservation.end;
         reservation.end = start;
      }

      return this.http.post<ID[]>(url, this.mapToRemote(reservation)).pipe(
         mergeMap(ids => forkJoin(ids.map(id => this.sBundle.get(id)))),
         defaultIfEmpty([]),
      );
   }

   findAll(query: string, includePast: boolean): Observable<ReservationInfo[]> {
      const url = `${this.baseUrl}/search`;
      const params = {query, includePast};
      return this.http.get<ReservationInfoDTO[]>(url, {params}).pipe(
         mergeMap(rs => forkJoin(rs.map(r => this.mapInfoToLocal(r)))),
         defaultIfEmpty([]),
      );
   }

   patch(reservation: Partial<Reservation>): Observable<void> {
      if (reservation.id) {
         const url = this.idUrl(reservation.id);
         return this.http.patch<void>(url, this.mapToRemote(reservation));
      }

      return throwError(() => new Error('No reservation ID!'));
   }

   getAvailableRooms(reservation: Pick<Reservation, 'start' | 'end'>): Observable<Room[]> {
      const url = `${this.baseUrl}/available-rooms`;
      return this.http.post<ID[]>(url, this.mapToRemote(reservation)).pipe(
         mergeMap(ids => forkJoin(ids.map(id => this.sRoom.get(id)))),
         defaultIfEmpty([]),
      );
   }

   getVouchers(reservation: Partial<Reservation>): Observable<Voucher[]> {
      const url = `${this.baseUrl}/vouchers`;
      return this.http.post<Voucher[]>(url, this.mapToRemote(reservation));
   }

   createGroup({reservations, ...r}: CreateGroupReservation): Observable<ID> {
      const request = {reservations: reservations.map(this.mapToRemote), ...r};
      return this.http.post<ID>(this.groupUrl, request);
   }

   getTotalPurchasesInRange(from: DateTime,
      to: DateTime): Observable<Record<ID, number>> {
      const params = {from: from.toMillis(), to: to.toMillis()};
      const url = `${this.baseUrl}/total-purchases`;

      return this.http.get<Record<ID, number>>(url, {params});
   }

   getTotalPaymentsInRange(from: DateTime,
      to: DateTime): Observable<Record<ID, number>> {
      const params = {from: from.toMillis(), to: to.toMillis()};
      const url = `${this.baseUrl}/total-payments`;

      return this.http.get<Record<ID, number>>(url, {params});
   }

   getNotesInRange(from: DateTime, to: DateTime): Observable<Record<ID, Note[]>> {
      const params = {from: from.toMillis(), to: to.toMillis()};
      const url = `${this.baseUrl}/notes`;

      return this.http.get<Record<ID, Note[]>>(url, {params});
   }

   getHistory(reservationId: ID): Observable<ReservationHistory[]> {
      const url = `${this.baseUrl}/history`;
      const params = {reservationId};

      return this.http.get<ReservationHistoryDTO[]>(url, {params}).pipe(
         map(hs => hs.map(h => this.mapHistoryToLocal(h))),
         defaultIfEmpty([]),
      );
   }

   swapRooms(newRooms: Record<ID, ID>): Observable<void> {
      return this.http.post<void>(`${this.baseUrl}/swap-rooms`, newRooms);
   }

   validateRoomSwap(newRooms: Record<ID, ID>): Observable<ID[]> {
      return this.http.post<ID[]>(`${this.baseUrl}/validate-room-swap`, newRooms);
   }

   createDraft(): Observable<ID> {
      return this.http.post<ID>(serverUrl('draft'), {});
   }

   deleteDraft(draftId: ID): Observable<void> {
      return this.http.delete<void>(`${serverUrl('draft')}/${draftId}`);
   }

   inviteToGuestApp(id: ID): Observable<void> {
      return this.http.post<void>(`${this.idUrl(id)}/invite-to-guest-app`, undefined);
   }

   getAvgScoresInRange(from: DateTime, to: DateTime): Observable<Record<ID, number>> {
      const url = `${this.baseUrl}/average-scores`;
      const params = {from: from.toMillis(), to: to.toMillis()};

      return this.http.get<Record<ID, number>>(url, {params});
   }

   cancel(reservationId: ID): Observable<ID> {
      return this.http.post<ID>(`${this.baseUrl}/${reservationId}/cancellation`, null);
   }

   private mapToRemote(reservation: Partial<Reservation>): Partial<ReservationDTORemote> {
      const {
         room,
         guests,
         accounts: _,
         guestGroupCount,
         bundle,
         start,
         end,
         source,
         invoiceReceiver,
         voucherDiscount,
         notes: __,
         ...rest
      } = reservation;

      const ggc: any = guestGroupCount ?
         filterProperties(guestGroupCount, ([_, count]) => count > 0) : undefined;

      let remoteVoucherDiscount;
      if (voucherDiscount) {
         remoteVoucherDiscount = {
            voucher: voucherDiscount.voucher.id,
            identifier: voucherDiscount.identifier
         };
      }

      return {
         room: room?.id,
         guests: guests?.map(c => c.id),
         guestGroupCount: ggc,
         bundle: bundle?.id,
         start: start?.toMillis().toString(),
         end: end?.toMillis().toString(),
         source: source?.id,
         invoiceReceiver: invoiceReceiver?.id,
         voucherDiscount: remoteVoucherDiscount,
         ...rest
      };
   }

   private mapInfoToLocal(info: ReservationInfoDTO): Observable<ReservationInfo> {
      const {room: r, titular: t, start, end, ...rest} = info;
      return forkJoin({
         room: this.sRoom.get(r),
         titular: this.sCustomer.get(t)
      }).pipe(
         map(result => ({
            start: DateTime.fromMillis(parseInt(start, 10)).setLocale('bg'),
            end: DateTime.fromMillis(parseInt(end, 10)).setLocale('bg'),
            ...rest,
            ...result
         })),
      );
   }

   private mapHistoryToLocal(history: ReservationHistoryDTO): ReservationHistory {
      const {
         start: s,
         end: e,
         timestamp: ts,
         ...rest
      } = history;

      return {
         start: DateTime.fromMillis(parseInt(s, 10)).setLocale('bg'),
         end: DateTime.fromMillis(parseInt(e, 10)).setLocale('bg'),
         timestamp: DateTime.fromMillis(parseInt(ts, 10)).setLocale('bg'),
         ...rest
      };
   }

   private mapToLocal(reservation: ReservationDTO): Observable<Reservation> {
      const {
         room,
         guests,
         accounts,
         activeAccount,
         bundle,
         start,
         end,
         source,
         invoiceReceiver: ir,
         voucherDiscount,
         surveyAnswers: sa,
         ...rest
      } = reservation;

      return forkJoin({
         room: this.sRoom.get(room),
         guests: guests.length === 0 ? of([]) :
            forkJoin(guests.map(c => this.sCustomer.get(c))),
         accounts: forkJoin(accounts.map(a => this.sFinancialAccount.get(a))),
         activeAccount: this.sFinancialAccount.get(activeAccount),
         bundle: this.sBundle.get(bundle),
         source: this.sReservationSource.get(source),
         invoiceReceiver: ir ? this.sCustomer.get(ir) : of(undefined),
         notes: this.sNotes.getByParent(reservation.id),
         questions: this.sSurveyQuestion.getAll(),
      }).pipe(
         map(({questions, ...result}) => {
            let surveyAnswers;
            if (sa) {
               const {answers, ...r} = sa;
               surveyAnswers = {
                  answers: Object.entries(answers).reduce((obj, [qId, score]) => {
                     const question = questions.find(q => q.id === qId);
                     if (question) {
                        obj[question.question] = score;
                     }
                     return obj;
                  }, {} as Record<string, number>),
                  ...r,
               };
            }
            return {
               ...result,
               surveyAnswers,
               start: DateTime.fromMillis(parseInt(start, 10)).setLocale('bg'),
               end: DateTime.fromMillis(parseInt(end, 10)).setLocale('bg'),
               ...rest
            };
         }),
         switchMap(result => {
            if (voucherDiscount) {
               return this.getVouchers(result).pipe(map(vouchers => {
                  const voucher = vouchers.find(v => v.id === voucherDiscount.voucher);
                  return {
                     voucherDiscount: {
                        voucher,
                        identifier: voucherDiscount.identifier
                     },
                     ...result
                  };
               }));
            }
            return of(result);
         })
      );
   }
}

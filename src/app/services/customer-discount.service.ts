import {inject, Injectable} from '@angular/core';
import {
   AccommodationCondition,
   CustomerDiscount
} from '../data/customers/customer-discount';
import {DtoCache} from '../utility/dto-cache';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {Discount} from '../utility/discount';
import {PricingService} from './pricing.service';
import {ConsumableService} from './consumable.service';
import {CustomerGroupService} from './customer-group.service';
import {HttpClient} from '@angular/common/http';
import {forkJoin, Observable} from 'rxjs';
import {map} from 'rxjs/operators';

export interface CustomerDiscountDTO extends Identifiable {
   name: string;
   discount: Discount;
   accommodationCondition?: AccommodationCondition;
   activePricings?: ID[];
   applicableConsumables?: ID[];
   applicableCustomerGroups?: ID[];
}

@Injectable({
   providedIn: 'root'
})
export class CustomerDiscountService
   extends DtoCache<CustomerDiscount, CustomerDiscountDTO> {
   private sPricing = inject(PricingService);
   private sConsumable = inject(ConsumableService);
   private sCustomerGroup = inject(CustomerGroupService);

   constructor(http: HttpClient) {
      super('customer-discount', http);
   }

   protected mapToLocal(discountDTO: CustomerDiscountDTO): Observable<CustomerDiscount> {
      return forkJoin({
         pricings: this.sPricing.getAll(),
         consumables: this.sConsumable.getAll(),
         customerGroups: this.sCustomerGroup.getAll()
      }).pipe(map(({pricings, consumables, customerGroups}) => {
         const {
            activePricings,
            applicableConsumables,
            applicableCustomerGroups,
            ...baseDiscount
         } = discountDTO;

         const result: any = {
            activePricings: activePricings?.map(id => pricings.find(p => p.id === id)),
            ...baseDiscount
         };

         if (applicableConsumables) {
            result.applicableConsumables = applicableConsumables.map(
               id => consumables.find(c => c.id === id));
         }

         if (applicableCustomerGroups) {
            result.applicableCustomerGroups = applicableCustomerGroups.map(
               id => customerGroups.find(g => g.id === id));
         }

         return result;
      }));
   }

   protected mapToRemote(discount: MaybeID<CustomerDiscount>): MaybeID<CustomerDiscountDTO> {
      const {
         activePricings,
         applicableConsumables,
         applicableCustomerGroups,
         ...baseDiscount
      } = discount;
      const pricingIds = activePricings?.map(p => p.id);
      return {
         activePricings: pricingIds,
         applicableConsumables: applicableConsumables?.map(c => c.id),
         applicableCustomerGroups: applicableCustomerGroups?.map(cg => cg.id),
         ...baseDiscount
      };
   }
}

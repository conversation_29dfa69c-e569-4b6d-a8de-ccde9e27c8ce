import {inject, Injectable} from '@angular/core';
import {ID, Identifiable} from '../data/identifiable';
import {forkJoin, mergeMap, Observable} from 'rxjs';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {defaultIfEmpty, map} from 'rxjs/operators';
import {DateTime} from 'luxon';
import {Cancellation} from '../data/cancellation';
import {GuestGroupCount} from '../data/reservation';
import {BundleService} from './bundle.service';
import {ReservationSourceService} from './reservation-source.service';
import {Customer} from '../data/customers/customer';

interface CancellationDTO extends Identifiable {
   titular: Customer;
   guestGroupCount: GuestGroupCount;
   bundle: ID;
   start: string;
   end: string;
   source: ID;
   activeAccount: ID;
   accounts: ID[];
}

@Injectable({
   providedIn: 'root'
})
export class CancellationService {
   private baseUrl = serverUrl('cancellation');
   private idUrl = addId.bind(null, this.baseUrl);

   private sBundle = inject(BundleService);
   private sReservationSource = inject(ReservationSourceService);
   private http = inject(HttpClient);


   get(id: ID): Observable<Cancellation> {
      return this.http.get<CancellationDTO>(this.idUrl(id)).pipe(
         mergeMap(c => this.mapToLocal(c)),
      );
   }

   getInRange(start: DateTime, end: DateTime): Observable<Cancellation[]> {
      const params = {from: start.toMillis(), to: end.toMillis()};
      return this.http.get<CancellationDTO[]>(this.baseUrl, {params}).pipe(
         mergeMap(cancellations => forkJoin(cancellations.map(c => this.mapToLocal(c)))),
         defaultIfEmpty([]),
      );
   }

   private mapToLocal(dto: CancellationDTO): Observable<Cancellation> {
      const {titular, bundle, source, start, end, ...rest} = dto;
      return forkJoin([
         this.sBundle.get(bundle),
         this.sReservationSource.get(source)
      ]).pipe(map(([b, s]) => {
         return {
            titular,
            bundle: b,
            source: s,
            start: DateTime.fromMillis(parseInt(start, 10)).setLocale('bg'),
            end: DateTime.fromMillis(parseInt(end, 10)).setLocale('bg'),
            ...rest
         } as Cancellation;
      }));
   }
}

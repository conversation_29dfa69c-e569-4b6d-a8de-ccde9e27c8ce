import {inject, Injectable} from '@angular/core';
import {ConsumableService} from './consumable.service';
import {BundleService} from './bundle.service';
import {VATGroupService} from './vat-group.service';
import {CleaningService} from './cleaning.service';
import {AuthService} from '../auth/auth.service';
import {RoomService} from './room.service';
import {forkJoin, mergeMap} from 'rxjs';
import {ReservationSourceService} from './reservation-source.service';
import {PricingService} from './pricing.service';
import {ConsumptionRuleService} from './consumption-rule.service';
import {CustomerGroupService} from './customer-group.service';
import {FeatureService} from './feature.service';
import {LicenseService} from './license.service';
import {AgentService} from './agent.service';
import {SurveyQuestionService} from './survey-question.service';
import {RateService} from './rate.service';

@Injectable({
   providedIn: 'root'
})
export class CachingService {
   private sAuth = inject(AuthService);
   private sFeature = inject(FeatureService);
   private sLicense = inject(LicenseService);
   private sAgent = inject(AgentService);
   private sConsumable = inject(ConsumableService);
   private sBundle = inject(BundleService);
   private sVatGroup = inject(VATGroupService);
   private sCleaning = inject(CleaningService);
   private sReservationSource = inject(ReservationSourceService);
   private sRoom = inject(RoomService);
   private sPricing = inject(PricingService);
   private sConsumptionRule = inject(ConsumptionRuleService);
   private sCustomerGroup = inject(CustomerGroupService);
   private sSurveyQuestion = inject(SurveyQuestionService);
   private sRate = inject(RateService);

   private isInitialized = false;
   private isInitializing = false;

   get initialized(): boolean {
      return this.isInitialized;
   }

   /**
    * Pre-cache the non-volatile elements in the inventory. They are also
    * not that much so holding them in a local cache won't increase memory usage
    * much, but it increases performance a lot.
    */
   init(): void {
      if (!this.sAuth.loggedIn() || this.isInitialized || this.isInitializing) {
         return;
      }

      this.isInitializing = true;

      forkJoin([
         this.sAuth.init(),
         this.sFeature.init(),
         this.sLicense.init(),
         this.sConsumable.getAll(),
         this.sVatGroup.getAll(),
         this.sReservationSource.getAll(),
         this.sPricing.getAll(),
         this.sCustomerGroup.getAll(),
         this.sSurveyQuestion.getAll(),
         this.sRate.getAll(),
      ]).pipe(
         mergeMap(() => forkJoin([
            this.sCleaning.getAll(),
            this.sConsumptionRule.getAll(),
         ])),
         mergeMap(() => forkJoin([
            this.sBundle.getAll(),
            this.sRoom.getAll(),
         ]))
      ).subscribe({
         error: err => console.error('Error during pre-caching operation:', err),
         complete: () => {
            this.sAgent.init();
            this.isInitialized = true;
         },
      });
   }
}

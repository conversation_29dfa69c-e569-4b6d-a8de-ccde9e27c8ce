import {Injectable} from '@angular/core';
import {Message} from '../data/message';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {Observable, of} from 'rxjs';
import {MaybeID} from '../data/identifiable';

@Injectable({
   providedIn: 'root'
})
export class MessageService extends DtoCache<Message> {
   constructor(protected http: HttpClient) {
      super('message', http);
   }

   protected mapToLocal(dto: Message): Observable<Message> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<Message>): MaybeID<Message> {
      return entry;
   }
}

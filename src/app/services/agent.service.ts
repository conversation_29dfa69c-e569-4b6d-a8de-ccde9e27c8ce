import {EventEmitter, inject, Injectable} from '@angular/core';
import {Client} from '@stomp/stompjs';
import {LicenseService} from './license.service';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {IdScannerData} from '../data/agent/id-scanner';

export interface AgentInfo {
   version: string;
   initialized: boolean;
}

@Injectable({providedIn: 'root'})
export class AgentService {
   idScanner = new EventEmitter<IdScannerData>();

   private url = 'http://localhost:8081';
   private ws?: Client;

   private sLicense = inject(LicenseService);
   private http = inject(HttpClient);

   init(): void {
      if (this.sLicense.idScanner()) {
         this.ws = new Client({
            brokerURL: 'ws://localhost:8081/ws',
            onConnect: () => {
               this.ws?.subscribe('/topic/public',
                  msg => this.idScanner.emit(JSON.parse(msg.body)));
            },
         });

         this.ws.activate();
      }
   }

   getInfo(): Observable<AgentInfo> {
      return this.http.get<AgentInfo>(`${this.url}/agent/info`);
   }
}

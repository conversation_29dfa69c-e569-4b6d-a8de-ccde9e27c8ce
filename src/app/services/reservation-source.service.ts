import {Injectable} from '@angular/core';
import {ReservationSource} from 'src/app/data/reservation-source';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {MaybeID, NoID} from '../data/identifiable';
import {Observable, of, tap} from 'rxjs';

@Injectable({
   providedIn: 'root'
})
export class ReservationSourceService extends DtoCache<ReservationSource> {
   constructor(http: HttpClient) {
      super('reservation-source', http);
   }

   override add(newEntry: NoID<ReservationSource>): Observable<ReservationSource> {
      return super.add(newEntry).pipe(tap(() => this.invalidateCache()));
   }

   override update(entry: ReservationSource): Observable<void> {
      return super.update(entry).pipe(tap(() => this.invalidateCache()));
   }

   protected mapToLocal(dto: ReservationSource): Observable<ReservationSource> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<ReservationSource>): MaybeID<ReservationSource> {
      return entry;
   }
}

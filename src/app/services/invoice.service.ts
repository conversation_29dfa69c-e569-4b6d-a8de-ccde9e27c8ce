import {inject, Injectable} from '@angular/core';
import {Document, Invoice} from '../data/invoice';
import {ID, Identifiable, MaybeID, NoID} from '../data/identifiable';
import {
   defaultIfEmpty,
   forkJoin,
   map,
   mergeMap,
   Observable,
   of,
   switchMap,
   throwError
} from 'rxjs';
import {HttpClient, HttpParams} from '@angular/common/http';
import {DtoCache} from '../utility/dto-cache';
import {CustomerService} from './customer.service';
import {InvoiceCategoryService} from './invoice-category.service';
import {OperatorService} from './operator.service';
import {DateTime} from 'luxon';
import {Customer} from '../data/customers/customer';

interface InvoiceDto extends Identifiable {
   categoryId: ID;
   accountId: ID;
   senderId: ID;
   receiver: Customer;
   cashierId?: ID;
   parentInvoiceId?: ID;
   invoiceNumber?: number;
   timestamp?: string;
}

@Injectable({
   providedIn: 'root'
})
export class InvoiceService extends DtoCache<Invoice, InvoiceDto> {

   private sCustomer = inject(CustomerService);
   private sInvoiceCategory = inject(InvoiceCategoryService);
   private sOperator = inject(OperatorService);

   constructor(protected http: HttpClient) {
      super('invoice', http);
   }

   override getAll(): Observable<Invoice[]> {
      return throwError(() => new Error('Not supported'));
   }

   addPartial(newItem: NoID<Invoice>, items: ID[]): Observable<Invoice> {
      const dto = {invoice: this.mapToRemote(newItem), items};
      return this.http.post<ID>(`${this.baseUrl}/partial`, dto).pipe(
         map(id => ({...newItem, id})),
      );
   }

   search(accountId: string, categoryId?: string): Observable<Invoice[]> {
      let params = new HttpParams().set('accountId', accountId);

      if (categoryId) {
         params = params.set('categoryId', categoryId);
      }

      return this.http.get<InvoiceDto[]>(`${this.baseUrl}/search`, {params}).pipe(
         mergeMap(invoices => {
            const fetchedInvoices = invoices.map(i => this.mapToLocal(i));
            return forkJoin(fetchedInvoices);
         }),
         defaultIfEmpty([])
      );
   }

   getIssuer(): Observable<Customer> {
      return this.http.get<ID>(`${this.baseUrl}/issuer`)
         .pipe(switchMap(id => this.sCustomer.get(id)));
   }

   generateDocument(id: ID, isDocumentCopy: boolean): Observable<Document> {
      const params = {isDocumentCopy};
      return this.http.get<Document>(`${this.baseUrl}/document/${id}`, {params});
   }

   getInRange(start: DateTime, end: DateTime): Observable<Invoice[]> {
      const params = {from: start.toMillis(), to: end.toMillis()};
      return this.http.get<InvoiceDto[]>(this.baseUrl, {params}).pipe(
         mergeMap(invoices => forkJoin(invoices.map(c => this.mapToLocal(c)))),
         defaultIfEmpty([]),
      );
   }

   protected mapToLocal(dto: InvoiceDto): Observable<Invoice> {
      const {categoryId, senderId, cashierId, parentInvoiceId, timestamp, ...rest} = dto;
      return forkJoin({
         sender: this.sCustomer.get(senderId),
         cashier: cashierId ? this.sOperator.get(cashierId) : of(undefined),
         category: this.sInvoiceCategory.get(categoryId),
         parentInvoice: parentInvoiceId ? this.get(parentInvoiceId) : of(undefined),
      }).pipe(map(({category, sender, cashier, parentInvoice}) => ({
         category,
         sender,
         cashier,
         parentInvoice,
         timestamp: timestamp ?
            DateTime.fromMillis(parseInt(timestamp, 10)).setLocale('bg') : undefined,
         ...rest
      })));
   }

   protected mapToRemote(entry: MaybeID<Invoice>): MaybeID<InvoiceDto> {
      const {category, sender, cashier: _, parentInvoice: ___, timestamp: __, ...rest} = entry;
      return {
         categoryId: category.id,
         senderId: sender.id,
         ...rest
      } as InvoiceDto;
   }
}

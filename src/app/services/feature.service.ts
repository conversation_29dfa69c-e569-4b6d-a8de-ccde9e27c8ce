import {inject, Injectable, signal} from '@angular/core';
import {serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';

export enum Feature {
   fiscal = 'FISCAL',
}

export enum FeatureStatus {
   enabled = 'ENABLED',
   disabled = 'DISABLED',
}

@Injectable({providedIn: 'root'})
export class FeatureService {
   fiscal = signal(false);

   private url = serverUrl('feature');
   private http = inject(HttpClient);

   enable(feature: Feature): Observable<void> {
      return this.http.post<void>(`${this.url}/${feature}/enable`, undefined);
   }

   disable(feature: Feature): Observable<void> {
      return this.http.post<void>(`${this.url}/${feature}/disable`, undefined);
   }

   init(): Observable<void> {
      return this.http.get<Record<Feature, FeatureStatus>>(this.url).pipe(
         map(fs => this.fiscal.set(fs.FISCAL == FeatureStatus.enabled)),
      );
   }
}

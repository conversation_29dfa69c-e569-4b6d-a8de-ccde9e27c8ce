import {Injectable} from '@angular/core';
import {VATGroup} from 'src/app/data/VATGroup';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {Observable, of} from 'rxjs';
import {MaybeID} from '../data/identifiable';

@Injectable({
   providedIn: 'root'
})
export class VATGroupService extends DtoCache<VATGroup> {
   constructor(http: HttpClient) {
      super('vat', http);
   }

   protected mapToLocal(dto: VATGroup): Observable<VATGroup> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<VATGroup>): MaybeID<VATGroup> {
      return entry;
   }
}

import {inject, Injectable} from '@angular/core';
import {Operator} from 'src/app/data/auth/operator';
import {Observable, of, tap} from 'rxjs';
import {DtoCache} from '../utility/dto-cache';
import {ID, MaybeID} from '../data/identifiable';
import {HttpClient} from '@angular/common/http';
import {AuthToken} from '../data/auth/auth-token-payload';
import {Credentials} from '../data/auth/credentials';

@Injectable({
   providedIn: 'root'
})
export class OperatorService extends DtoCache<Operator> {
   constructor() {
      super('operator', inject(HttpClient));
   }

   getAllUsernames(): Observable<string[]> {
      return this.http.get<string[]>(this.baseUrl + '/usernames');
   }

   login(credentials: Credentials): Observable<AuthToken> {
      return this.http.post<AuthToken>(`${this.baseUrl}/login`, credentials);
   }

   refreshToken(tokenId: string): Observable<AuthToken> {
      const data = {value: tokenId};
      return this.http.post<AuthToken>(`${this.baseUrl}/refresh-token`, data);
   }

   override batchDelete(ids: ID[]): Observable<void> {
      return this.http.post<void>(`${this.baseUrl}/batch-deactivate`, {ids}).pipe(
         tap(() => this.deleteCacheEntries(ids)),
      );
   }

   protected override mapToLocal(operator: Operator): Observable<Operator> {
      return of(operator);
   }

   protected override mapToRemote(operator: MaybeID<Operator>): MaybeID<Operator> {
      return operator;
   }
}

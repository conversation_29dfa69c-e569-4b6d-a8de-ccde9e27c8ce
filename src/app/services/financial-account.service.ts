import {inject, Injectable} from '@angular/core';
import {DataService} from './data-service';
import {FinancialAccount, FinancialAccountChain} from '../data/financial-account';
import {ID, Identifiable, MaybeID, NoID} from '../data/identifiable';
import {forkJoin, mergeMap, Observable, of} from 'rxjs';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {CustomerService} from './customer.service';
import {defaultIfEmpty, map, switchMap} from 'rxjs/operators';
import {DateTime} from 'luxon';
import {Money} from '../data/common';

interface FinancialAccountDTO extends Identifiable {
   titular: ID;
   balance?: Money;
   createdAt?: string;
   deactivatedAt?: string;
}

interface FinancialAccountChainDTO extends Identifiable {
   activeAccount: ID;
   accounts: ID[];
   invoiceReceiver?: ID;
}

@Injectable({
   providedIn: 'root'
})
export class FinancialAccountService extends DataService<FinancialAccount> {
   private baseUrl = serverUrl('financial-account');
   private idUrl = addId.bind(null, this.baseUrl);

   private sCustomer = inject(CustomerService);
   private http = inject(HttpClient);

   override getAll(): Observable<FinancialAccount[]> {
      return this.http.get<FinancialAccountDTO[]>(this.baseUrl).pipe(
         mergeMap(cs => forkJoin(cs.map(c => this.mapToLocal(c)))),
      );
   }

   override get(id: ID): Observable<FinancialAccount> {
      return this.http.get<FinancialAccountDTO>(this.idUrl(id)).pipe(
         mergeMap(c => this.mapToLocal(c)),
      );
   }

   override add(newItem: NoID<FinancialAccount>): Observable<FinancialAccount> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newItem)).pipe(
         switchMap(id => this.get(id)),
      );
   }

   override update(newItem: FinancialAccount): Observable<void> {
      return this.http.put<void>(this.idUrl(newItem.id), this.mapToRemote(newItem));
   }

   override delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id));
   }

   search(query: string): Observable<FinancialAccount[]> {
      const url = `${this.baseUrl}/search`;
      const params = {query};

      return this.http.get<FinancialAccountDTO[]>(url, {params}).pipe(
         mergeMap(cs => forkJoin(cs.map(c => this.mapToLocal(c)))),
         defaultIfEmpty([]),
      );
   }

   ofTitular(titularId: ID): Observable<FinancialAccount[]> {
      const url = `${this.baseUrl}/of-titular`;
      const params = {titularId};

      return this.http.get<FinancialAccountDTO[]>(url, {params}).pipe(
         mergeMap(cs => forkJoin(cs.map(c => this.mapToLocal(c)))),
         defaultIfEmpty([]),
      );
   }

   getChain(id: ID): Observable<FinancialAccountChain> {
      return this.http.get<FinancialAccountChainDTO>(`${this.baseUrl}/chain/${id}`)
         .pipe(switchMap(dto => this.mapChain(dto)));
   }

   restore(id: ID): Observable<ID | null> {
      return this.http.post<ID>(`${this.baseUrl}/${id}/restore`, null);
   }

   private mapToLocal(financialAccountDTO: FinancialAccountDTO): Observable<FinancialAccount> {
      const {titular: t, createdAt: ca, deactivatedAt: da, ...rest} = financialAccountDTO;
      const createdAt = ca ? DateTime.fromMillis(parseInt(ca, 10)).setLocale('bg') :
         undefined;
      const deactivatedAt = da ? DateTime.fromMillis(parseInt(da, 10)).setLocale('bg') :
         undefined;

      return this.sCustomer.get(t).pipe(
         map(titular => ({titular, createdAt, deactivatedAt, ...rest}))
      );
   }

   private mapToRemote(financialAccount: MaybeID<FinancialAccount>): MaybeID<FinancialAccountDTO> {
      const {titular, createdAt, deactivatedAt, ...rest} = financialAccount;
      return {
         titular: titular.id,
         createdAt: createdAt?.toMillis().toString(),
         deactivatedAt: deactivatedAt?.toMillis().toString(),
         ...rest
      };
   }

   private mapChain(chainDTO: FinancialAccountChainDTO): Observable<FinancialAccountChain> {
      const {id, activeAccount, accounts, invoiceReceiver} = chainDTO;

      return forkJoin([
         this.get(activeAccount),
         invoiceReceiver ? this.sCustomer.get(invoiceReceiver) : of(undefined),
         ...accounts.map(a => this.get(a))
      ]).pipe(
         map(([active, receiver, ...all]) => ({
            id,
            activeAccount: active,
            invoiceReceiver: receiver,
            accounts: all,
         }))
      );
   }
}

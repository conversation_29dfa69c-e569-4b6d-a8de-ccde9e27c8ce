import {Injectable} from '@angular/core';
import {InvoiceCategory} from '../data/invoice';
import {HttpClient} from '@angular/common/http';
import {Observable, of} from 'rxjs';
import {ID, MaybeID, NoID} from '../data/identifiable';
import {DtoCache} from '../utility/dto-cache';

@Injectable({
   providedIn: 'root'
})
export class InvoiceCategoryService extends DtoCache<InvoiceCategory> {
   constructor(http: HttpClient) {
      super('invoice/category', http);
   }

   override add(_: NoID<InvoiceCategory>): Observable<InvoiceCategory> {
      throw new Error('Unsupported operation');
   }

   override delete(_: ID): Observable<void> {
      throw new Error('Unsupported operation');
   }

   override batchDelete(_: ID[]): Observable<void> {
      throw new Error('Unsupported operation');
   }

   protected mapToLocal(dto: InvoiceCategory): Observable<InvoiceCategory> {
      return of(dto);
   }

   protected mapToRemote(entry: MaybeID<InvoiceCategory>): MaybeID<InvoiceCategory> {
      return entry;
   }
}

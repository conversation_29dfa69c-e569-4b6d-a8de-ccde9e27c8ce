import {inject, Injectable} from '@angular/core';
import {HttpClient, HttpErrorResponse} from '@angular/common/http';
import {serverUrl} from '../utility/http-utility';
import {catchError, map, Observable, of, tap, throwError} from 'rxjs';
import {Customer} from '../data/customers/customer';

export enum VIESStatus {
   valid = 'Valid',
   invalid = 'Invalid',
   unknown = 'Unknown'
}

export interface VIESResult {
   status: VIESStatus;
   person?: Partial<Customer>;
}

@Injectable({providedIn: 'root'})
export class RegistryAgencyService {
   private http = inject(HttpClient);
   private url = serverUrl('registry-agency');

   private idNumberCache = new Map<string, Partial<Customer>>();
   private vatNumberCache = new Map<string, VIESResult>();

   findByIdNumber(idNumber: string): Observable<Partial<Customer> | undefined> {
      if (this.idNumberCache.has(idNumber)) {
         return of(this.idNumberCache.get(idNumber));
      }

      const url = `${this.url}/id-number`;
      const params = {idNumber};
      return this.http.get<Partial<Customer> | null>(url, {params}).pipe(
         catchError((err: HttpErrorResponse) => {
            if (err.status == 400) {
               return of(null);
            } else {
               console.error(err);
               this.idNumberCache.delete(idNumber);
               return throwError(() => err);
            }
         }),
         map(result => result ? this.removeNullsRecursively(result) : null),
         tap(result => result && this.idNumberCache.set(idNumber, result))
      );
   }

   findByVatNumber(vatNumber: string, countryCode: string): Observable<VIESResult> {
      const cacheKey = `${countryCode}${vatNumber}`;

      if (this.vatNumberCache.has(cacheKey)) {
         return of(this.vatNumberCache.get(cacheKey)!);
      }

      const params = {countryCode, vatNumber};
      return this.http.get<VIESResult>(`${this.url}/vies`, {params}).pipe(
         catchError((err: HttpErrorResponse) => {
            if (err.status == 400) {
               return of({status: VIESStatus.invalid});
            } else {
               console.error(err);
               this.vatNumberCache.delete(cacheKey);
               return throwError(() => err);
            }
         }),
         map(result => this.removeNullsRecursively(result)),
         tap(result => this.vatNumberCache.set(cacheKey, result)),
      );
   }

   private removeNullsRecursively(obj: any): any {
      if (Array.isArray(obj)) {
         return obj.map(this.removeNullsRecursively);
      } else if (obj !== null && typeof obj === 'object') {
         return Object.fromEntries(
            Object.entries(obj)
               .filter(([_, value]) => value !== null) // Remove null values
               .map(([key, value]) => [key, this.removeNullsRecursively(value)]) // Recursively process
         );
      }
      return obj;
   }
}

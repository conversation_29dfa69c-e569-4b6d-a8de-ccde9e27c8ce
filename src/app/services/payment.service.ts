import {Payment, PaymentMethod} from '../data/payment';
import {computed, inject, Injectable, Signal} from '@angular/core';
import {forkJoin, Observable, of, switchMap} from 'rxjs';
import {ID, Identifiable, NoID} from '../data/identifiable';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {defaultIfEmpty, map} from 'rxjs/operators';
import {FiscalAgentService, FiscalState} from './fiscal-agent.service';
import {FeatureService} from './feature.service';
import {AuthService} from '../auth/auth.service';
import {Money} from '../data/common';
import {OperatorService} from './operator.service';
import {DateTime} from 'luxon';

interface PaymentDTO extends Identifiable {
   account: ID;
   price: Money;
   method: PaymentMethod;
   isDownPayment: boolean;
   cashier: ID;
   timestamp: string;
   receiptId?: ID;
}

@Injectable({
   providedIn: 'root'
})
export class PaymentService {
   enabledNoBank: Signal<boolean>;
   enabled: Signal<boolean>;

   private baseUrl = serverUrl('payment');
   private idUrl = addId.bind(null, this.baseUrl);
   private http = inject(HttpClient);
   private sAuth = inject(AuthService);
   private sFiscalAgent = inject(FiscalAgentService);
   private sFeature = inject(FeatureService);
   private sOperator = inject(OperatorService);

   constructor() {
      this.enabledNoBank = computed(() => !this.sFeature.fiscal()
         || this.sFiscalAgent.state() === FiscalState.ready);
      this.enabled =
         computed(() => this.enabledNoBank() || this.sAuth.privileges.BANK_PAYMENTS);
   }

   getByAccount(accountId: ID): Observable<Payment[]> {
      const params = {accountId};
      return this.http.get<PaymentDTO[]>(this.baseUrl, {params}).pipe(
         switchMap(ps => forkJoin(ps.map(p => this.mapToLocal(p)))),
         defaultIfEmpty([]),
      );
   }

   add(newItem: NoID<Payment>): Observable<Payment> {
      return this.http.post<ID>(this.baseUrl, newItem).pipe(
         map(id => ({...newItem, id})),
      );
   }

   update(payment: Payment): Observable<void> {
      return this.http.put<void>(this.idUrl(payment.id), payment);
   }

   delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id));
   }

   moveToStandalone(payments: ID[], accountId: ID): Observable<void> {
      if (payments.length === 0) {
         return of(undefined);
      }

      const url = `${this.baseUrl}/move-to-standalone`;
      const params = {accountId};
      return this.http.post<void>(url, payments, {params});
   }

   moveToReservation(payments: ID[], reservationId: ID): Observable<void> {
      if (payments.length === 0) {
         return of(undefined);
      }

      const url = `${this.baseUrl}/move-to-reservation`;
      const params = {reservationId};
      return this.http.post<void>(url, payments, {params});
   }

   private mapToLocal({cashier, timestamp, ...rest}: PaymentDTO): Observable<Payment> {
      return this.sOperator.get(cashier).pipe(map(o => ({
         cashier: o.name,
         timestamp: DateTime.fromMillis(parseInt(timestamp, 10)),
         ...rest
      })));
   }
}

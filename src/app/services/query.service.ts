import {inject, Injectable} from '@angular/core';
import {QueryGroup, QueryInput, QueryResult} from '../data/queries/query';
import {Observable} from 'rxjs';
import {ID} from '../data/identifiable';
import {serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';

@Injectable({
   providedIn: 'root'
})
export class QueryService {
   private groupsUrl = serverUrl('v1/queries/groups/webui');
   private executeUrl = serverUrl('v1/queries');

   private http = inject(HttpClient);

   execute(queryId: ID, input: QueryInput): Observable<QueryResult> {
      const params = {
         queryId,
         from: input.dateRange.start.toMillis().toString(),
         to: input.dateRange.end.toMillis().toString(),
      };

      return this.http.get<QueryResult>(`${this.executeUrl}/data`, {params});
   }

   getGroups(): Observable<QueryGroup[]> {
      return this.http.get<QueryGroup[]>(this.groupsUrl);
   }
}

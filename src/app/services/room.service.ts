import {inject, Injectable} from '@angular/core';
import {Room} from '../data/room';
import {forkJoin, Observable, of} from 'rxjs';
import {ID, Identifiable, MaybeID} from '../data/identifiable';
import {DateTime} from 'luxon';
import {DtoCache} from '../utility/dto-cache';
import {map, tap} from 'rxjs/operators';
import {Consumable} from '../data/bundles/consumable';
import {entity} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {ConsumableService} from './consumable.service';
import {CleaningService} from './cleaning.service';
import {Note} from '../data/notes';

export interface BlockRoomsRequest {
   rooms: ID[];
   start: DateTime;
   end: DateTime;
   reason: string;
}

interface RoomDTO extends Identifiable {
   name: string;
   baseCapacity: number;
   additionalCapacity: number;
   baseConsumable: ID;
   cleaning?: ID;
   blocked?: {
      startTime: string;
      endTime: string;
      reason: string;
   };
}

@Injectable({
   providedIn: 'root'
})
export class RoomService extends DtoCache<Room, RoomDTO> {
   private sConsumable = inject(ConsumableService);
   private sCleaning = inject(CleaningService);

   constructor() {
      super('room', inject(HttpClient));
   }

   override update(newItem: Room): Observable<void> {
      return super.update(newItem).pipe(tap(() => this.invalidateCache()));
   }

   cleanRoom(id: ID): Observable<void> {
      return this.http.post<void>(`${this.idUrl(id)}/mark-clean`, {}).pipe(
         tap(() => this.invalidateCache()),
      );
   }

   assignCleaning(id: ID, cleaningId: ID): Observable<void> {
      const value = entity(cleaningId);
      return this.http.post<void>(`${this.idUrl(id)}/assign-cleaning`, value).pipe(
         tap(() => this.invalidateCache()),
      );
   }

   getCleaningNotes(): Observable<Record<ID, Note[]>> {
      return this.http.get<Record<ID, Note[]>>(`${this.baseUrl}/cleaning-notes`);
   }

   blockRooms({start, end, ...rest}: BlockRoomsRequest): Observable<void> {
      const body = {
         start: start.toMillis().toString(),
         end: end.toMillis().toString(),
         ...rest,
      };
      return this.http.post<void>(`${this.baseUrl}/block-rooms`, body).pipe(
         tap(() => this.invalidateCache()),
      );
   }

   unblockRooms(ids: ID[]): Observable<void> {
      return this.http.post<void>(`${this.baseUrl}/unblock-rooms`, {ids}).pipe(
         tap(() => this.invalidateCache()),
      );
   }

   protected mapToLocal({
                           baseConsumable,
                           cleaning,
                           blocked: b,
                           ...rest
                        }: RoomDTO): Observable<Room> {
      return forkJoin({
         bc: this.sConsumable.get(baseConsumable),
         c: cleaning ? this.sCleaning.get(cleaning) : of(undefined)
      }).pipe(
         map(({bc, c}) => ({
            baseConsumable: bc as Consumable,
            cleaning: c,
            blocked: b ? {
               start: DateTime.fromMillis(parseInt(b.startTime, 10)).setLocale('bg'),
               end: DateTime.fromMillis(parseInt(b.endTime, 10)).setLocale('bg'),
               reason: b.reason
            } : undefined,
            ...rest
         })),
      );
   }

   protected mapToRemote({
                            baseConsumable,
                            cleaning,
                            blocked,
                            ...rest
                         }: MaybeID<Room>): MaybeID<RoomDTO> {
      return {
         baseConsumable: baseConsumable.id,
         cleaning: cleaning?.id,
         blocked: blocked ? {
            startTime: blocked.start.toMillis().toString(),
            endTime: blocked.end.toMillis().toString(),
            reason: blocked.reason,
         } : undefined,
         ...rest
      };
   }
}

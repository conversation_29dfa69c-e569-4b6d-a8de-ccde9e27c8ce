import {inject, Injectable} from '@angular/core';
import {DataService} from './data-service';
import {VoucherSource} from '../data/voucher';
import {ID, Identifiable, MaybeID, NoID} from '../data/identifiable';
import {forkJoin, mergeMap, Observable, tap} from 'rxjs';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {ReservationSourceService} from './reservation-source.service';
import {ConsumableService} from './consumable.service';
import {map} from 'rxjs/operators';

interface VoucherSourceDTO extends Identifiable {
   name: string;
   reservationSources: ID[];
}

@Injectable({
   providedIn: 'root'
})
export class VoucherSourceService extends DataService<VoucherSource> {
   private baseUrl = serverUrl('voucher-source');
   private idUrl = addId.bind(null, this.baseUrl);

   private sReservationSource = inject(ReservationSourceService);
   private sConsumable = inject(ConsumableService);
   private http = inject(HttpClient);

   override getAll(): Observable<VoucherSource[]> {
      return this.http.get<VoucherSourceDTO[]>(this.baseUrl).pipe(
         mergeMap(cs => forkJoin(cs.map(c => this.mapToLocal(c)))),
      );
   }

   override get(id: ID): Observable<VoucherSource> {
      return this.http.get<VoucherSourceDTO>(this.idUrl(id)).pipe(
         mergeMap(c => this.mapToLocal(c)),
      );
   }

   override add(newItem: NoID<VoucherSource>): Observable<VoucherSource> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newItem)).pipe(
         map(id => ({...newItem, id}) as VoucherSource),
         tap(() => this.sConsumable.invalidateCache())
      );
   }

   override update(newItem: VoucherSource): Observable<void> {
      return this.http.put<void>(this.idUrl(newItem.id), this.mapToRemote(newItem)).pipe(
         tap(() => this.sConsumable.invalidateCache())
      );
   }

   override delete(id: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(id)).pipe(
         tap(() => this.sConsumable.invalidateCache())
      );
   }

   batchDelete(ids: ID[]): Observable<void> {
      return this.http.post<void>(`${this.baseUrl}/batch-delete`, {ids}).pipe(
         tap(() => this.sConsumable.invalidateCache()),
      );
   }

   private mapToLocal(voucherSource: VoucherSourceDTO): Observable<VoucherSource> {
      const {reservationSources, ...rest} = voucherSource;

      return this.sReservationSource.getAll().pipe(map(all => ({
         reservationSources: all.filter(s => reservationSources.includes(s.id)),
         ...rest
      })));
   }

   private mapToRemote(voucherSource: MaybeID<VoucherSource>): MaybeID<VoucherSourceDTO> {
      const {reservationSources, ...rest} = voucherSource;

      const sourceIds = reservationSources.map(s => s.id);
      return {reservationSources: sourceIds, ...rest};
   }
}

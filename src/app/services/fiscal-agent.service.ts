import {EventEmitter, inject, Injectable, signal} from '@angular/core';
import {addId} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';
import {Observable, tap, throwError} from 'rxjs';
import {
   FiscalAgentPrinter,
   FPAAmount,
   FPAAmountResponse,
   FPAReceiptRequest,
   FPAReceiptResponse,
   FPAReceiptReversalRequest,
   FPAResponse,
   paymentToFiscal,
   ReceiptReversalReason
} from '../data/fiscal/fiscal-agent';
import {ID} from '../data/identifiable';
import {NotificationService} from './notification.service';
import {AuthService} from '../auth/auth.service';
import {Receipt} from '../data/fiscal/fiscal';
import {Payment} from '../data/payment';

export enum FiscalState {
   agentNotRunning = 'agent-not-running',
   noDevice = 'no-device',
   loadingDevices = 'loading-devices',
   ready = 'ready',
}

interface OperatorCredentials {
   operator: string;
   operatorPassword: string;
}

@Injectable({
   providedIn: 'root'
})
export class FiscalAgentService {
   currentDeviceId: ID = '';
   renewCash$ = new EventEmitter<void>();
   state = signal(FiscalState.agentNotRunning);

   private url = 'http://localhost:8001/printers';
   private idUrl = addId.bind(null, this.url);
   private adminUrl = 'http://localhost:8001/service';

   private http = inject(HttpClient);
   private sNotification = inject(NotificationService);
   private sAuth = inject(AuthService);
   private readonly credentials?: OperatorCredentials;

   constructor() {
      const operator = this.sAuth.current();
      if (operator && operator.fiscalPassword) {
         this.credentials = {
            operator: operator.code,
            operatorPassword: operator.fiscalPassword
         };
      }
   }

   getAll(): Observable<Record<ID, FiscalAgentPrinter>> {
      return this.http.get<Record<ID, FiscalAgentPrinter>>(this.url);
   }

   getInfo(): Observable<FiscalAgentPrinter> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      return this.http.get<FiscalAgentPrinter>(this.idUrl(this.currentDeviceId));
   }

   getStatus(): Observable<FPAResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const url = `${this.idUrl(this.currentDeviceId)}/status`;
      return this.http.get<FPAResponse>(url);
   }

   receipt(payment: Payment, receipt: Receipt): Observable<FPAReceiptResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const {items, uniqueSaleNumber} = receipt;
      const request: FPAReceiptRequest = {
         items,
         payments: [paymentToFiscal(payment)],
         uniqueSaleNumber
      };

      const url = `${this.idUrl(this.currentDeviceId)}/receipt`;
      return this.http.post<FPAReceiptResponse>(url, this.withCredentials(request))
         .pipe(this.showErrors(), this.signalRenewCash());
   }

   reverse(payment: Payment,
           receipt: Receipt,
           reason: ReceiptReversalReason): Observable<FPAReceiptResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const {
         uniqueSaleNumber,
         items,
         receiptNumber,
         receiptDateTime,
         fiscalMemorySerialNumber
      } = receipt;

      if (receiptNumber && receiptDateTime && fiscalMemorySerialNumber) {
         const request: FPAReceiptReversalRequest = {
            payments: [paymentToFiscal(payment)],
            uniqueSaleNumber,
            items,
            receiptNumber,
            receiptDateTime,
            fiscalMemorySerialNumber,
            reason,
         };

         const url = `${this.idUrl(this.currentDeviceId)}/reversalreceipt`;
         return this.http.post<FPAReceiptResponse>(url, this.withCredentials(request))
            .pipe(this.showErrors(), this.signalRenewCash());
      }

      return throwError(() => new Error(
         `Фискалната бележка ${payment.receiptId} не е запазена правилно!`));
   }

   lastReceiptDuplicate(): Observable<FPAResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      return this.http.post<FPAResponse>(`${this.idUrl(this.currentDeviceId)}/duplicate`,
         undefined).pipe(this.showErrors());
   }

   getCashAmount(): Observable<FPAAmountResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      return this.http.get<FPAAmountResponse>(`${this.idUrl(this.currentDeviceId)}/cash`)
         .pipe(this.showErrors());
   }

   deposit(amount: FPAAmount): Observable<FPAResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const url = `${this.idUrl(this.currentDeviceId)}/deposit`;
      return this.http.post<FPAResponse>(url, this.withCredentials(amount))
         .pipe(this.showErrors(), this.signalRenewCash());
   }

   withdraw(amount: FPAAmount): Observable<FPAResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const url = `${this.idUrl(this.currentDeviceId)}/withdraw`;
      return this.http.post<FPAResponse>(url, this.withCredentials(amount))
         .pipe(this.showErrors(), this.signalRenewCash());
   }

   xReport(): Observable<FPAResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const url = `${this.idUrl(this.currentDeviceId)}/xreport`;
      return this.http.post<FPAResponse>(url, null).pipe(this.showErrors());
   }

   zReport(): Observable<FPAResponse> {
      if (!this.currentDeviceId) {
         return this.noDeviceError();
      }

      const url = `${this.idUrl(this.currentDeviceId)}/zreport`;
      return this.http.post<FPAResponse>(url, null).pipe(
         this.showErrors(), this.signalRenewCash());
   }

   detect(): Observable<void> {
      return this.http.get<void>(`${this.adminUrl}/detect`);
   }

   private showErrors(): any {
      return tap<FPAResponse>(response => {
         if (!response.ok) {
            this.sNotification.displayFiscalErrors(response.messages);
         }
      });
   }

   private signalRenewCash(): any {
      return tap<any>(() => this.renewCash$.emit());
   }

   private withCredentials(data: any = {}): any {
      return this.credentials ? {...data, ...this.credentials} : data;
   }

   private noDeviceError(): Observable<never> {
      return throwError(() => new Error('Няма фискално устройство!'));
   }
}

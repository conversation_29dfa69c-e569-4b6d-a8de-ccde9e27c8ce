import {Injectable, Optional} from '@angular/core';
import {GoogleAnalyticsService} from 'ngx-google-analytics';

@Injectable({
   providedIn: 'root'
})
export class AnalyticsService {
   constructor(@Optional() private gaService: GoogleAnalyticsService | null) {
   }

   event(category: string, event: string, label?: string, value?: number): void {
      this.gaService?.event(event, category, label, value);
   }
}

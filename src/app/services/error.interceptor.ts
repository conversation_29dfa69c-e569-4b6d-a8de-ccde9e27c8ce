import {inject, Injectable} from '@angular/core';
import {
   HttpErrorResponse,
   HttpEvent,
   HttpHandler,
   HttpInterceptor,
   HttpRequest
} from '@angular/common/http';
import {catchError, Observable, throwError} from 'rxjs';
import {Clipboard} from '@angular/cdk/clipboard';
import {NotificationService} from './notification.service';

interface ApiError {
   path: string;
   message: string;
   timestamp: string;
   alertUser: boolean;
   payload?: any;
}

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
   private sNotification = inject(NotificationService);
   private clipboard = inject(Clipboard);

   intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
      return next.handle(req).pipe(
         catchError((errorResponse: HttpErrorResponse) => {
            const {error}: { error?: ApiError } = errorResponse;
            if (error?.alertUser) {
               let {message} = error;
               if (error.payload) {
                  message += ` ${JSON.stringify(error.payload)}`;
               }

               this.sNotification.displayNotification(message, 'Копирай грешката',
                  () => this.clipboard.copy(JSON.stringify(errorResponse, undefined, 3)));
            }

            return throwError(() => errorResponse);
         })
      );
   }
}

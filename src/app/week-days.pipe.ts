import {Pipe, PipeTransform} from '@angular/core';
import {DayOfWeek} from './data/common';

@Pipe({
   name: 'weekDays',
   standalone: false
})
export class WeekDaysPipe implements PipeTransform {
   transform(weekDays: DayOfWeek[]): string {
      const keys = Object.keys(DayOfWeek);
      const values = Object.values(DayOfWeek);
      const indices = weekDays.map(day => keys.indexOf(day));
      const transformed = indices.sort().map(idx => values[idx]);
      return transformed.join(', ');
   }
}

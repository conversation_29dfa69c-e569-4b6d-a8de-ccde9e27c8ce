import {booleanAttribute, Component, Input, OnInit} from '@angular/core';

@Component({
   selector: 'app-info-icon',
   template: `
      <mat-icon [matTooltipClass]="tooltipClass"
                [matTooltipShowDelay]="immediate ? 0 : 500" [matTooltip]="tooltip"
                class="material-icons-outlined" inline matTooltipPosition="above">
         info
      </mat-icon>
   `,
   standalone: false
})
export class InfoIconComponent implements OnInit {
   @Input() tooltip = '';
   @Input() tooltipPrefix = '';
   @Input({transform: booleanAttribute}) immediate = false;
   @Input() tooltipClass = '';

   ngOnInit(): void {
      if (this.tooltipPrefix) {
         this.tooltip = `${this.tooltipPrefix}${this.tooltip}`;
      }
   }
}

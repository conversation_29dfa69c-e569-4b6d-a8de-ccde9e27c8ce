import {Component, inject, Input, OnInit} from '@angular/core';
import {Observable} from 'rxjs';
import {Pricing} from '../../../data/pricing';
import {PricingService} from '../../../services/pricing.service';
import {equalIdentifiables, nameJoin} from '../../../utility/utility';
import {UntypedFormControl} from '@angular/forms';

@Component({
   selector: 'app-price-select',
   templateUrl: './price-select.component.html',
   styles: [`
      .price-select {
         width: 100%
      }
   `],
   standalone: false
})
export class PriceSelectComponent implements OnInit {
   @Input() control!: UntypedFormControl;
   @Input() multiple = false;

   pricings$?: Observable<Pricing[]>;
   equalPricings = equalIdentifiables;
   nameJoin = nameJoin;

   private sPricing = inject(PricingService);

   get enabled(): boolean {
      return this.control.enabled;
   }

   ngOnInit(): void {
      this.pricings$ = this.sPricing.getAll();
   }

   enable(): void {
      this.control.enable();
   }

   disable(): void {
      this.control.setValue([]);
      this.control.disable();
   }
}

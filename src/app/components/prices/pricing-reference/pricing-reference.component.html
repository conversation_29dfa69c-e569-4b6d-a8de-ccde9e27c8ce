<mat-card appearance="outlined">
   <mat-card-header style="display: flex; justify-content: space-between;">
      <mat-card-title>{{pricing.name}}</mat-card-title>
      <div>
         <button (click)="emitEdit()" mat-icon-button>
            <mat-icon>edit</mat-icon>
         </button>
         <button (click)="emitDelete()" mat-icon-button>
            <mat-icon>delete</mat-icon>
         </button>
      </div>
   </mat-card-header>
   <mat-card-content>
      <mat-list>
         @for (range of pricing.ranges; track range) {
            <mat-list-item>
               <span matListItemTitle> {{range.dateRange | dateRanges}} </span>
               @if (range.activeWeekDays) {
                  <p matListItemLine>Активен {{range.activeWeekDays | weekDays}}</p>
               } @else {
                  <p matListItemLine>Активен през цялата седмица</p>
               }
               <p matListItemMeta>Приоритет {{range.priority}}</p>
            </mat-list-item>
         }
      </mat-list>
   </mat-card-content>
</mat-card>

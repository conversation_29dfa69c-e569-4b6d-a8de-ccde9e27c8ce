import {Component, EventEmitter, Input, Output} from '@angular/core';
import {Pricing} from '../../../data/pricing';

@Component({
   selector: 'app-pricing-reference',
   templateUrl: './pricing-reference.component.html',
   standalone: false
})
export class PricingReferenceComponent {
   @Input() pricing!: Pricing;
   @Output() editPricing = new EventEmitter<Pricing>();
   @Output() deletePricing = new EventEmitter<Pricing>();

   emitEdit(): void {
      this.editPricing.emit(this.pricing);
   }

   emitDelete(): void {
      this.deletePricing.emit(this.pricing);
   }
}

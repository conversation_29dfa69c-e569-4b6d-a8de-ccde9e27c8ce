import {Component, EventEmitter, inject, Input, OnInit, Output} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {Observable} from 'rxjs';
import {Customer} from 'src/app/data/customers/customer';
import {FinancialAccount} from 'src/app/data/financial-account';
import {Invoice, InvoiceCategory, InvoiceCategoryType} from 'src/app/data/invoice';
import {Payment} from 'src/app/data/payment';
import {Purchase} from 'src/app/data/purchase';
import {InvoiceCategoryService} from 'src/app/services/invoice-category.service';
import {InvoiceService} from 'src/app/services/invoice.service';
import {InvoiceData, InvoiceInput, issueInvoice} from 'src/app/utility/invoice-utility';

@Component({
   selector: 'app-invoice-button',
   standalone: false,
   templateUrl: './invoice-button.component.html'
})
export class InvoiceButtonComponent implements OnInit {
   @Input() account!: FinancialAccount;
   @Input() items: (Payment | Purchase)[] = [];
   @Input() disabled: boolean = false;
   @Input() titular?: Customer;

   @Output() invoice = new EventEmitter<InvoiceData>();

   protected invoiceCategories: InvoiceCategory[] = [];

   private sInvoice = inject(InvoiceService);
   private sInvoiceCategory = inject(InvoiceCategoryService);
   private dialog = inject(MatDialog);

   private sender: Customer | null = null;
   private existingInvoices: Invoice[] = [];

   ngOnInit(): void {
      this.sInvoice.getIssuer().subscribe(issuer => this.sender = issuer);
      this.sInvoice.search(this.account.id).subscribe(is => this.existingInvoices = is);
      this.sInvoiceCategory.getAll().subscribe(ics => this.invoiceCategories =
         ics.filter(ic => ic.type !== InvoiceCategoryType.creditNote));
   }

   protected isInvoiceCategoryEligible(category: InvoiceCategory): boolean {
      if (category.isFiscalDocument) {
         const fiscalInvoiceDurationExpired = this.account.deactivatedAt &&
            this.account.deactivatedAt.diffNow('days').days < -5;
         if (fiscalInvoiceDurationExpired) {
            return false;
         }

         const creditNotes = this.existingInvoices.filter(i => i.category.type === InvoiceCategoryType.creditNote);

         const activeFiscals = this.existingInvoices.find(
            i => i.category.id == category.id && !creditNotes.find(creditNote => creditNote.parentInvoice?.id === i.id));

         return !activeFiscals;
      } else {
         return this.items.length === 0;
      }
   }

   protected issueInvoice(category: InvoiceCategory) {
      this.issueInvoiceInternal(category).subscribe(data => this.invoice.emit(data));
   }

   private issueInvoiceInternal(category: InvoiceCategory): Observable<InvoiceData> {
      const titular = this.titular ? this.titular : this.account.titular;
      const items = this.items.map(item => item.id);
      const invoiceInput = {
         category,
         sender: this.sender,
         accountId: this.account.id,
         titular,
         items,
      } as InvoiceInput;

      return issueInvoice(invoiceInput, this.sInvoice, this.dialog);
   }

}

import {Component, inject} from '@angular/core';
import {MatButtonModule} from '@angular/material/button';
import {
   MAT_SNACK_BAR_DATA,
   MatSnackBarModule,
   MatSnackBarRef
} from '@angular/material/snack-bar';
import {FPAMessageError, FPMessage, MessageSeverity} from '../data/fiscal/fiscal-agent';
import {fiscalErrors} from '../data/fiscal/fiscal-errors';
import {Clipboard} from '@angular/cdk/clipboard';

@Component({
   template: `
      <div matSnackBarLabel>
         <b>Грешка при операция с фискално устройство:</b>
         @for (message of messages; track message) {
            <div>{{message}}</div>
         }
      </div>
      <div class="apart-row" matSnackBarActions style="margin: 0 12px 12px;">
         <button (click)="copyAndDismiss()" mat-button matSnackBarAction>
            Копирай грешката
         </button>
         <button (click)="dismiss()" mat-button matSnackBarAction>
            Затвори
         </button>
      </div>
   `,
   imports: [MatButtonModule, MatSnackBarModule]
})
export class FiscalNotificationComponent {
   data?: FPMessage[] = inject(MAT_SNACK_BAR_DATA);
   messages: string[] = this.data
      ?.filter((m: FPMessage) => m.type !== MessageSeverity.info)
      .flatMap((m: FPAMessageError) => {
         const translation = fiscalErrors.get(m.code);
         if (translation) {
            return [`[${m.code}] (${m.type}) ${translation}`, m.text, '---'];
         } else {
            return `[${m.code}] (${m.type}) ${m.text}`;
         }
      }) ?? ['Фискалното устройство върна неочаквана грешка!'];

   private snackbar = inject(MatSnackBarRef);
   private clipboard = inject(Clipboard);

   dismiss(): void {
      this.snackbar.dismissWithAction();
   }

   copyAndDismiss(): void {
      this.clipboard.copy(JSON.stringify(this.messages));
      this.dismiss();
   }
}

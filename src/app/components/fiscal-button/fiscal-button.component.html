<button [matMenuTriggerFor]="fiscalMenu" mat-icon-button>
   <mat-icon [class.blink]="printing" [ngClass]="sFiscalAgent.state()" matBadge="&#8288;"
             matBadgeSize="small">
      point_of_sale
   </mat-icon>
</button>

<mat-menu #fiscalMenu>
   @switch (sFiscalAgent.state()) {
      @case (fs.ready) {
         @if (device) {
            <div (click)="blockEvent($event)" mat-menu-item>
               {{device.manufacturer}} {{device.model}}
               <div style="font-size: 0.7em;">
                  <i>Номер {{device.serialNumber}} с
                     ЕИК {{device.taxIdentificationNumber}}</i>
               </div>
            </div>
            <button [matMenuTriggerFor]="cashMenu" mat-menu-item>
               <b>{{cash$ | async}}</b> в касата
            </button>
            <button [matMenuTriggerFor]="reportMenu" mat-menu-item>
               Отчети
            </button>
            <button [matMenuTriggerFor]="actionsMenu" mat-menu-item>
               Действия
            </button>
         }
      }
      @case (fs.loadingDevices) {
         <div mat-menu-item (click)="blockEvent($event)">
            <div class="text-center">Списъкът с устройства се презарежда!</div>
         </div>
      }
      @case (fs.noDevice) {
         <div mat-menu-item (click)="blockEvent($event)">
            Няма свързано устройство!
         </div>
         <button (click)="detectPrinters()" mat-menu-item>
            <mat-icon>refresh</mat-icon>
            Презареди устройствата
         </button>
      }
      @case (fs.agentNotRunning) {
         <div mat-menu-item (click)="blockEvent($event)">
            <div class="text-center">Фискализацията не е настроена правилно!</div>
         </div>
         <a href="https://help.overview-pms.com/docs/fiscal" target="_blank"
            mat-menu-item>
            <mat-icon>help_center</mat-icon>
            Инструкции
         </a>
      }
   }
</mat-menu>

<mat-menu #cashMenu>
   <button (click)="deposit()" class="green-text" mat-menu-item>
      <mat-icon class="green-text">add</mat-icon>
      Депозирай
   </button>
   <button (click)="withdraw()" class="red-text" mat-menu-item>
      <mat-icon class="red-text">remove</mat-icon>
      Изтегли
   </button>
</mat-menu>

<mat-menu #reportMenu>
   <button (click)="xReport()" mat-menu-item>
      Дневен отчет
   </button>
   <button (click)="zReport()" class="red-text" mat-menu-item>
      Дневен отчет с нулиране
   </button>
</mat-menu>

<mat-menu #actionsMenu>
   <button (click)="lastReceiptDuplicate()" mat-menu-item>
      Дубликат на последния фискален бон
   </button>
</mat-menu>

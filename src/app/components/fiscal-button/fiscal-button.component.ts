import {Component, inject, OnDestroy, OnInit} from '@angular/core';
import {Observable, Observer} from 'rxjs';
import {FiscalAgentService, FiscalState} from '../../services/fiscal-agent.service';
import {
   FiscalAgentPrinter,
   FPMessage,
   MessageSeverity
} from '../../data/fiscal/fiscal-agent';
import {FiscalService} from '../../services/fiscal.service';
import {filter, map, switchMap} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import {
   AmountDialogComponent,
   AmountDialogData
} from '../../dialogs/amount-dialog.component';
import {NotificationService} from '../../services/notification.service';
import {blockEvent} from '../../utility/utility';
import {HttpErrorResponse} from '@angular/common/http';

@Component({
   selector: 'app-fiscal-button',
   templateUrl: './fiscal-button.component.html',
   styleUrl: './fiscal-button.component.scss',
   standalone: false
})
export class FiscalButtonComponent implements OnInit, OnDestroy {
   device?: FiscalAgentPrinter;
   cash$?: Observable<string>;
   printing = false;

   blockEvent = blockEvent;
   fs = FiscalState;

   sFiscalAgent = inject(FiscalAgentService);
   private sFiscal = inject(FiscalService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);
   private timeout?: number;

   ngOnInit(): void {
      this.sFiscalAgent.renewCash$.subscribe(() => this.renewCash());
      this.renewData();
   }

   ngOnDestroy(): void {
      if (this.timeout) {
         window.clearTimeout(this.timeout);
      }
   }

   detectPrinters(): void {
      setTimeout(() => this.detect().subscribe(() => this.renewData()), 250);
   }

   lastReceiptDuplicate(): void {
      this.sNotification.openConfirmationDialog({
         title: 'Дубликат на последния фискален бон',
         description: 'Наистина ли искате да издадете дубликат на последния фискален бон?',
      }).pipe(switchMap(() => {
         this.printing = true;
         return this.sFiscalAgent.lastReceiptDuplicate();
      })).subscribe(this.getCb());
   }

   deposit(): void {
      const d = this.device;
      if (d) {
         this.openCashDialog(`Депозиране в ${d.manufacturer} ${d.model}`, 'Депозит').pipe(
            switchMap(amount => {
               this.printing = true;
               return this.sFiscalAgent.deposit({amount});
            })
         ).subscribe(this.getCb());
      }
   }

   withdraw(): void {
      const d = this.device;
      if (d) {
         this.openCashDialog(`Теглене от ${d.manufacturer} ${d.model}`, 'Сума').pipe(
            switchMap(amount => {
               this.printing = true;
               return this.sFiscalAgent.withdraw({amount});
            })
         ).subscribe(this.getCb());
      }
   }

   xReport(): void {
      this.sNotification.openConfirmationDialog({
         title: 'Дневен отчет',
         description: 'Наистина ли искате да принтирате дневния отчет?',
      }).pipe(switchMap(() => {
         this.printing = true;
         return this.sFiscalAgent.xReport();
      })).subscribe(this.getCb());
   }

   zReport(): void {
      this.sNotification.openConfirmationDialog({
         title: 'Дневен отчет с нулиране',
         description: [
            'Наистина ли искате да принтирате дневния отчет с нулиране?',
            'Това действие ще запише текущият отчет във фискалната памет на устройството',
            'и ще изпрати информацията към НАП!'
         ],
      }).pipe(switchMap(() => {
         this.printing = true;
         return this.sFiscalAgent.zReport();
      })).subscribe(this.getCb());
   }

   private openCashDialog(heading: string, inputLabel: string): Observable<number> {
      const data: AmountDialogData = {heading, inputLabel};
      return this.dialog.open(AmountDialogComponent, {data})
         .afterClosed()
         .pipe(filter(result => !!result));
   }

   private renewData(): void {
      this.sFiscalAgent.currentDeviceId = '';
      this.device = undefined;

      this.sFiscalAgent.getAll().subscribe(
         {
            next: fds => {
               const devices = Object.entries(fds);

               if (devices.length > 0) {
                  this.sFiscalAgent.currentDeviceId = devices[0][0];
                  this.device = devices[0][1];

                  this.renewCash();

                  this.savePrinter(this.sFiscalAgent.currentDeviceId,
                     this.device.serialNumber);

                  this.sFiscalAgent.state.set(FiscalState.ready);
                  this.timeout = window.setTimeout(() => this.checkDeviceStatus(), 5000);
               } else {
                  this.sFiscalAgent.state.set(FiscalState.noDevice);
                  this.timeout = window.setTimeout(() => this.renewData(), 5000);
               }
            },
            error: err => {
               if (err instanceof HttpErrorResponse) {
                  if (err.status === 405) {
                     this.sFiscalAgent.state.set(FiscalState.loadingDevices);
                  } else {
                     this.sFiscalAgent.state.set(FiscalState.agentNotRunning);
                  }
                  this.timeout = window.setTimeout(() => this.renewData(), 5000);
               }
            }
         });
   }

   private checkDeviceStatus(): void {
      this.sFiscalAgent.getStatus().subscribe({
         next: fp => {
            const deviceNotResponding = (msg: FPMessage) => msg.type ===
               MessageSeverity.error && msg.code === 'E101';

            if (!fp.ok && fp.messages?.some(deviceNotResponding)) {
               this.detectAndRenew();
            } else {
               this.timeout = window.setTimeout(() => this.checkDeviceStatus(), 5000);
            }
         },
         error: () => this.renewData()
      });
   }

   private renewCash(): void {
      this.cash$ = this.sFiscalAgent.getCashAmount()
         .pipe(map(fpr => `${fpr.amount} лв.`));
   }

   private savePrinter(printerId: string, serialNumber: string): void {
      this.sFiscal.create({printerId, serialNumber}).subscribe(
         deviceId => this.sFiscal.currentDeviceId = deviceId
      );
   }

   private detect(): Observable<void> {
      this.sFiscalAgent.state.set(FiscalState.loadingDevices);
      return this.sFiscalAgent.detect();
   }

   private detectAndRenew(): void {
      this.sFiscalAgent.state.set(FiscalState.loadingDevices);
      this.sFiscalAgent.detect().subscribe(() => this.renewData());
   }

   private getCb(): Observer<any> {
      return {
         next: () => this.printing = false,
         error: () => this.printing = false,
         complete: () => this.printing = false,
      };
   }
}

import {Component, inject, Input, OnInit} from '@angular/core';
import {equalIdentifiables} from '../../../utility/utility';
import {UntypedFormControl} from '@angular/forms';
import {Observable} from 'rxjs';
import {CustomerGroup} from '../../../data/customers/customer-group';
import {CustomerGroupService} from '../../../services/customer-group.service';

@Component({
   selector: 'app-customer-group-select',
   templateUrl: './customer-group-select.component.html',
   standalone: false
})
export class CustomerGroupSelectComponent implements OnInit {
   @Input() control!: UntypedFormControl;
   @Input() multiple = false;

   customerGroups$?: Observable<CustomerGroup[]>;
   equalGroups = equalIdentifiables;

   private sCustomerGroup = inject(CustomerGroupService);

   get enabled(): boolean {
      return this.control.enabled;
   }

   ngOnInit(): void {
      this.customerGroups$ = this.sCustomerGroup.getAll();
   }

   enable(): void {
      this.control.enable();
   }

   disable(): void {
      this.control.setValue([]);
      this.control.disable();
   }
}

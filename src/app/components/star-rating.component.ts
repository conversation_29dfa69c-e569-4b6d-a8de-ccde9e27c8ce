import {Component, input, OnInit} from '@angular/core';
import {MatIconModule} from '@angular/material/icon';

@Component({
   selector: 'app-star-rating',
   standalone: true,
   imports: [
      MatIconModule
   ],
   template: `
      <div class="flex-row">
         @for (_ of full; track $index) {
            <mat-icon>star</mat-icon>
         }
         @for (_ of remaining; track $index) {
            <mat-icon>star_outline</mat-icon>
         }
      </div>
   `,
})
export class StarRatingComponent implements OnInit {
   rating = input(5);
   maxRating = input(5);

   full: any[] = [];
   remaining: any[] = [];

   ngOnInit(): void {
      this.full = Array(this.rating());
      this.remaining = Array(this.maxRating() - this.rating());
   }
}

import {
   Component,
   EventEmitter,
   inject,
   Input,
   OnChanges,
   OnInit,
   Output,
   SimpleChanges
} from '@angular/core';
import {Payment} from '../../data/payment';
import {Purchase} from '../../data/purchase';
import {moneyToString, normalizeMoney, nullMoney} from '../../utility/utility';
import {Money} from '../../data/common';
import {SelectionModel} from '@angular/cdk/collections';
import {ReservationPipe} from '../../pipes/reservation.pipe';
import {PaymentMethodPipe} from '../../pipes/payment-method.pipe';
import {PaymentService} from '../../services/payment.service';
import {DateTimePipe} from '../../pipes/date-time.pipe';
import {AuthService} from '../../auth/auth.service';
import {Privilege} from '../../data/auth/operator';
import {PurchaseService} from '../../services/purchase.service';
import {NotificationService} from '../../services/notification.service';
import {switchMap} from 'rxjs/operators';
import {InvoiceData} from 'src/app/utility/invoice-utility';
import {FinancialAccount} from 'src/app/data/financial-account';
import {Customer} from 'src/app/data/customers/customer';

interface BalanceRowDataBase {
   name: string;
   quantity: string | number;
   price: string;
   klass: any;
   item: Payment | Purchase;
   isPurchase: boolean;
   description: string;
}

interface PaymentRowData extends BalanceRowDataBase {
   item: Payment;
   isPurchase: false;
}

interface PurchaseRowData extends BalanceRowDataBase {
   item: Purchase;
   isPurchase: true;
}

type BalanceRowData = PaymentRowData | PurchaseRowData;

@Component({
   selector: 'app-balance-table',
   templateUrl: './balance-table.component.html',
   standalone: false
})
export class BalanceTableComponent implements OnInit, OnChanges {
   @Input() account!: FinancialAccount;
   @Input() invoiceReceiver?: Customer;

   @Input() edit = false;
   @Input() payments: Payment[] = [];
   @Input() purchases: Purchase[] = [];
   @Input() allowChoice = true;

   @Output() renew = new EventEmitter<void>();
   @Output() balanceChange = new EventEmitter<Money>();
   @Output() moveSelected = new EventEmitter<void>();
   @Output() invoice = new EventEmitter<InvoiceData>();
   @Output() purchase = new EventEmitter<Purchase | undefined>();
   @Output() payment = new EventEmitter<Payment | undefined>();

   displayedColumns = ['name', 'description', 'quantity', 'price'];
   selection = new SelectionModel<Payment | Purchase>(true, []);
   data: BalanceRowData[] = [];
   balance: Money = nullMoney();

   sPayment = inject(PaymentService);
   private sPurchase = inject(PurchaseService);
   private sNotification = inject(NotificationService);
   private sAuth = inject(AuthService);

   ngOnInit(): void {
      if (this.allowChoice) {
         this.displayedColumns.unshift('chosen');

         if (this.edit && this.sAuth.privileges[Privilege.balanceEdit]) {
            this.displayedColumns.push('actions');
         }
      }
   }

   ngOnChanges(changes: SimpleChanges): void {
      if (changes.payments || changes.purchases) {
         this.renewData();
      }
   }

   isAllSelected(): boolean {
      return this.selection.selected.length === this.data.length;
   }

   masterToggle(): void {
      if (this.isAllSelected()) {
         this.selection.clear();
      } else {
         this.payments.forEach(payment => this.selection.select(payment));
         this.purchases.forEach(purchase => this.selection.select(purchase));
      }
   }

   emitEdit(element: BalanceRowData): void {
      if (element.isPurchase) {
         this.purchase.emit(element.item);
      } else {
         this.payment.emit(element.item);
      }
   }

   delete(element: BalanceRowData) {
      const id = element.item.id;
      const {isPurchase} = element;
      const text = isPurchase ? 'начисление' : 'плащане';
      const deleteOp = isPurchase ? this.sPurchase.delete(id) : this.sPayment.delete(id);

      this.sNotification.openConfirmationDialog({
         title: `Изтриване на ${text}`,
         description: `Сигурни ли сте ще искате да изтриете ${text}то?`,
      }).pipe(switchMap(() => deleteOp)).subscribe(() => this.renew.emit());
   }

   private renewData(): void {
      this.data = [];
      this.balance = nullMoney();
      this.selection.clear();

      this.purchases.forEach(purchase => {
         const {bundle, quantity, price, originalReservation} = purchase;
         this.data.push({
            name: bundle.name,
            quantity,
            price: moneyToString(price),
            klass: {'red-text': true},
            item: purchase,
            isPurchase: true,
            description: ReservationPipe.toString(originalReservation),
         });

         this.balance.amount -= price.amount;
      });

      const paymentToString = (p: Payment) =>
         `Въведено от ${p.cashier} в ${DateTimePipe.toString(p.timestamp, false)}`;

      this.payments.forEach(payment => {
         const {price, method} = payment;
         this.data.push({
            name: (payment.isDownPayment ? 'Капаро - ' : 'Плащане - ') +
               PaymentMethodPipe.toString(method),
            quantity: '',
            price: moneyToString(price),
            klass: {'green-text': true},
            item: payment,
            isPurchase: false,
            description: paymentToString(payment),
         });

         this.balance.amount += price.amount;
      });

      this.balance = normalizeMoney(this.balance);
      this.balanceChange.emit(this.balance);
   }
}

<table [dataSource]="data" mat-table style="width: 100%;">
   <ng-container matColumnDef="chosen">
      <th *matHeaderCellDef mat-header-cell>
         <mat-checkbox (change)="$event ? masterToggle() : null"
                       [checked]="selection.hasValue() && isAllSelected()"
                       [indeterminate]="selection.hasValue() && !isAllSelected()"
                       color="primary">
         </mat-checkbox>
      </th>
      <td *matCellDef="let element" mat-cell>
         <mat-checkbox (change)="$event ? selection.toggle(element.item) : null"
                       [checked]="selection.isSelected(element.item)"
                       color="primary"></mat-checkbox>
      </td>
   </ng-container>

   <ng-container matColumnDef="name">
      <th *matHeaderCellDef mat-header-cell>
         @if (allowChoice) {
            <button (click)="purchase.emit()" class="red-text" mat-icon-button
                    matTooltip="Добавяне на начисление" matTooltipPosition="above">
               <mat-icon>add</mat-icon>
            </button>
            @if (sPayment.enabled()) {
               <button (click)="payment.emit()" class="green-text" mat-icon-button
                       matTooltip="Добавяне на плащане" matTooltipPosition="above">
                  <mat-icon>add</mat-icon>
               </button>
            } @else {
               <span matTooltip="Плащанията са забранени понеже системата не може да
               установи връзка с фискално устройство!" matTooltipPosition="above"
                     matTooltipShowDelay="0">
                  <button disabled mat-icon-button>
                     <mat-icon>add</mat-icon>
                  </button>
               </span>
            }
            <app-invoice-button [account]="account" [titular]="invoiceReceiver"
                                [disabled]="data.length === 0"
                                [items]="selection.selected"
                                (invoice)="invoice.emit($event)"></app-invoice-button>
            <button (click)="moveSelected.emit()" [disabled]="selection.isEmpty()"
                    mat-icon-button matTooltip="Местене на избраните"
                    matTooltipPosition="above">
               <mat-icon>move_up</mat-icon>
            </button>
         }
      </th>
      <td *matCellDef="let element" mat-cell>
         <span [ngClass]="element.klass">{{element.name}}</span>
      </td>
   </ng-container>
   <ng-container matColumnDef="description">
      <th *matHeaderCellDef mat-header-cell>
         Допълнителна информация
      </th>
      <td *matCellDef="let element" mat-cell>
         @if (!!element.item.description) {
            <div>{{element.item.description}}</div>
         }
         <div>{{element.description}}</div>
      </td>
   </ng-container>
   <mat-text-column headerText="Количество" name="quantity"/>
   <mat-text-column headerText="Сума" name="price"/>

   <ng-container matColumnDef="actions">
      <th *matHeaderCellDef mat-header-cell>Действия</th>
      <td *matCellDef="let element" mat-cell>
         @if (!element.isPurchase || element.item.isManual) {
            <button [matMenuTriggerData]="{element}" [matMenuTriggerFor]="itemActions"
                    mat-icon-button>
               <mat-icon>more_vert</mat-icon>
            </button>
         }
      </td>
   </ng-container>


   <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
   <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
</table>
<h2 style="text-align: right;">Баланс: {{balance | money}}</h2>

<mat-menu #itemActions>
   <ng-template let-elem="element" matMenuContent>
      <button (click)="emitEdit(elem)" mat-menu-item>
         <mat-icon>edit</mat-icon>
         Редактирай
         @if (elem.isPurchase) {
            начисление
         } @else {
            плащане
         }
      </button>
      <button (click)="delete(elem)" mat-menu-item>
         <mat-icon color="warn">delete</mat-icon>
         Изтрий
         @if (elem.isPurchase) {
            начисление
         } @else {
            плащане
         }
      </button>
   </ng-template>
</mat-menu>

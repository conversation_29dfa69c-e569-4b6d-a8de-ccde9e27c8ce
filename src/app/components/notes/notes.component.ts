import {Component, EventEmitter, inject, Input, OnInit, Output} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';
import {ID} from '../../data/identifiable';
import {Note} from '../../data/notes';
import {NotesService} from '../../services/notes.service';
import {NotificationService} from '../../services/notification.service';
import {switchMap} from 'rxjs';

@Component({
   selector: 'app-notes',
   templateUrl: './notes.component.html',
   styles: `
      .button {
         position: relative;
         bottom: -4px;
      }
   `,
   standalone: false
})
export class NotesComponent implements OnInit {
   @Input({required: true}) parent!: ID;
   @Output() addedNote = new EventEmitter<Note>();

   notes: Note[] = [];
   newNote = new UntypedFormControl('');

   private sNotes = inject(NotesService);
   private sNotification = inject(NotificationService);

   ngOnInit() {
      if (this.parent) {
         this.renewData();
      }
   }

   createNote() {
      const note = this.newNote.value;
      if (note) {
         this.sNotes.add({content: note, parent: this.parent})
            .subscribe(newNote => this.addNote(newNote));
      }
   }

   deleteNote(note: Note) {
      this.sNotification.openConfirmationDialog({
         title: 'Премахване на бележка',
         description: 'Наистина ли искате да премахнете тази бележка?',
      }).pipe(
         switchMap(() => this.sNotes.delete(note.id))
      ).subscribe(() => this.renewData());
   }

   private renewData() {
      this.sNotes.getByParent(this.parent).subscribe(notes => {
         this.notes = notes.reverse();
      });
   }

   private addNote(note: Note) {
      this.notes.unshift(note);
      this.addedNote.emit(note);
      this.newNote.setValue('');
   }
}

import {Component, inject, input, OnDestroy, OnInit, output} from '@angular/core';
import {LicenseService} from '../../services/license.service';
import {AgentService} from '../../services/agent.service';
import {catchError, forkJoin, Observable, of, Subscription, tap, throwError} from 'rxjs';
import {filter, switchMap} from 'rxjs/operators';
import {Customer} from '../../data/customers/customer';
import {IdScannerData, IdScannerDataType} from '../../data/agent/id-scanner';
import {gemaltoToCustomer} from '../../data/agent/gemalto';
import {NotificationService} from '../../services/notification.service';
import {MatDialog, MatDialogRef} from '@angular/material/dialog';
import {
   IdScannerResultAction,
   IdScannerResultDialogComponent,
   IdScannerResultDialogData,
} from '../../dialogs/id-scanner-result-dialog.component';
import {CustomerService} from '../../services/customer.service';

export enum IdScannerUseCase {
   reservation,
   confirm,
}

export interface IdScannerResult {
   customer: Customer;
   action: IdScannerResultAction;
}

@Component({
   selector: 'app-id-scanner-icon',
   template: `
      @if (enabled) {
         <mat-icon [matTooltip]="tooltip()" color="primary">
            document_scanner
         </mat-icon>
      }
   `,
   standalone: false
})
export class IdScannerIconComponent implements OnInit, OnDestroy {
   tooltip = input<string>();
   useCase = input<IdScannerUseCase>(IdScannerUseCase.confirm);

   scannerResult = output<IdScannerResult>();

   protected enabled = inject(LicenseService).idScanner();

   private sAgent = inject(AgentService);
   private sCustomer = inject(CustomerService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);
   private dialogRef?: MatDialogRef<IdScannerResultDialogComponent>;
   private sub?: Subscription;

   ngOnInit(): void {
      if (this.enabled) {
         this.sub = this.sAgent.idScanner.pipe(
            switchMap(customer => this.handleIdScanner(customer).pipe(
               catchError(e => {
                  this.sNotification.displayError(e, 'Грешка при четене на лични данни');
                  return of(null);
               })
            )),
         ).subscribe(result => result && this.scannerResult.emit(result));
      }
   }

   ngOnDestroy(): void {
      this.sub?.unsubscribe();
   }

   private handleIdScanner(data: IdScannerData): Observable<IdScannerResult> {
      if (!data.ok) {
         return throwError(() => Error(data.error));
      }

      if (data.type === IdScannerDataType.Gemalto) {
         const customer = gemaltoToCustomer(data.data);

         const dialogData: IdScannerResultDialogData = {
            customer: customer,
            useCase: this.useCase(),
         };

         if (this.dialogRef) {
            this.dialogRef.close(false);
         }

         this.dialogRef = this.dialog.open(IdScannerResultDialogComponent, {
            data: dialogData
         });

         return this.dialogRef.afterClosed().pipe(
            tap(() => this.dialogRef = undefined),
            filter((action: IdScannerResultAction) => !!action),
            switchMap(action => forkJoin({
               action: of(action),
               customer: this.sCustomer.saveScanned(customer),
            })),
         );
      }

      return throwError(() => Error('Непознат тип данни от четеца!'));
   }
}

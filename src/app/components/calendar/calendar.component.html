<div [@fade500] class="mat-app-background">
   <div class="calendar-header mat-app-background" data-event-category="calendar_header">
      <div>
         <button (click)="shiftDates(-7)"
                 mat-icon-button matTooltip="7 дни назад" matTooltipPosition="above">
            <mat-icon>keyboard_double_arrow_left</mat-icon>
         </button>
         <button (click)="shiftDates(-1)"
                 mat-icon-button matTooltip="1 ден назад" matTooltipPosition="above">
            <mat-icon>keyboard_arrow_left</mat-icon>
         </button>
         <button (click)="setDefaultDates()" class="align-icons" mat-stroked-button>
            Днес
         </button>
         <button (click)="shiftDates(1)"
                 mat-icon-button matTooltip="1 ден напред" matTooltipPosition="above">
            <mat-icon>keyboard_arrow_right</mat-icon>
         </button>
         <button (click)="shiftDates(7)"
                 mat-icon-button matTooltip="7 дни напред" matTooltipPosition="above">
            <mat-icon>keyboard_double_arrow_right</mat-icon>
         </button>
      </div>
      @if (activeAddons) {
         <div [@fade500]>
            @if (creatingGroup) {
               <button (click)="addGroupReservation()" [disabled]="!groupDays.size"
                       mat-raised-button class="align-icons" color="primary">
                  Направи групова резервация
               </button>
            } @else if (swappingRooms) {
               <button (click)="swapSelectedRooms()"
                       [disabled]="!reservationSelection.selected.length"
                       mat-raised-button class="align-icons" color="primary">
                  Размени избраните стаи
               </button>
            }
            <button (click)="clearAddons()" color="warn" mat-icon-button>
               <mat-icon>cancel</mat-icon>
            </button>
         </div>
      }
      <div class="date-range-container">
         <mat-form-field class="date-range-input">
            <mat-label>Период</mat-label>
            <mat-date-range-input [rangePicker]="picker">
               <input [value]="dateStart()" matStartDate placeholder="Начална дата">
               <input [value]="dateEnd()" matEndDate placeholder="Крайна дата">
            </mat-date-range-input>
            <mat-datepicker-toggle [for]="picker" matSuffix/>
            <mat-date-range-picker #picker/>
         </mat-form-field>
         @if (extended) {
            <button (click)="setNormalDays()" mat-icon-button>
               <mat-icon>close_fullscreen</mat-icon>
            </button>
         } @else {
            <button (click)="setExtendedDays()" mat-icon-button>
               <mat-icon>open_in_full</mat-icon>
            </button>
         }
         <button [matMenuTriggerFor]="actionsMenu" mat-icon-button>
            <mat-icon>more_vert</mat-icon>
         </button>
      </div>
   </div>

   <div class="table">
      <div class="row sticky-row">
         <div class="grid header-grid">
            <div class="cell header-text filler-cell border-cell mat-app-background">
               @if (checkingIn.length) {
                  <button (click)="nextCheckIn()" mat-stroked-button>
                     <mat-icon>luggage</mat-icon>
                     <span>{{checkingIn.length}}</span>
                  </button>
               }
               @if (checkingOut.length && !(checkingIn.length && extended)) {
                  <button (click)="nextCheckOut()" mat-stroked-button>
                     <mat-icon>logout</mat-icon>
                     <span>{{checkingOut.length}}</span>
                  </button>
               }
            </div>
            @for (day of days(); track day) {
               <div [class.highlight]="isToday(day)"
                    class="cell border-cell header-cell header-text no-select mat-app-background">
                  <span>{{day.toFormat("dd MMMM")}}</span>
                  <span>{{day.toFormat("EEE")}}</span>
               </div>
            }
            <!--
               Every grid needs an absolutely positioned element in the end, otherwise
               the cells don't align properly. This happens because "flex: 1 1 0" does not
               calculate equal widths for .grids without an absolutely positioned element
               and for those that have it.
            -->
            <div style="position: absolute;"></div>
         </div>
      </div>
      @for (room of rooms; track room.id) {
         <div class="row">
            <div class="grid">
               <div (click)="roomSelection.toggle(room)" [id]="room.id"
                    class="cell border-cell header-cell room-cell">
                  @if (!extended) {
                     <span>
                        {{roomCapacities[$index]}}
                        <mat-icon inline>groups</mat-icon>
                     </span>
                  }
                  <span class="no-select">{{room.name}}</span>
                  @if (swappingRooms) {
                     <mat-checkbox [checked]="roomSelection.isSelected(room)"/>
                  } @else if (room.cleaning) {
                     @if (getCleaningNotes(room.id); as cleaningNotes) {
                        <div
                           [matTooltip]="room.cleaning.name + '\n' + (cleaningNotes | notes)"
                           class="red cube" matTooltipClass="multi-line"
                           matTooltipPosition="right"></div>
                     } @else {
                        <div [matTooltip]="room.cleaning.name" class="red cube"
                             matTooltipPosition="right"></div>
                     }

                  } @else {
                     <div class="green cube"></div>
                  }
               </div>
               @for (day of days(); track day) {
                  <div
                     (click)="duplicateFirstInGroup($event, room)"
                     (dblclick)="creatingGroup || addReservation(day.plus({seconds: 1}),day.plus({days: 1}), room)"
                     (dragend)="ondragend()"
                     (dragenter)="$event.preventDefault()"
                     (dragover)="$event.preventDefault()"
                     (dragstart)="ondragstart($event, room, day)"
                     [class.highlight]="isToday(day)"
                     class="cell border-cell"
                     draggable="true"></div>
               }
               <div style="position: absolute"></div>
               <ng-template [roomId]="room.id" appReservationContainer/>
            </div>
         </div>
      }
   </div>
</div>

<ng-container appViewContainerRef/>

<ng-template #reservationCard let-notes="notes" let-payments="payments"
             let-purchases="purchases"
             let-res="reservation" let-styles="styles">
   <div (click)="creatingGroup || openBrief($event, res)" [@fade500]
        [class.clickable]="!creatingGroup" [ngClass]="styles"
        class="reservation-card mat-elevation-z1">
      <div class="reservation-card-content">
         @if (!res.isLeisure) {
            <span class="no-select">{{res.titular | formatCustomer:false}}</span>
         }
         @if (notes && notes.length > 0) {
            <app-info-icon immediate [tooltip]="notes | notes" tooltipClass="multi-line"/>
         }
      </div>
      <div class="reservation-indicators">
         @if (!swappingRooms) {
            @if (res.color && !res.isLeisure) {
               <div [matTooltip]="getResStatusName(res.status)" [ngClass]="res.status"
                    class="status-box" matTooltipPosition="above" matTooltipShowDelay="0">
               </div>
            }
            @if (payments) {
               <span class="balance-item top-left-round light-green">{{payments}}</span>
            }
            @if (purchases) {
               <span class="balance-item bottom-right-round red">{{purchases}}</span>
            }
         } @else if (res.status === rs.pending) {
            <mat-checkbox [checked]="reservationSelection.isSelected(res)"/>
         }
      </div>
   </div>
</ng-template>

<ng-template #roomBlock let-room="room" let-styles="styles">
   <div [@fade500] [ngClass]="styles" class="reservation-card bold striped
         mat-app-background block-card mat-elevation-z1 no-select">
      <span class="mat-app-background" style="padding: 2px 6px; border-radius: 4px;">
         {{room.blocked.reason}}
      </span>
   </div>
</ng-template>

<ng-template #reservationPreview let-isGroup="isGroup" let-viewRef="viewRef">
   <div class="Pending reservation-card mat-elevation-z1">
      <div class="reservation-card-content"></div>
      @if (isGroup) {
         <button (click)="removeFromGroup(viewRef)" mat-icon-button>
            <mat-icon>delete</mat-icon>
         </button>
      }
   </div>
</ng-template>

<ng-template #reservationBrief let-res="reservation">
   <div (click)="$event.stopPropagation()"
        class="reservation-brief mat-app-background mat-elevation-z19">
      <div class="brief-top-bar">
         @if (res.status !== rs.completed) {
            <button (click)="viewReservation(res)" mat-icon-button
                    matTooltip="Редактиране" matTooltipPosition="above">
               <mat-icon>edit</mat-icon>
            </button>
         }
         @if (res.status === rs.completed) {
            <button (click)="viewReservation(res, true)" mat-icon-button
                    matTooltip="Детайли" matTooltipPosition="above">
               <mat-icon>open_in_new</mat-icon>
            </button>
         }
         <button (click)="viewHistory(res)" mat-icon-button
                 matTooltip="История на промените" matTooltipPosition="above">
            <mat-icon>history</mat-icon>
         </button>
         <button (click)="viewBalance(res, false)" mat-icon-button
                 matTooltip="Начисления и плащания" matTooltipPosition="above">
            <mat-icon>attach_money</mat-icon>
         </button>
         @if (res.status === rs.pending) {
            <button (click)="checkIn(res)" [disabled]="disableCheckIn(res.start)"
                    mat-icon-button matTooltip="Настаняване" matTooltipPosition="above">
               <mat-icon>luggage</mat-icon>
            </button>
         }
         @if (res.status === rs.ongoing) {
            <button (click)="checkOut(res)" [disabled]="!isToday(res.end)"
                    mat-icon-button matTooltip="Напускане" matTooltipPosition="above">
               <mat-icon>logout</mat-icon>
            </button>
         }
         @if (res.status === rs.pending) {
            <button (click)="cancelReservation(res)" mat-icon-button
                    matTooltip="Анулиране"
                    matTooltipPosition="above">
               <mat-icon>cancel</mat-icon>
            </button>
         }
         <button [matMenuTriggerData]="{res}" [matMenuTriggerFor]="briefActions"
                 mat-icon-button>
            <mat-icon>more_vert</mat-icon>
         </button>
         <button (click)="closeBrief()" mat-icon-button matTooltip="Затваряне"
                 matTooltipPosition="above">
            <mat-icon>close</mat-icon>
         </button>
      </div>
      <div class="brief-content">
         <div class="brief-data-item">
            <div class="brief-data-item-header">
               <mat-icon>tag</mat-icon>
            </div>
            <div class="brief-data-item-content">
               {{res.serialNumber}}
            </div>
         </div>
         <div class="brief-data-item">
            <div class="brief-data-item-header">
               <div [ngClass]="res.status" class="status-box"></div>
            </div>
            <div class="brief-data-item-content">
               {{getResStatusName(res.status)}}
            </div>
         </div>
         <div class="brief-data-item">
            <div class="brief-data-item-header">
               <mat-icon>person</mat-icon>
            </div>
            <div class="brief-data-item-content">
               <div>
                  {{res.titular | formatCustomer}}
                  @if (res.titular.contact.notes; as notes) {
                     <mat-icon [matTooltip]="notes" inline>info_outlined</mat-icon>
                  }
               </div>
               <div>
                  {{res.start.toFormat('dd MMMM')}} - {{res.end.toFormat('dd MMMM')}}
               </div>
            </div>
         </div>
         <div class="brief-data-item">
            <div class="brief-data-item-header">
               <mat-icon>room</mat-icon>
            </div>
            <div class="brief-data-item-content">
               {{res.room.name}}
            </div>
         </div>
         @if (res.balance.amount !== 0) {
            <div class="brief-data-item">
               <div class="brief-data-item-header">
                  <mat-icon>payments</mat-icon>
               </div>
               <div class="brief-data-item-content">
                  Дължима сума: {{res.balance | money}}
               </div>
            </div>
         }
         @if (res.notes && res.notes.length > 0) {
            <div class="brief-data-item">
               <div class="brief-data-item-header">
                  <mat-icon>notes</mat-icon>
               </div>
               <div class="brief-data-item-content">
                  Бележки
               </div>
            </div>
            <div class="flex-row">
               <div class="brief-data-item-header"></div>
               <ul class="notes-list brief-data-item-content">
                  @for (note of res.notes; track note.id) {
                     <li>
                        @if (note.creator; as creator) {
                           <b>{{creator.name}}</b>
                        }
                        {{note.content}}
                     </li>
                  }
               </ul>
            </div>
         }
         @if (res.surveyAnswers; as answers) {
            <div class="flex-row">
               <div class="brief-data-item-header">
                  <mat-icon>poll</mat-icon>
               </div>
               <div class="brief-data-item-content">
                  <mat-expansion-panel class="mat-elevation-z0 mat-app-background">
                     <mat-expansion-panel-header>
                        <mat-panel-title>
                           <app-star-rating [rating]="answers.average"/>
                           @if (answers.comments) {
                              <app-info-icon [tooltip]="answers.comments"
                                             tooltipPrefix="Коментар: " immediate
                                             style="margin-left: 16px;"/>
                           }
                        </mat-panel-title>
                     </mat-expansion-panel-header>
                     @for (answer of answers.answers | keyvalue; track answer.key) {
                        <p class="flex-row jc-space-between">
                           <span style="margin-right: 16px;">{{answer.key}}</span>
                           <span>{{answer.value}}</span>
                        </p>
                     }
                  </mat-expansion-panel>
               </div>
            </div>
         }
      </div>
   </div>
</ng-template>

<mat-menu #actionsMenu>
   <button (click)="startGroupReservation()" mat-menu-item>
      <mat-icon>group_add</mat-icon>
      Групова резервация
   </button>
   <button (click)="searchReservation()" mat-menu-item>
      <mat-icon>search</mat-icon>
      Търсене на резервация
   </button>
   <button (click)="startRoomSwap()" mat-menu-item>
      <mat-icon>loop</mat-icon>
      Размяна на резервации
   </button>
</mat-menu>

<mat-menu #briefActions>
   <ng-template let-res="res" matMenuContent>
      <mat-list>
         @if (sLicense.guestApp()) {
            <button (click)="inviteToGuestApp(res)" mat-menu-item>
               <mat-icon>phone_iphone</mat-icon>
               Покани в приложението
            </button>
         }
         <button (click)="deleteReservation(res)" mat-menu-item>
            <mat-icon color="warn">delete</mat-icon>
            Изтрий
         </button>
      </mat-list>
   </ng-template>
</mat-menu>

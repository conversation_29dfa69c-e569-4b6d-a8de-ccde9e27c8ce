.hide-overflow {
   text-overflow: clip;
   overflow: hidden;
   white-space: nowrap;
}

// 7 days resolution
@for $i from 1 through 7 {
   .p#{$i}-7 {
      left: calc(100vw / 8 * #{$i} + 7px);
   }
   .d#{$i}-7 {
      width: calc(100vw / 8 * #{$i} - 32px);
      @if $i < 4 {
         @extend .hide-overflow;
      }
   }
}

// Leisure reservations
.d-l-7 {
   width: calc(100vw / 8 - 96px);
   z-index: 1;
   @extend .hide-overflow;
}

// Styles for dragging backwards on the 7 days resolution
.d0-7 {
   transform: translateX(calc(-100vw / 8));
   @extend .d2-7;
}

@for $i from 1 through 5 {
   .d-#{$i}-7 {
      transform: translateX(calc(-100vw / 8 * #{$i+1}));
      @extend .d#{$i+2}-7
   }
}

// 14 days resolution
@for $i from 1 through 14 {
   .p#{$i}-14 {
      left: calc(100vw / 15 * #{$i} + 7px);
   }
   .d#{$i}-14 {
      width: calc(100vw / 15 * #{$i} - 32px);
      @if $i < 7 {
         @extend .hide-overflow;
      }
   }
}

// Styles for dragging backwards on the 14 days resolution
.d0-14 {
   transform: translateX(calc(-100vw / 15));
   @extend .d2-14;
}

@for $i from 1 through 12 {
   .d-#{$i}-14 {
      transform: translateX(calc(-100vw / 15 * #{$i+1}));
      @extend .d#{$i+2}-14
   }
}

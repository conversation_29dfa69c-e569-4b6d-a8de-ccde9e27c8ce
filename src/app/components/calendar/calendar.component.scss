@use 'reservation-positioning';

.calendar-header {
   padding: 0 50px 0 120px;
   height: 75px;
   display: flex;
   align-items: center;
   justify-content: space-between;

   position: sticky;
   top: 0;
   z-index: 3;

   .date-range-container {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .date-range-input {
         width: 230px;
         margin-right: 16px;
      }

      & > * {
         transition: transform 0.5s;
      }
   }
}

.table {
   width: 100vw;
   max-width: 100%;
}

.grid {
   display: flex;
   justify-content: space-between;
   height: 40px;
}

.grid.header-grid {
   height: 50px;
}

.row {
   position: relative; // Needed for correct positioning of the reservation cards

   &:last-child .cell {
      border-bottom: none;
   }

   &:nth-child(2) .room-cell {
      border-top: none;
   }
}

.sticky-row {
   position: sticky;
   top: 75px;
   z-index: 1;
}

.header-text {
   font-size: 13px;
   font-weight: 500;
}

.header-cell {
   display: flex;
   flex-direction: column;
   justify-content: center;
   align-items: center;
}

.cell {
   flex: 1 1 0;

   &:last-child {
      border-right: none;
   }
}

.border-cell {
   border-right: 1px solid var(--cell-border-color);
   border-bottom: 1px solid var(--cell-border-color);
}

.filler-cell {
   border-right: 1px solid var(--cell-border-color);

   display: flex;
   flex-direction: row;
   justify-content: space-evenly;
   align-items: center;
}

.room-cell {
   width: 100%;
   display: flex;
   flex-direction: row;
   justify-content: space-evenly;
   scroll-margin-top: 125px;
}

.blink {
   animation: blinker 1s linear infinite;
}

.reservation-card {
   align-self: center;

   display: flex;
   align-items: center;
   justify-content: space-between;
   position: absolute;

   height: 74%;
   border-radius: 4px;

   padding: 0 8px;

   transition: width 70ms linear, transform 70ms linear;

   .reservation-card-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
   }

   .reservation-indicators {
      position: absolute;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
   }

   .balance-item {
      position: relative;
      top: 6px;
      padding: 2px;
      font-size: 0.8em;
      line-height: 1.2em;
      font-weight: bold;
      color: black;
   }

   .score {
      margin-right: 8px;
   }

   .top-left-round {
      border-top-left-radius: 4px;
   }

   .bottom-right-round {
      border-bottom-right-radius: 4px;
   }
}

.block-card {
   border: 0;
}

.hidden {
   display: none;
}

.reservation-brief {
   position: absolute;
   width: calc(33% - 28px);
   padding: 10px;

   border-radius: 8px;
   z-index: 4;
}

.brief-top-bar {
   display: flex;
   justify-content: flex-end;
   align-items: center;

   & > button:last-child {
      margin-left: 14px;
   }
}

.brief-data-item {
   display: flex;
   height: 44px;
}

.brief-data-item-header {
   display: flex;
   justify-content: center;
   align-items: center;

   flex: 1;
}

.brief-data-item-content {
   display: flex;
   flex-direction: column;
   align-items: flex-start;
   justify-content: center;

   flex: 4;
}

.notes-list {
   margin: 0;
   padding: 0 0 0 16px;
}

.status-box {
   width: 16px;
   height: 16px;
   border-radius: 2px;
   margin-right: 4px;
}

.dot {
   width: 16px;
   height: 16px;
   border-radius: 50%;
   justify-self: flex-end;
}

.cube {
   width: 16px;
   height: 16px;
   border-radius: 16%;
}

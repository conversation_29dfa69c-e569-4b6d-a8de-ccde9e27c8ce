import {AfterViewInit, Component, inject, OnInit, ViewChild} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';
import {FormControl} from '@angular/forms';
import {filter, map, switchMap} from 'rxjs/operators';
import {MatPaginator} from '@angular/material/paginator';
import {MatDialog} from '@angular/material/dialog';
import {
   CustomerDialogComponent,
   CustomerDialogData
} from '../dialogs/customer-dialog/customer-dialog.component';
import {
   Customer,
   CustomerType,
   fullName,
   matchCustomerProperty
} from '../data/customers/customer';
import {CustomerService} from '../services/customer.service';
import {
   BalanceDialogComponent,
   BalanceDialogData,
   FinancialContext
} from '../dialogs/balance-dialog/balance-dialog.component';
import {fullScreenDialogOptions} from '../utility/dialog-utility';
import {FinancialAccountService} from '../services/financial-account.service';
import {of, throwError} from 'rxjs';
import {NotificationService} from '../services/notification.service';
import {FinancialAccount, FinancialAccountChain} from '../data/financial-account';
import {FinancialAccountPipe} from '../pipes/financial-account.pipe';
import {
   CustomerAccountsDialogComponent
} from '../dialogs/customer-accounts-dialog/customer-accounts-dialog.component';

@Component({
   selector: 'app-customers',
   templateUrl: './customers.component.html',
   standalone: false
})
export class CustomersComponent implements OnInit, AfterViewInit {
   @ViewChild(MatPaginator) paginator!: MatPaginator;

   displayedColumns: string[] = ['name', 'actions'];
   dataSource = new MatTableDataSource<Customer>();

   filterInput = new FormControl();
   typeFilter: CustomerType | null = CustomerType.individual;
   protected customerType = CustomerType;
   private sCustomer = inject(CustomerService);
   private sAccounts = inject(FinancialAccountService);
   private dialog = inject(MatDialog);
   private sNotification = inject(NotificationService);

   ngOnInit(): void {
      this.renewData();
      this.filterInput.valueChanges.subscribe(fltr => {
         this.typeFilter = (fltr && fltr.trim()) ? null : CustomerType.individual;
         const filterObject = {type: this.typeFilter, text: fltr};
         this.dataSource.filter = JSON.stringify(filterObject);
      });
   }

   ngAfterViewInit(): void {
      this.dataSource.filterPredicate = this.customerFilterPredicate();
      this.dataSource.paginator = this.paginator;
      this.filterInput.setValue(null);
   }

   protected applyFilter(filterValue: CustomerType) {
      if (!this.filterInput.value) {
         this.typeFilter = filterValue;
         const filterObject = {type: this.typeFilter, text: null};
         this.dataSource.filter = JSON.stringify(filterObject);
      }
   }

   protected openCustomerDialog(customer?: Customer) {
      const data: CustomerDialogData = {
         data: customer,
         edit: !!customer,
         allowDelete: customer?.type == CustomerType.individual
      };

      const dialog = this.dialog.open(CustomerDialogComponent, {
         data,
         closeOnNavigation: true,
      });

      dialog.afterClosed().pipe(filter(result => !!result))
         .subscribe(() => this.renewData());
   }

   protected openAccounts(customer: Customer) {
      this.sAccounts.ofTitular(customer.id).pipe(
         switchMap(accounts => {
            const activeAccount = accounts.find(account => !account.deactivatedAt);
            if (!activeAccount) {
               return this.sNotification.openConfirmationDialog({
                  title: 'Отваряне на сметка',
                  description: `${fullName(
                     customer)} няма активна сметка. Искате ли да отворите нова сметка?`,
               }).pipe(
                  switchMap(() => this.sAccounts.add({titular: customer})),
                  map(account => [account, ...accounts])
               );
            } else {
               return of(accounts);
            }
         }),
         switchMap(accounts => {
            const activeAccounts = accounts.filter(account => !account.deactivatedAt);
            const closedAccounts = accounts.filter(account => !!account.deactivatedAt);
            if (activeAccounts.length === 0) {
               return throwError(() => Error('Missing active account'));
            } else if (activeAccounts.length == 1) {
               const accountChain = {
                  id: customer.id,
                  activeAccount: activeAccounts[0],
                  accounts: closedAccounts
               } as FinancialAccountChain;
               return of(accountChain);
            } else {
               const data = {
                  customer,
                  accounts: activeAccounts.sort((lhs: FinancialAccount,
                                                 rhs: FinancialAccount) => lhs.createdAt!.valueOf() -
                     rhs.createdAt!.valueOf())
               };
               return this.dialog.open(CustomerAccountsDialogComponent, {data})
                  .afterClosed().pipe(
                     filter(account => !!account),
                     switchMap(selectedAccount => {
                        const accountChain = {
                           id: customer.id,
                           activeAccount: selectedAccount,
                           accounts: closedAccounts
                        } as FinancialAccountChain;
                        return of(accountChain);
                     })
                  );
            }
         }),
         switchMap(accountChain => {
            const data: BalanceDialogData = {
               accountChain,
               movementAllowed: true,
               context: FinancialContext.customer,
               subtitle: FinancialAccountPipe.toString(accountChain.activeAccount, true)
            };

            return this.dialog.open(BalanceDialogComponent, {
               data,
               ...fullScreenDialogOptions
            }).afterClosed();
         })
      ).subscribe();
   }

   private renewData(): void {
      setTimeout(() =>
         this.sCustomer.getAll().subscribe(
            cs => this.dataSource.data = cs.filter(c => !c.isForgotten)), 0);
   }

   private customerFilterPredicate() {
      return (customer: Customer, fltr: string): boolean => {
         const {type, text} = JSON.parse(fltr);
         const matchesText =
            !text || matchCustomerProperty(customer, text.trim().toLowerCase());

         const matchesType =
            !type || // No type filter when text filter is active
            (type == customer.type && !customer.isForgotten);

         return matchesText && matchesType;
      };
   }
}

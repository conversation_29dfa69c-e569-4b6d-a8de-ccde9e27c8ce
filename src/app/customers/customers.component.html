<div class="main-view-header">
   <div style="width: 144px"></div>

   <mat-form-field class="search">
      <mat-label>Търсене</mat-label>
      <input [formControl]="filterInput" autocomplete="off" matInput
             placeholder="Име, телефон, имейл или ЕГН/БУЛСТАТ">
      <mat-icon matSuffix>search</mat-icon>
   </mat-form-field>

   <button (click)="openCustomerDialog()" appEvent="add_customer" color="primary"
           data-event-category="customers"
           extended mat-fab>
      <mat-icon>person_add</mat-icon>
      Нов клиент
   </button>
</div>

<div class="filter-group" style="display: flex; justify-content: center; margin: 10px">
   <mat-button-toggle-group (change)="applyFilter($event.value)" [(value)]="typeFilter"
                            [disabled]="filterInput.value?.length > 0">
      <mat-button-toggle [value]="customerType.individual">
         Физически лица
      </mat-button-toggle>
      <mat-button-toggle [value]="customerType.legal">
         Юридически лица
      </mat-button-toggle>
      <mat-button-toggle [value]="customerType.service">
         Служебни лица
      </mat-button-toggle>
   </mat-button-toggle-group>
</div>

<div class="main-view-table mat-elevation-z3">
   <table [dataSource]="dataSource" mat-table>
      <!-- Name Column -->
      <ng-container matColumnDef="name">
         <th *matHeaderCellDef mat-header-cell>Клиент</th>
         <td *matCellDef="let element" mat-cell>
            {{element | formatCustomer}}
            @if (element.contact.notes; as notes) {
               <mat-icon [matTooltip]="notes" inline>info_outlined</mat-icon>
            }
         </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
         <th *matHeaderCellDef class="two-action-column" mat-header-cell></th>
         <td *matCellDef="let customer" mat-cell>
            <button (click)="openCustomerDialog(customer)" mat-icon-button>
               <mat-icon>manage_accounts</mat-icon>
            </button>
            <button (click)="openAccounts(customer)" mat-icon-button>
               <mat-icon>attach_money</mat-icon>
            </button>
         </td>
      </ng-container>

      <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns;" mat-row></tr>

      <tr *matNoDataRow>
         <td [colSpan]="displayedColumns.length" class="text-center">
            Няма елементи за визуализация
            @if (filterInput?.value) {
               <p (click)="filterInput.setValue(null)" class="primary-text clickable">
                  Премахни филтъра "{{filterInput.value}}"
               </p>
            }
         </td>
      </tr>
   </table>

   <mat-paginator [pageSizeOptions]="[20, 50, 100]" showFirstLastButtons/>
</div>

import {Pipe, PipeTransform} from '@angular/core';
import {DateRange} from '../data/common';
import {rangesToString} from '../utility/utility';
import {PricingRange} from '../data/pricing';

@Pipe({
   name: 'dateRanges',
   standalone: false
})
export class DateRangesPipe implements PipeTransform {
   transform(ranges: PricingRange[] | DateRange): string {
      if (Array.isArray(ranges)) {
         return rangesToString(ranges.map(r => r.dateRange));
      } else {
         return rangesToString([ranges]);
      }
   }
}

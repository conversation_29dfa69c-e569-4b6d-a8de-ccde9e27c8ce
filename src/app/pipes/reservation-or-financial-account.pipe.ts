import {Pipe, PipeTransform} from '@angular/core';
import {Reservation, ReservationInfo} from '../data/reservation';
import {FinancialAccount} from '../data/financial-account';
import {ReservationPipe} from './reservation.pipe';
import {FinancialAccountPipe} from './financial-account.pipe';

@Pipe({
   name: 'reservationOrFinancialAccount',
   standalone: false
})
export class ReservationOrFinancialAccountPipe implements PipeTransform {
   transform(value?: Reservation | ReservationInfo | FinancialAccount): string {
      if (!value) {
         return '';
      }

      return 'start' in value ? ReservationPipe.toString(value) :
         FinancialAccountPipe.toString(value);
   }
}

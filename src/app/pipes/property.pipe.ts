import {Pipe, PipeTransform} from '@angular/core';
import {cmp} from '../utility/utility';

@Pipe({
   name: 'property',
   standalone: true
})
export class PropertyPipe implements PipeTransform {
   transform(value: any, property: string | ((obj: any) => any)): any {
      const result = property instanceof Function ? property(value) : value[property];
      return result instanceof Array ? result.sort(cmp) : [result];
   }
}

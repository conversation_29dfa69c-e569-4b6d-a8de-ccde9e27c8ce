import {Pipe, PipeTransform} from '@angular/core';
import {Customer, CustomerType, fullName} from '../data/customers/customer';
import {secretId} from '../utility/utility';

@Pipe({
   name: 'formatCustomer',
   standalone: false
})
export class FormatCustomerPipe implements PipeTransform {
   static toString(customer?: Customer | null, long = true): string {
      if (!customer) {
         return '';
      }

      const name = fullName(customer);

      switch (customer.type) {
         case CustomerType.individual:
            return long && customer.idNumber ?
               `${name} (ЕГН: ${secretId(customer.idNumber)})` : name;
         case CustomerType.legal:
            return long && customer.idNumber ?
               `${name} (ЕИК: ${secretId(customer.idNumber)})` : name;
         default:
            return name;
      }
   }

   transform(customer: Customer | null, long: boolean = true): string {
      return FormatCustomerPipe.toString(customer, long);
   }
}

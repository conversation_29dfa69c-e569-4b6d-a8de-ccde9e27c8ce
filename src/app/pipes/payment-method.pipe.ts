import {Pipe, PipeTransform} from '@angular/core';
import {PaymentMethod} from '../data/payment';

@Pipe({
   name: 'paymentMethod',
   standalone: false
})
export class PaymentMethodPipe implements PipeTransform {
   static toString(method?: PaymentMethod): string {
      if (!method) {
         return '';
      }

      switch (method) {
         case PaymentMethod.cash:
            return 'В брой';
         case PaymentMethod.bank:
            return 'По банков път';
         case PaymentMethod.card:
            return 'С карта';
         case PaymentMethod.voucher:
            return 'Ваучер';
      }
   }

   transform(method?: PaymentMethod): string {
      return PaymentMethodPipe.toString(method);
   }
}

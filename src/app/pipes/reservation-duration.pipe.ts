import {Pipe, PipeTransform} from '@angular/core';
import {Reservation} from '../data/reservation';
import {getDuration} from '../utility/utility';

@Pipe({
   name: 'reservationDuration',
   standalone: false
})
export class ReservationDurationPipe implements PipeTransform {
   transform(res: Partial<Reservation>): unknown {
      return res.start && res.end ? getDuration(res.start, res.end) : 0;
   }
}

import {Pipe, PipeTransform} from '@angular/core';
import {Reservation, ReservationInfo} from '../data/reservation';
import {dateFmt} from '../utility/utility';
import {fullName} from '../data/customers/customer';

@Pipe({
   name: 'reservation',
   standalone: false
})
export class ReservationPipe implements PipeTransform {
   static toString(reservation?: Reservation | ReservationInfo) {
      if (!reservation) {
         return '';
      }

      const {serialNumber, titular, room, start, end} = reservation;
      return `(${serialNumber}) ${fullName(titular)} в стая ${room.name} от ${dateFmt(
         start)} до ${dateFmt(end)}`;
   }

   transform(reservation?: Reservation | ReservationInfo): string {
      return ReservationPipe.toString(reservation);
   }
}

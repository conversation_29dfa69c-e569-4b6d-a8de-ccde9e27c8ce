import {Pipe, PipeTransform} from '@angular/core';
import {FinancialAccount} from '../data/financial-account';
import {moneyToString} from '../utility/utility';
import {fullName} from '../data/customers/customer';

@Pipe({
   name: 'financialAccount',
   standalone: false
})
export class FinancialAccountPipe implements PipeTransform {
   static toString(account?: FinancialAccount, short: boolean = false): string {
      if (!account) {
         return '';
      }

      const {titular, balance} = account;
      const shortResult = `Сметка на ${fullName(titular)}`;

      if (short) {
         return shortResult;
      } else {
         return `${shortResult} с баланс ${moneyToString(balance)}`;
      }
   }

   transform(account?: FinancialAccount): string {
      return FinancialAccountPipe.toString(account);
   }
}

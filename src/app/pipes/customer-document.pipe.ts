import {Pipe, PipeTransform} from '@angular/core';
import {PersonDocumentType} from '../data/customers/customer';

@Pipe({
   name: 'customerDocument',
   standalone: false
})
export class CustomerDocumentPipe implements PipeTransform {
   transform(value: PersonDocumentType): string {
      switch (value) {
         case PersonDocumentType.card:
            return 'Лична карта';
         case PersonDocumentType.passport:
            return 'Парпорт';
         case PersonDocumentType.driverLicense:
            return 'Шофьорска книжка';
      }
   }
}

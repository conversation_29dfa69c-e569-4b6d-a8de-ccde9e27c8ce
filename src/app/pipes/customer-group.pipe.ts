import {Pipe, PipeTransform} from '@angular/core';
import {CustomerGroup} from '../data/customers/customer-group';

@Pipe({
   name: 'customerGroup',
   standalone: false
})
export class CustomerGroupPipe implements PipeTransform {
   transform(group: CustomerGroup, plural: boolean = false): string {
      return `${plural ? group.pluralName : group.singularName}` +
         (group.minAge === null && group.maxAge === null ? '' :
            ` (${group.minAge} - ${group.maxAge})`);
   }
}

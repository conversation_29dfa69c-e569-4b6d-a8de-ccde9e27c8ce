import {Pipe, PipeTransform} from '@angular/core';
import {VoucherDiscount} from '../data/voucher';
import {Money} from '../data/common';
import {moneyToString} from '../utility/utility';

@Pipe({
   name: 'voucher',
   standalone: true
})
export class VoucherPipe implements PipeTransform {
   transform({voucher}: VoucherDiscount): unknown {
      const {multiplier, price, name} = voucher;
      const total: Money = {amount: multiplier * price.amount, currency: price.currency};
      return `${multiplier} x ${name} (общо ${moneyToString(total)})`;
   }
}

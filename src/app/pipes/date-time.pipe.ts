import {Pipe, PipeTransform} from '@angular/core';
import {DateTime} from 'luxon';

@Pipe({
   name: 'dateTime',
   standalone: false
})
export class DateTimePipe implements PipeTransform {
   static toString(date?: number | DateTime, short = true): string {
      if (!date) {
         return '';
      }

      const format = short ? 'D' : 'tt на DD';
      const opts = {locale: 'bg'};

      if (DateTime.isDateTime(date)) {
         return date.toFormat(format, opts);
      } else {
         return DateTime.fromMillis(date).toFormat(format, opts);
      }
   }

   transform(date?: number | DateTime, short = true): string {
      return DateTimePipe.toString(date, short);
   }
}

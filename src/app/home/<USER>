import {Component, ViewChild} from '@angular/core';
import {AuthService} from '../auth/auth.service';
import {Router} from '@angular/router';
import {LoginFormComponent} from '../forms/login-form/login-form.component';
import {MatSnackBar} from '@angular/material/snack-bar';

@Component({
   selector: 'app-home',
   templateUrl: './home.component.html',
   styleUrls: ['./home.component.scss'],
   standalone: false
})
export class HomeComponent {
   @ViewChild(LoginFormComponent) loginForm!: LoginFormComponent;

   errMsg = '';

   constructor(public auth: AuthService,
               private snackbar: MatSnackBar,
               private router: Router) {
      if (this.auth.loggedIn()) {
         this.router.navigate(['reservation']).then();
      }
   }

   login() {
      if (!this.loginForm.valid) {
         return;
      }

      this.auth.login(this.loginForm.value).subscribe({
         next: () => {
            this.snackbar.dismiss();
            this.router.navigate(['reservation']).then();
         },
         error: () => this.errMsg = 'Грешно потребителско име или парола.'
      });
   }
}

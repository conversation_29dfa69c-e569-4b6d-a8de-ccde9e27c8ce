.home-unauthenticated {
   display: flex;
   flex-direction: row;

   height: calc(100vh - 64px);

   & > * {
      flex: 1 1 0;
   }

   & > .graphic-container {
      display: flex;
      justify-content: center;
      align-items: center;

      & > .circle {
         width: 300px;
         height: 300px;
         clip-path: circle(50% at 50% 50%);
         background: #2d99cf;
      }

      & > .letter-v {
         width: 300px;
         height: 300px;
         position: absolute;
         z-index: 1;
         clip-path: polygon(10% 0%, 0% 0%, 50% 100%, 100% 0%, 90% 0%, 50% 82%);
      }
   }

   & > .login-container {
      display: flex;
      justify-content: center;
      align-items: center;

      & > .login-form {
         display: flex;
         flex-direction: column;
         align-items: center;
      }
   }
}

/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;
@use 'styles/input';
@use 'styles/layout';

@use "@angular/material/prebuilt-themes/indigo-pink.css";

@use "normalize.css";
@use "assets/materialize-colors.scss";

$light-primary: mat.m2-define-palette(mat.$m2-blue-palette, 600, 100, 800);
$light-accent: mat.m2-define-palette(mat.$m2-blue-gray-palette);
$light-theme: mat.m2-define-light-theme((
   color: (
      primary: $light-primary,
      accent: $light-accent
   ),
));

$dark-primary: mat.m2-define-palette(mat.$m2-blue-palette, 300, 100, 500);
$dark-accent: mat.m2-define-palette(mat.$m2-blue-gray-palette);
$dark-theme: mat.m2-define-dark-theme((
   color: (
      primary: $dark-primary,
      accent: $dark-accent
   )
));

@include mat.all-component-colors($light-theme);

.dark-mode {
   @include mat.all-component-colors($dark-theme);
}

// Only final values, not SCSS functions!
:root {
   --cell-border-color: rgba(0, 0, 0, 0.12);

   .dark-mode {
      --cell-border-color: rgba(255, 255, 255, 0.12);
   }
}

html, body {
   height: 100%;
}

body {
   margin: 0;
   font-family: Roboto, "Helvetica Neue", sans-serif;
}

* {
   box-sizing: border-box;
   letter-spacing: normal !important;
}

.clickable {
   cursor: pointer;
}

.no-select {
   user-select: none;
}

.dark-text {
   color: rgba(0, 0, 0, 0.87);
}

.no-overflow {
   overflow: clip;
}

.text-center {
   text-align: center;
}

.max-width {
   width: 100%;
}

.bold {
   font-weight: bold;
}

.settings-header {
   display: flex;
   justify-content: space-between;

   &:not(:first-child) {
      margin-top: 24px;
   }

   .description {
      max-width: 55vw;
   }
}

// Child horizontal margin small, medium, and large
.child-hm-s > * {
   margin: auto 8px;
}

.child-hm-m > * {
   margin: auto 16px;
}

.child-hm-l > * {
   margin: auto 32px;
}

:is(.child-hm-s, .child-hm-m, .child-hm-l) > :first-child {
   margin-left: 0;
}

$highlight: mat.m2-get-color-from-palette($light-accent, 50);
$dark-highlight: mat.m2-get-color-from-palette(mat.$m2-gray-palette, 800);

.Pending {
   background-color: mat.m2-get-color-from-palette($light-primary, 100);
   color: mat.m2-get-contrast-color-from-palette($light-primary, 100);
}

.Ongoing {
   background-color: mat.m2-get-color-from-palette($light-primary, A400);
   color: mat.m2-get-contrast-color-from-palette($light-primary, A400);
}

.Completed {
   background-color: mat.m2-get-color-from-palette(mat.$m2-green-palette, 600);
   color: mat.m2-get-contrast-color-from-palette(mat.$m2-green-palette, 600);
}

.highlight {
   background-color: $highlight;
}

.primary-text {
   color: mat.m2-get-color-from-palette($light-primary, 600);
}

.accent-text {
   color: mat.m2-get-color-from-palette($light-accent, 500);
}

.warn-text {
   color: mat.m2-get-color-from-palette(mat.$m2-red-palette, 600);
}

.mat-success {
   background-color: mat.m2-get-color-from-palette(mat.$m2-green-palette, 600) !important;
   color: mat.m2-get-contrast-color-from-palette(mat.$m2-green-palette, 600) !important;
}

.striped {
   background-image: repeating-linear-gradient(-45deg, transparent 0 3px, #ccc 3px 6px);
}

.score-1 {
   border-left: 6px solid mat.m2-get-color-from-palette(mat.$m2-red-palette, 400);
}

.score-2 {
   @extend .score-1;
}

.score-3 {
   border-left: 6px solid mat.m2-get-color-from-palette(mat.$m2-yellow-palette, 500);
}

.score-4 {
   @extend .score-3;
}

.score-5 {
   border-left: 6px solid mat.m2-get-color-from-palette(mat.$m2-green-palette, 400);
}

.dark-mode {
   .Pending {
      background-color: mat.m2-get-color-from-palette(mat.$m2-light-blue-palette, A100);
      color: mat.m2-get-contrast-color-from-palette(mat.$m2-light-blue-palette, A100);
   }

   .Ongoing {
      background-color: mat.m2-get-color-from-palette($dark-primary, 800);
      color: mat.m2-get-contrast-color-from-palette($dark-primary, 800);
   }

   .Completed {
      background-color: mat.m2-get-color-from-palette(mat.$m2-green-palette, 800);
      color: mat.m2-get-contrast-color-from-palette(mat.$m2-green-palette, 800);
   }

   .highlight {
      background-color: $dark-highlight;
   }

   .primary-text {
      color: mat.m2-get-color-from-palette($light-primary, 300);
   }

   .accent-text {
      color: mat.m2-get-color-from-palette($dark-accent, 500);
   }

   .warn-text {
      color: mat.m2-get-color-from-palette(mat.$m2-red-palette, 300);
   }

   .striped {
      background-image: repeating-linear-gradient(-45deg, transparent 0 3px, #666 3px 6px);
   }

   .score-1 {
      border-left: 6px solid mat.m2-get-color-from-palette(mat.$m2-red-palette, 700);
   }

   .score-3 {
      border-left: 6px solid mat.m2-get-color-from-palette(mat.$m2-yellow-palette, 600);
   }

   .score-5 {
      border-left: 6px solid mat.m2-get-color-from-palette(mat.$m2-green-palette, 700);
   }
}

.query-highlight:hover:not(.selected-query) {
   @extend .accent-text;
}

@keyframes blinker {
   50% {
      background-color: mat.m2-get-color-from-palette($light-accent, 400);
   }
}

.multi-line {
   white-space: pre-line;
}

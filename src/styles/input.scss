.native-input-table {
   input::-webkit-outer-spin-button,
   input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
   }

   input[type=number] {
      -moz-appearance: textfield;
   }

   .row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
   }

   .row.header {
      font-weight: bold;
      font-style: italic;

      span:first-child {
         margin-right: 20px;
      }

      span:last-child {
         width: 90px;
      }
   }

   .row.data {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;

      span {
         margin-right: 20px;
      }

      input {
         width: 90px;
         border: 1px solid;
         border-bottom: none;

         &:focus {
            outline: none;
         }
      }

      &:nth-child(2) {
         input {
            border-top-right-radius: 4px;
            border-top-left-radius: 4px;
         }
      }

      &:last-child {
         input {
            border-bottom: 1px solid;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;
         }
      }
   }
}

.input-with-button {
   display: inline-flex;
   align-items: center;
}

.mat-mdc-form-field {
   margin-top: 4px;
   margin-bottom: 4px;
}

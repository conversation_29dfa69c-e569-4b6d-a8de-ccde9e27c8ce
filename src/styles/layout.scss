// Grid
.two-column-grid {
   display: grid;
   grid-template-columns: 50% 50%;
}

.inline-two-column-grid {
   display: inline-grid;
   grid-template-columns: 50% 50%;
}

.four-column-grid {
   display: grid;
   grid-template-columns: 25% 25% 25% 25%;
}

.colspan-1 {
   grid-column: span 1;
}

.colspan-2 {
   grid-column: span 2;
}

.colspan-3 {
   grid-column: span 3;
}

// Flexbox
.center-flex {
   display: flex;
   justify-content: center;
   align-items: center;
}

.flex-center-column {
   display: flex;
   flex-direction: column;
   align-items: center;
}

.flex-row {
   display: flex;
   flex-direction: row;
}

.flex-center-row {
   display: flex;
   flex-direction: row;
   justify-content: center;
}

.end-row {
   @extend .flex-row;
   justify-content: flex-end;
}

.apart-row {
   @extend .flex-row;
   justify-content: space-between;
}

.flex-column {
   display: flex;
   flex-direction: column;
}

.btn-row > * {
   margin-right: 16px;
}

.jc-space-between {
   justify-content: space-between;
}

// Main views
.main-view-table {
   margin: auto;
   width: 80vw;

   table {
      width: 100%;
   }

   .edit-column {
      min-width: 37px;
      width: 10%;
   }

   .two-action-column {
      min-width: 37px;
      width: 15%;
   }
}

.main-view-header {
   margin: 20px;
   display: flex;
   justify-content: space-between;
   align-items: center;

   .search {
      width: 40vw;
   }
}

// Forms
.wide-form-field {
   width: 400px;
}

// Query
.q-header {
   height: 3em !important;
}

.q-row {
   height: 2em !important;
}

// Misc
.align-icons {
   margin-bottom: 12px;
}

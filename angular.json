{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"front-end": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"allowedCommonJsDependencies": ["file-saver", "exceljs"], "outputPath": "dist/front-end", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "development": {}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {}, "configurations": {"production": {"buildTarget": "front-end:build:production"}, "development": {"buildTarget": "front-end:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "front-end:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "front-end:serve", "watch": true, "headless": false}, "configurations": {"production": {"devServerTarget": "front-end:serve:production"}}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "front-end:serve"}, "configurations": {"production": {"devServerTarget": "front-end:serve:production"}}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false}}, "ct": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "front-end:serve", "watch": true, "headless": false, "testingType": "component"}, "configurations": {"development": {"devServerTarget": "front-end:serve:development"}}}}}}, "cli": {"analytics": false}}
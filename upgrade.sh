#! /bin/bash

set -eu

VERSIONING_SERVICE='src/app/services/versioning.service.ts'
DEPLOYMENT_YAML='deployment/kubernetes/ui.yaml'
VERSION_ASSET='src/assets/version'

if [[ ! $1 =~ ^[0-9]\.[0-9]+\.[0-9]+$ ]]; then
   echo Wrong version format
   exit 1
fi

if [[ `uname` == 'Darwin' ]]; then
   sed -i '' "s#currentVersion = '.*'#currentVersion = '$1'#" ${VERSIONING_SERVICE}
   sed -i '' "s#image: ghcr.io/overview-pms/front-end:.*#image: ghcr.io/overview-pms/front-end:$1#" ${DEPLOYMENT_YAML}
else
   sed -i "s#currentVersion = '.*'#currentVersion = '$1'#" ${VERSIONING_SERVICE}
   sed -i "s#image: ghcr.io/overview-pms/front-end:.*#image: ghcr.io/overview-pms/front-end:$1#" ${DEPLOYMENT_YAML}
fi

echo -n "$1" > ${VERSION_ASSET}

git add .
git commit -m "Front-end $1"
git tag $1
git push
git push --tags

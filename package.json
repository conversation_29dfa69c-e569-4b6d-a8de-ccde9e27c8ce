{"name": "front-end", "version": "0.6.2", "scripts": {"ng": "ng", "start": "ng serve -o --no-hmr", "build": "ng build", "build-production": "ng build", "build-development": "ng build -c development", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.2.11", "@angular/material": "^19.0.0", "@angular/material-luxon-adapter": "^19.2.16", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.2.11", "@angular/router": "^19.2.11", "@stomp/stompjs": "^7.1.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "luxon": "^3.6.1", "ngx-google-analytics": "^14.0.1", "ngx-mat-select-search": "^8.0.1", "normalize.css": "^8.0.1", "rxjs": "^7.8.2", "tslib": "^2.3.1", "zone.js": "0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular-eslint/builder": "^19.4.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/schematics": "^19.4.0", "@angular-eslint/template-parser": "^19.4.0", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.11", "@angular/language-service": "^19.2.11", "@cypress/schematic": "^3.0.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "^5.1.8", "@types/jasminewd2": "^2.0.13", "@types/luxon": "^3.6.2", "@types/node": "^22.15.21", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "cypress": "^14.4.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.17", "eslint-plugin-prefer-arrow": "1.2.3", "eslint-plugin-rxjs": "^5.0.3", "jasmine-core": "~5.6.0", "jasmine-spec-reporter": "~7.0.0", "karma": "^6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "rxjs-spy": "^8.0.2", "ts-node": "~10.9.1", "typescript": "5.8.3"}}
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
   worker_connections  1024;
}

http {
   include       /etc/nginx/mime.types;
   default_type  application/octet-stream;

   log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                     '$status $body_bytes_sent "$http_referer" '
                     '"$http_user_agent" "$http_x_forwarded_for"';

   access_log  /var/log/nginx/access.log  main;

   sendfile        on;

   keepalive_timeout  65;

   server {
      listen 4200;
      root /var/www/;

      location / {
         try_files $uri $uri/ /index.html;
      }

      location = /index.html {
         internal;
         add_header Cache-Control 'no-store';
      }

      location = /assets/version {
         add_header Cache-Control 'no-store';
      }
   }
}

apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
data:
  nginx.conf: |
    worker_processes  1;

    error_log  /var/log/nginx/error.log warn;
    pid        /var/run/nginx.pid;

    events {
       worker_connections  1024;
    }

    http {
       include       /etc/nginx/mime.types;
       default_type  application/octet-stream;

       log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                         '$status $body_bytes_sent "$http_referer" '
                         '"$http_user_agent" "$http_x_forwarded_for"';

       access_log  /var/log/nginx/access.log  main;

       sendfile        on;

       keepalive_timeout  65;

       server {
          listen 4200;
          root /var/www/;

          location / {
             try_files $uri $uri/ /index.html;
          }

          location = /index.html {
             internal;
             add_header Cache-Control 'no-store';
          }

          location = /assets/version {
             add_header Cache-Control 'no-store';
          }
       }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ui-deployment
spec:
  selector:
    matchLabels:
      app: ui-app
  template:
    metadata:
      labels:
        app: ui-app
    spec:
      automountServiceAccountToken: false
      securityContext:
        runAsUser: 65534
        runAsGroup: 3000
      imagePullSecrets:
        - name: ghcr-secret
      containers:
        - name: ui
          image: ghcr.io/overview-pms/front-end:2.19.5
          ports:
            - name: ui-port
              containerPort: 4200
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
          volumeMounts:
            - name: config-volume
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
            - mountPath: /var/cache/nginx
              name: nginx-cache-volume
            - mountPath: /var/run
              name: nginx-pid-volume
      volumes:
        - name: config-volume
          configMap:
            name: nginx-config
        - name: nginx-cache-volume
          emptyDir: { }
        - name: nginx-pid-volume
          emptyDir: { }
---
apiVersion: v1
kind: Service
metadata:
  name: ui-service
spec:
  selector:
    app: ui-app
  ports:
    - name: ui-port
      port: 4200
      targetPort: ui-port
  type: ClusterIP

apiVersion: apps/v1
kind: Deployment
metadata:
  name: ui-maintenance-deployment
spec:
  selector:
    matchLabels:
      app: ui-maintenance-app
  template:
    metadata:
      labels:
        app: ui-maintenance-app
    spec:
      automountServiceAccountToken: false
      securityContext:
        runAsUser: 65534
        runAsGroup: 3000
      imagePullSecrets:
        - name: docker-creds
      containers:
        - name: ui
          image: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/overview:front-end_maintenance
          imagePullPolicy: Always
          ports:
            - name: ui-port
              containerPort: 4200
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - all
          volumeMounts:
            - mountPath: /var/cache/nginx
              name: nginx-cache-volume
            - mountPath: /var/run
              name: nginx-pid-volume
      volumes:
        - name: nginx-cache-volume
          emptyDir: { }
        - name: nginx-pid-volume
          emptyDir: { }
---
apiVersion: v1
kind: Service
metadata:
  name: ui-service
spec:
  selector:
    app: ui-maintenance-app
  ports:
    - name: ui-port
      port: 4200
      targetPort: ui-port
  type: ClusterIP

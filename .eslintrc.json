{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json", "e2e/tsconfig.json"], "createDefaultProgram": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates"], "plugins": ["import", "jsdoc", "prefer-arrow", "rxjs"], "rules": {"no-shadow": "off", "no-trailing-spaces": "off", "@typescript-eslint/no-shadow": ["error", {"allow": ["_"]}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "rxjs/no-async-subscribe": "error", "rxjs/no-ignored-notifier": "error", "rxjs/no-ignored-observable": "error", "rxjs/no-ignored-replay-buffer": "error", "rxjs/no-nested-subscribe": "error", "rxjs/no-unbound-methods": "error", "rxjs/no-unsafe-switchmap": "error", "rxjs/no-unsafe-takeuntil": "error", "rxjs/throw-error": "error", "@angular-eslint/prefer-standalone": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_+$", "varsIgnorePattern": "^_+$"}]}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {}}]}
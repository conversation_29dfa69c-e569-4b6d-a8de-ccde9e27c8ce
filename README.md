# Overview PMS: Front-end

This is the main repository for Overview's front-end. It contains everything necessary to
build and deploy it locally and in Docker.

## Build and run with <PERSON><PERSON>

```shell
docker-compose up --build -d 
```

## Remove the leftover containers

```shell
docker-compose down -v
```

## Engineering Process

Some good insights by <PERSON><PERSON> on the engineering process that companies should follow.
To be moved to the company's values and processes when we start hiring.

### 1. Make your requirements less dumb

Assign a person to each of those requirements.

### 2. Try to delete parts

If we try to add things just because we might need them at some point, we will end up with
a mess.

### 3. Simplify or optimize

The most common error of a smart engineer is to optimize a thing that should not exist.

### 4. Accelerate cycle time

Make sure that you worked on the first 3 things first.
"If you're digging your grave, don't dig it faster. Stop digging your grave."

### 5. Automate
